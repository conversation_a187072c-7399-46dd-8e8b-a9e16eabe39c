# PostgreSQL + Redis 双数据库架构设计

## 数据分离策略

### Redis 负责 (热数据/缓存层)
```
🔥 热数据存储:
├── 活跃 API 密钥缓存
├── 实时请求计数器
├── 会话和认证令牌
├── 系统配置缓存
├── 限流计数器
├── 实时统计数据
└── 消息队列

⚡ 特点:
- 毫秒级响应
- 高并发读写
- 自动过期
- 内存存储
```

### PostgreSQL 负责 (持久化层/分析层)
```
💾 持久化存储:
├── 用户账户和权限
├── 组织和租户信息
├── API 密钥完整记录
├── 历史请求日志
├── 审计日志
├── 计费和统计数据
├── 系统配置备份
└── 数据备份和归档

📊 特点:
- ACID 事务
- 复杂查询
- 数据完整性
- 长期存储
```

## 具体实现示例

### 1. API 密钥管理
```sql
-- PostgreSQL: 主存储
CREATE TABLE api_keys (
    id SERIAL PRIMARY KEY,
    key_value VARCHAR(700) NOT NULL,
    user_id INTEGER REFERENCES users(id),
    group_id INTEGER REFERENCES groups(id),
    status VARCHAR(50) DEFAULT 'active',
    quota_limit INTEGER,
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);

-- Redis: 缓存活跃密钥
SET api_key:sk-xxx123 '{"group_id":1,"status":"active","quota_remaining":1000}'
EXPIRE api_key:sk-xxx123 3600  # 1小时过期
```

### 2. 实时统计
```python
# Redis: 实时计数
def increment_request_count(api_key):
    redis.incr(f"requests:daily:{api_key}:{today}")
    redis.incr(f"requests:hourly:{api_key}:{current_hour}")
    redis.expire(f"requests:daily:{api_key}:{today}", 86400)

# PostgreSQL: 历史数据
def save_request_log(request_data):
    db.execute("""
        INSERT INTO request_logs 
        (api_key, endpoint, response_time, status_code, timestamp)
        VALUES (%s, %s, %s, %s, %s)
    """, request_data)
```

### 3. 用户会话管理
```python
# Redis: 会话存储
def create_session(user_id, session_data):
    session_id = generate_session_id()
    redis.setex(f"session:{session_id}", 3600, json.dumps(session_data))
    return session_id

# PostgreSQL: 用户信息
def get_user_permissions(user_id):
    return db.query("""
        SELECT p.permission_name 
        FROM users u
        JOIN user_roles ur ON u.id = ur.user_id
        JOIN role_permissions rp ON ur.role_id = rp.role_id
        JOIN permissions p ON rp.permission_id = p.id
        WHERE u.id = %s
    """, [user_id])
```

## 数据同步策略

### 1. 写入策略 (Write-Through)
```python
def update_api_key_status(key_id, status):
    # 1. 更新 PostgreSQL (主数据)
    db.execute("UPDATE api_keys SET status = %s WHERE id = %s", [status, key_id])
    
    # 2. 更新 Redis 缓存
    key_data = get_api_key_from_db(key_id)
    redis.setex(f"api_key:{key_data['key_value']}", 3600, json.dumps(key_data))
    
    # 3. 清除相关缓存
    redis.delete(f"user_keys:{key_data['user_id']}")
```

### 2. 缓存失效策略
```python
def get_api_key_info(key_value):
    # 1. 先查 Redis
    cached = redis.get(f"api_key:{key_value}")
    if cached:
        return json.loads(cached)
    
    # 2. 查 PostgreSQL
    key_data = db.query("SELECT * FROM api_keys WHERE key_value = %s", [key_value])
    if key_data:
        # 3. 写入 Redis
        redis.setex(f"api_key:{key_value}", 3600, json.dumps(key_data))
        return key_data
    
    return None
```

## 监控和维护

### 1. 数据一致性检查
```python
def check_data_consistency():
    # 检查 Redis 和 PostgreSQL 数据是否一致
    pg_count = db.scalar("SELECT COUNT(*) FROM api_keys WHERE status = 'active'")
    redis_keys = redis.keys("api_key:*")
    
    if abs(pg_count - len(redis_keys)) > threshold:
        alert("Data inconsistency detected")
```

### 2. 性能监控
```python
def monitor_performance():
    # Redis 性能
    redis_info = redis.info()
    redis_memory = redis_info['used_memory_human']
    redis_ops = redis_info['instantaneous_ops_per_sec']
    
    # PostgreSQL 性能
    pg_stats = db.query("SELECT * FROM pg_stat_database WHERE datname = 'gpt_load'")
    
    return {
        'redis_memory': redis_memory,
        'redis_ops': redis_ops,
        'pg_connections': pg_stats['numbackends'],
        'pg_transactions': pg_stats['xact_commit']
    }
```
