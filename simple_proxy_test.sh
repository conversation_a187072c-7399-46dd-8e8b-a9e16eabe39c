#!/bin/bash

# 简化的代理测试脚本

echo "=== GPT-Load 代理功能测试 ==="
echo ""

# 1. 检查 hajimi-warp 容器
echo "🔍 检查 hajimi-warp 容器..."
if docker ps | grep -q hajimi-warp; then
    echo "  ✓ hajimi-warp 容器正在运行"
else
    echo "  ❌ hajimi-warp 容器未运行"
    exit 1
fi

# 2. 测试代理连通性
echo ""
echo "🔍 测试代理连通性..."
echo "  当前 IP (不使用代理):"
current_ip=$(curl -s https://api.ipify.org 2>/dev/null || echo "获取失败")
echo "    $current_ip"

echo "  代理 IP (使用 SOCKS5 代理):"
proxy_ip=$(curl -s --socks5 127.0.0.1:1080 https://api.ipify.org 2>/dev/null || echo "获取失败")
echo "    $proxy_ip"

if [ "$current_ip" != "$proxy_ip" ] && [ "$proxy_ip" != "获取失败" ]; then
    echo "  ✓ 代理工作正常，IP 地址已改变"
else
    echo "  ❌ 代理未生效"
    exit 1
fi

# 3. 检查 GPT-Load 服务
echo ""
echo "🔍 检查 GPT-Load 服务..."
if docker compose ps | grep -q gpt-load; then
    echo "  ✓ GPT-Load 服务正在运行"
else
    echo "  ❌ GPT-Load 服务未运行"
    exit 1
fi

# 4. 检查容器内代理环境变量
echo ""
echo "🔍 检查容器内代理配置..."
http_proxy=$(docker compose exec gpt-load printenv HTTP_PROXY 2>/dev/null || echo "未设置")
echo "  HTTP_PROXY: $http_proxy"

if [[ "$http_proxy" == *"socks5://host.docker.internal:1080"* ]]; then
    echo "  ✓ 代理环境变量配置正确"
else
    echo "  ❌ 代理环境变量配置错误"
fi

# 5. 测试容器内代理连接
echo ""
echo "🔍 测试容器内代理连接..."
container_ip=$(docker compose exec gpt-load timeout 10 curl -s --socks5 host.docker.internal:1080 https://api.ipify.org 2>/dev/null || echo "获取失败")
echo "  容器通过代理获取的 IP: $container_ip"

if [ "$container_ip" = "$proxy_ip" ]; then
    echo "  ✓ 容器内代理连接成功"
else
    echo "  ❌ 容器内代理连接失败"
fi

# 6. 测试 OpenAI API 访问
echo ""
echo "🔍 测试 OpenAI API 访问..."
openai_test=$(curl -s --socks5 127.0.0.1:1080 -w "%{http_code}" -o /dev/null https://api.openai.com/v1/models 2>/dev/null || echo "000")
echo "  OpenAI API 响应码: $openai_test"

if [ "$openai_test" = "401" ] || [ "$openai_test" = "200" ]; then
    echo "  ✓ 可以通过代理访问 OpenAI API"
else
    echo "  ❌ 无法通过代理访问 OpenAI API"
fi

# 7. 测试 GPT-Load 健康状态
echo ""
echo "🔍 测试 GPT-Load 健康状态..."
health_test=$(curl -s -w "%{http_code}" -o /dev/null http://localhost:3001/health 2>/dev/null || echo "000")
echo "  GPT-Load 健康检查响应码: $health_test"

if [ "$health_test" = "200" ]; then
    echo "  ✓ GPT-Load 服务健康"
else
    echo "  ❌ GPT-Load 服务异常"
fi

# 总结
echo ""
echo "📋 测试总结:"
echo "  hajimi-warp 状态: 运行中"
echo "  代理功能: $([ "$proxy_ip" != "获取失败" ] && [ "$current_ip" != "$proxy_ip" ] && echo "正常" || echo "异常")"
echo "  容器代理: $([ "$container_ip" = "$proxy_ip" ] && echo "正常" || echo "异常")"
echo "  OpenAI 访问: $([ "$openai_test" = "401" ] || [ "$openai_test" = "200" ] && echo "正常" || echo "异常")"
echo "  GPT-Load 健康: $([ "$health_test" = "200" ] && echo "正常" || echo "异常")"

echo ""
echo "💡 使用说明:"
echo "  管理界面: http://localhost:3001"
echo "  认证密钥: $(grep AUTH_KEY .env | cut -d'=' -f2)"
echo "  代理状态: 已启用 (hajimi-warp SOCKS5)"

echo ""
if [ "$proxy_ip" != "获取失败" ] && [ "$current_ip" != "$proxy_ip" ] && [ "$health_test" = "200" ]; then
    echo "✅ 代理集成测试通过！GPT-Load 现在通过代理访问外部服务"
else
    echo "⚠️  部分测试未通过，请检查配置"
fi
