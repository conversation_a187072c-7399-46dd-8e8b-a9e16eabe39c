#!/bin/bash

# GPT-Load SQLite to Redis Migration Script
# 数据迁移执行脚本

set -e

echo "=== GPT-Load 数据迁移工具 ==="
echo "将SQLite数据迁移到Redis"
echo ""

# 检查必要文件
if [ ! -f "docker-compose.yml" ]; then
    echo "❌ 错误: 未找到docker-compose.yml文件"
    exit 1
fi

# 检查 Docker Compose 命令
if command -v docker-compose &> /dev/null; then
    DOCKER_COMPOSE="docker-compose"
elif docker compose version &> /dev/null; then
    DOCKER_COMPOSE="docker compose"
else
    echo "❌ 错误: 未找到 docker-compose 或 docker compose 命令"
    exit 1
fi

echo "使用命令: $DOCKER_COMPOSE"

if [ ! -f "data/gpt-load.db" ]; then
    echo "❌ 错误: 未找到SQLite数据库文件 data/gpt-load.db"
    exit 1
fi

if [ ! -f "migrate_sqlite_to_redis.py" ]; then
    echo "❌ 错误: 未找到迁移脚本 migrate_sqlite_to_redis.py"
    exit 1
fi

# 创建Redis数据目录
mkdir -p data/redis

echo "📋 迁移前检查:"
echo "  ✓ docker-compose.yml 文件存在"
echo "  ✓ SQLite数据库文件存在"
echo "  ✓ 迁移脚本存在"
echo "  ✓ Redis数据目录已创建"
echo ""

# 检查当前服务状态
echo "🔍 检查当前服务状态..."
if $DOCKER_COMPOSE ps | grep -q "gpt-load.*Up"; then
    echo "⚠️  检测到gpt-load服务正在运行"
    read -p "是否停止服务进行迁移? (y/N): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        echo "🛑 停止gpt-load服务..."
        $DOCKER_COMPOSE stop gpt-load
    else
        echo "❌ 迁移已取消"
        exit 1
    fi
fi

# 启动Redis服务
echo "🚀 启动Redis服务..."
$DOCKER_COMPOSE up -d redis

# 等待Redis启动
echo "⏳ 等待Redis服务启动..."
sleep 5

# 检查Redis是否正常运行
if ! $DOCKER_COMPOSE exec redis redis-cli ping > /dev/null 2>&1; then
    echo "❌ Redis服务启动失败"
    exit 1
fi
echo "  ✓ Redis服务运行正常"

# 运行迁移脚本
echo ""
echo "🔄 开始数据迁移..."
echo "注意: 迁移过程中请勿中断"
echo ""

# 使用Python容器运行迁移脚本
docker run --rm \
    --network "$(basename $(pwd))_default" \
    -v "$(pwd):/app" \
    -w /app \
    python:3.9-slim \
    bash -c "
        pip install redis > /dev/null 2>&1 && 
        python migrate_sqlite_to_redis.py
    "

if [ $? -eq 0 ]; then
    echo ""
    echo "🎉 数据迁移完成!"
    echo ""
    echo "📝 后续步骤:"
    echo "  1. 检查.env文件中的REDIS_DSN配置已启用"
    echo "  2. 启动完整服务: docker-compose up -d"
    echo "  3. 检查服务日志: docker-compose logs -f gpt-load"
    echo "  4. 验证数据迁移是否成功"
    echo ""
    
    # 检查.env文件中的Redis配置
    if grep -q "^REDIS_DSN=redis://redis:6379/0" .env; then
        echo "  ✓ Redis配置已启用"
    else
        echo "  ⚠️  请检查.env文件中的REDIS_DSN配置"
    fi
    
    # 询问是否立即启动服务
    read -p "是否现在启动完整服务? (y/N): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        echo "🚀 启动完整服务..."
        $DOCKER_COMPOSE up -d
        echo ""
        echo "📊 服务状态:"
        $DOCKER_COMPOSE ps
        echo ""
        echo "📋 查看日志: $DOCKER_COMPOSE logs -f gpt-load"
    fi
else
    echo ""
    echo "❌ 数据迁移失败"
    echo "请检查错误信息并重试"
    exit 1
fi
