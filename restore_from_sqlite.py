#!/usr/bin/env python3
"""
从 SQLite 恢复数据到 PostgreSQL + Redis
直接从原始 SQLite 数据库恢复所有数据
"""

import sqlite3
import redis
import psycopg2
import psycopg2.extras
import json
import sys
import os
from datetime import datetime
from typing import Dict, List, Any
import uuid

class SQLiteRestorer:
    def __init__(self, sqlite_path: str = './data/gpt-load.db'):
        """初始化恢复器"""
        
        self.sqlite_path = sqlite_path
        
        # Redis 连接
        self.redis_client = redis.Redis(host='redis', port=6379, db=0, decode_responses=True)
        
        # PostgreSQL 连接
        self.pg_conn = None
        self.pg_cursor = None
        
        # PostgreSQL 连接参数
        self.pg_params = {
            'host': 'postgres',
            'port': 5432,
            'database': 'gpt-load',
            'user': 'postgres',
            'password': '123456'
        }
        
        self.connect_databases()

    def connect_databases(self):
        """连接数据库"""
        # 检查 SQLite 文件
        if not os.path.exists(self.sqlite_path):
            print(f"✗ SQLite 文件不存在: {self.sqlite_path}")
            sys.exit(1)
        print(f"✓ SQLite 文件存在: {self.sqlite_path}")
        
        # 连接 Redis
        try:
            self.redis_client.ping()
            print(f"✓ Redis 连接成功")
        except Exception as e:
            print(f"✗ Redis 连接失败: {e}")
            sys.exit(1)
        
        # 连接 PostgreSQL
        try:
            self.pg_conn = psycopg2.connect(**self.pg_params)
            self.pg_cursor = self.pg_conn.cursor(cursor_factory=psycopg2.extras.RealDictCursor)
            print(f"✓ PostgreSQL 连接成功")
        except Exception as e:
            print(f"✗ PostgreSQL 连接失败: {e}")
            sys.exit(1)

    def restore_system_settings(self):
        """恢复系统设置"""
        print("\n=== 恢复系统设置 ===")
        
        # 从 SQLite 读取
        sqlite_conn = sqlite3.connect(self.sqlite_path)
        sqlite_conn.row_factory = sqlite3.Row
        cursor = sqlite_conn.cursor()
        
        cursor.execute("SELECT * FROM system_settings")
        settings = cursor.fetchall()
        
        migrated_count = 0
        for setting in settings:
            # 插入到 PostgreSQL
            self.pg_cursor.execute("""
                INSERT INTO system_settings (setting_key, setting_value, description, created_at, updated_at)
                VALUES (%s, %s, %s, %s, %s)
                ON CONFLICT (setting_key) DO UPDATE SET
                    setting_value = EXCLUDED.setting_value,
                    description = EXCLUDED.description,
                    updated_at = EXCLUDED.updated_at
            """, (
                setting['setting_key'],
                setting['setting_value'],
                setting['description'] or '',
                setting['created_at'] or datetime.now(),
                setting['updated_at'] or datetime.now()
            ))
            
            # 缓存到 Redis
            self.redis_client.setex(
                f"setting_cache:{setting['setting_key']}", 
                1800,  # 30分钟过期
                setting['setting_value']
            )
            
            migrated_count += 1
            print(f"  ✓ {setting['setting_key']}: {setting['setting_value']}")
        
        sqlite_conn.close()
        self.pg_conn.commit()
        print(f"✓ 系统设置恢复完成: {migrated_count} 条记录")

    def restore_groups(self):
        """恢复组配置"""
        print("\n=== 恢复组配置 ===")
        
        # 从 SQLite 读取
        sqlite_conn = sqlite3.connect(self.sqlite_path)
        sqlite_conn.row_factory = sqlite3.Row
        cursor = sqlite_conn.cursor()
        
        cursor.execute("SELECT * FROM groups")
        groups = cursor.fetchall()
        
        migrated_count = 0
        for group in groups:
            # 插入到 PostgreSQL
            self.pg_cursor.execute("""
                INSERT INTO groups 
                (id, name, display_name, proxy_keys, description, upstreams, 
                 validation_endpoint, channel_type, sort, test_model, param_overrides, 
                 config, last_validated_at, created_at, updated_at)
                VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                ON CONFLICT (id) DO UPDATE SET
                    name = EXCLUDED.name,
                    display_name = EXCLUDED.display_name,
                    updated_at = EXCLUDED.updated_at
            """, (
                group['id'],
                group['name'],
                group['display_name'] or '',
                group['proxy_keys'] or '',
                group['description'] or '',
                group['upstreams'] or '[]',
                group['validation_endpoint'] or '',
                group['channel_type'] or 'openai',
                group['sort'] or 0,
                group['test_model'] or 'gpt-3.5-turbo',
                group['param_overrides'] or '{}',
                group['config'] or '{}',
                group['last_validated_at'],
                group['created_at'] or datetime.now(),
                group['updated_at'] or datetime.now()
            ))
            
            migrated_count += 1
            print(f"  ✓ {group['name']} (ID: {group['id']})")
        
        sqlite_conn.close()
        self.pg_conn.commit()
        print(f"✓ 组配置恢复完成: {migrated_count} 条记录")

    def restore_api_keys(self):
        """恢复 API 密钥"""
        print("\n=== 恢复 API 密钥 ===")
        
        # 从 SQLite 读取
        sqlite_conn = sqlite3.connect(self.sqlite_path)
        sqlite_conn.row_factory = sqlite3.Row
        cursor = sqlite_conn.cursor()
        
        cursor.execute("SELECT COUNT(*) FROM api_keys")
        total_count = cursor.fetchone()[0]
        print(f"  总计 {total_count} 个 API 密钥需要恢复")
        
        # 分批处理
        batch_size = 1000
        offset = 0
        migrated_count = 0
        
        while offset < total_count:
            cursor.execute(f"SELECT * FROM api_keys LIMIT {batch_size} OFFSET {offset}")
            api_keys = cursor.fetchall()
            
            if not api_keys:
                break
            
            for api_key in api_keys:
                # 插入到 PostgreSQL
                self.pg_cursor.execute("""
                    INSERT INTO api_keys 
                    (id, key_value, group_id, status, request_count, 
                     failure_count, last_used_at, created_at, updated_at)
                    VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s)
                    ON CONFLICT (id) DO UPDATE SET
                        status = EXCLUDED.status,
                        request_count = EXCLUDED.request_count,
                        failure_count = EXCLUDED.failure_count,
                        last_used_at = EXCLUDED.last_used_at,
                        updated_at = EXCLUDED.updated_at
                """, (
                    api_key['id'],
                    api_key['key_value'],
                    api_key['group_id'],
                    api_key['status'] or 'active',
                    api_key['request_count'] or 0,
                    api_key['failure_count'] or 0,
                    api_key['last_used_at'],
                    api_key['created_at'] or datetime.now(),
                    api_key['updated_at'] or datetime.now()
                ))
                
                # 缓存活跃的密钥到 Redis
                if api_key['status'] == 'active':
                    cache_data = {
                        'id': api_key['id'],
                        'group_id': api_key['group_id'],
                        'status': api_key['status'],
                        'request_count': api_key['request_count'] or 0
                    }
                    self.redis_client.setex(
                        f"api_key_cache:{api_key['key_value']}", 
                        3600,  # 1小时过期
                        json.dumps(cache_data)
                    )
                
                migrated_count += 1
            
            # 批量提交
            self.pg_conn.commit()
            offset += batch_size
            print(f"  ✓ 已恢复 {migrated_count} 个 API 密钥...")
        
        sqlite_conn.close()
        print(f"✓ API 密钥恢复完成: {migrated_count} 条记录")

    def restore_request_logs(self):
        """恢复请求日志"""
        print("\n=== 恢复请求日志 ===")
        
        # 从 SQLite 读取
        sqlite_conn = sqlite3.connect(self.sqlite_path)
        sqlite_conn.row_factory = sqlite3.Row
        cursor = sqlite_conn.cursor()
        
        cursor.execute("SELECT * FROM request_logs ORDER BY timestamp DESC LIMIT 10000")  # 只恢复最近的1万条日志
        logs = cursor.fetchall()
        
        migrated_count = 0
        for log in logs:
            # 生成 UUID
            log_id = str(uuid.uuid4())
            
            # 插入到 PostgreSQL
            self.pg_cursor.execute("""
                INSERT INTO request_logs 
                (id, timestamp, group_id, group_name, key_value, 
                 is_success, source_ip, status_code, request_path, duration, 
                 error_message, user_agent, retries, upstream_addr, is_stream)
                VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                ON CONFLICT (id) DO NOTHING
            """, (
                log_id,
                log['timestamp'] or datetime.now(),
                log['group_id'] or 1,
                log['group_name'] or '',
                log['key_value'] or '',
                bool(log['is_success']),
                log['source_ip'],
                log['status_code'] or 0,
                log['request_path'] or '',
                log['duration'] or 0,
                log['error_message'] or '',
                log['user_agent'] or '',
                log['retries'] or 0,
                log['upstream_addr'] or '',
                bool(log['is_stream'])
            ))
            
            migrated_count += 1
        
        sqlite_conn.close()
        self.pg_conn.commit()
        print(f"✓ 请求日志恢复完成: {migrated_count} 条记录")

    def restore_hourly_stats(self):
        """恢复小时统计数据"""
        print("\n=== 恢复小时统计数据 ===")
        
        # 从 SQLite 读取
        sqlite_conn = sqlite3.connect(self.sqlite_path)
        sqlite_conn.row_factory = sqlite3.Row
        cursor = sqlite_conn.cursor()
        
        cursor.execute("SELECT * FROM group_hourly_stats")
        stats = cursor.fetchall()
        
        migrated_count = 0
        for stat in stats:
            # 插入到 PostgreSQL
            self.pg_cursor.execute("""
                INSERT INTO group_hourly_stats 
                (time, group_id, success_count, failure_count, created_at, updated_at)
                VALUES (%s, %s, %s, %s, %s, %s)
                ON CONFLICT (time, group_id) DO UPDATE SET
                    success_count = EXCLUDED.success_count,
                    failure_count = EXCLUDED.failure_count,
                    updated_at = EXCLUDED.updated_at
            """, (
                stat['time'] or datetime.now(),
                stat['group_id'] or 1,
                stat['success_count'] or 0,
                stat['failure_count'] or 0,
                stat['created_at'] or datetime.now(),
                stat['updated_at'] or datetime.now()
            ))
            
            migrated_count += 1
        
        sqlite_conn.close()
        self.pg_conn.commit()
        print(f"✓ 小时统计数据恢复完成: {migrated_count} 条记录")

    def create_restore_metadata(self):
        """创建恢复元数据"""
        print("\n=== 创建恢复元数据 ===")
        
        # 在 PostgreSQL 中记录恢复信息
        self.pg_cursor.execute("""
            INSERT INTO system_settings (setting_key, setting_value, description)
            VALUES (%s, %s, %s)
            ON CONFLICT (setting_key) DO UPDATE SET
                setting_value = EXCLUDED.setting_value,
                updated_at = NOW()
        """, (
            'data_restored_at',
            datetime.now().isoformat(),
            'Data restoration from SQLite completion timestamp'
        ))
        
        self.pg_conn.commit()
        print("✓ 恢复元数据已创建")

    def run_restore(self):
        """执行完整的数据恢复"""
        print("开始从 SQLite 恢复数据到 PostgreSQL + Redis...")
        print(f"源数据库: {self.sqlite_path}")
        
        try:
            # 清理现有数据
            print("\n清理现有数据...")
            tables = ['system_settings', 'groups', 'api_keys', 'request_logs', 'group_hourly_stats']
            for table in tables:
                self.pg_cursor.execute(f"TRUNCATE TABLE {table} RESTART IDENTITY CASCADE")
            
            # 清理 Redis 缓存
            keys = self.redis_client.keys("*_cache:*")
            if keys:
                self.redis_client.delete(*keys)
            
            self.pg_conn.commit()
            print("✓ 数据清理完成")
            
            # 执行恢复
            self.restore_system_settings()
            self.restore_groups()
            self.restore_api_keys()
            self.restore_request_logs()
            self.restore_hourly_stats()
            self.create_restore_metadata()
            
            print("\n🎉 数据恢复完成!")
            print("\n📊 恢复统计:")
            
            # 显示恢复统计
            for table in ['system_settings', 'groups', 'api_keys', 'request_logs', 'group_hourly_stats']:
                self.pg_cursor.execute(f"SELECT COUNT(*) FROM {table}")
                count = self.pg_cursor.fetchone()[0]
                print(f"  {table}: {count} 条记录")
            
        except Exception as e:
            print(f"\n❌ 恢复过程中出现错误: {e}")
            self.pg_conn.rollback()
            raise
        finally:
            if self.pg_conn:
                self.pg_conn.close()

if __name__ == "__main__":
    # 执行恢复
    restorer = SQLiteRestorer()
    restorer.run_restore()
