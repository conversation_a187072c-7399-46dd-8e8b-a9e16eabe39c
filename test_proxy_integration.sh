#!/bin/bash

# GPT-Load 代理集成测试脚本
# 测试 hajimi-warp SOCKS5 代理与 GPT-Load 的集成

set -e

echo "=== GPT-Load 代理集成测试 ==="
echo ""

# 检查 hajimi-warp 容器状态
echo "🔍 检查 hajimi-warp 容器状态..."
if docker ps | grep -q "hajimi-warp.*Up"; then
    echo "  ✓ hajimi-warp 容器正在运行"
    docker ps --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}" | grep hajimi-warp

    # 检查健康状态
    health_status=$(docker inspect hajimi-warp --format='{{.State.Health.Status}}' 2>/dev/null || echo "unknown")
    echo "  健康状态: $health_status"
else
    echo "  ❌ hajimi-warp 容器未正常运行"
    echo "  请检查容器状态: docker ps | grep hajimi-warp"
    exit 1
fi

echo ""

# 检查 GPT-Load 服务状态
echo "🔍 检查 GPT-Load 服务状态..."
if docker compose ps | grep -q "gpt-load.*Up"; then
    echo "  ✓ GPT-Load 服务正在运行"
    docker compose ps --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}"
else
    echo "  ❌ GPT-Load 服务未正常运行"
    echo "  请检查服务状态: docker compose ps"
    exit 1
fi

echo ""

# 测试宿主机代理连接
echo "🔍 测试宿主机代理连接..."
if command -v curl >/dev/null 2>&1; then
    echo "  测试不使用代理的 IP:"
    current_ip=$(timeout 10 curl -s https://api.ipify.org 2>/dev/null || echo "无法获取")
    echo "    当前 IP: $current_ip"
    
    echo ""
    echo "  测试使用 SOCKS5 代理的 IP:"
    proxy_ip=$(timeout 10 curl -s --socks5 127.0.0.1:1080 https://api.ipify.org 2>/dev/null || echo "无法获取")
    echo "    代理 IP: $proxy_ip"
    
    if [ "$current_ip" != "$proxy_ip" ] && [ "$proxy_ip" != "无法获取" ]; then
        echo "    ✓ 代理工作正常，IP 地址已改变"
    else
        echo "    ❌ 代理可能未生效或连接失败"
        exit 1
    fi
else
    echo "  ⚠️  curl 命令不可用，跳过宿主机代理测试"
fi

echo ""

# 测试容器内代理环境变量
echo "🔍 检查容器内代理环境变量..."
if docker compose ps | grep -q "gpt-load.*Up"; then
    echo "  容器内代理配置:"
    
    http_proxy=$(docker compose exec gpt-load printenv HTTP_PROXY 2>/dev/null || echo "未设置")
    https_proxy=$(docker compose exec gpt-load printenv HTTPS_PROXY 2>/dev/null || echo "未设置")
    no_proxy=$(docker compose exec gpt-load printenv NO_PROXY 2>/dev/null || echo "未设置")
    
    echo "    HTTP_PROXY: $http_proxy"
    echo "    HTTPS_PROXY: $https_proxy"
    echo "    NO_PROXY: $no_proxy"
    
    if [[ "$http_proxy" == *"socks5://host.docker.internal:1080"* ]]; then
        echo "    ✓ 代理环境变量配置正确"
    else
        echo "    ❌ 代理环境变量配置错误"
        exit 1
    fi
else
    echo "  ❌ GPT-Load 容器未运行"
    exit 1
fi

echo ""

# 测试容器内网络连接
echo "🔍 测试容器内网络连接..."
echo "  测试容器内是否能解析 host.docker.internal:"
if docker compose exec gpt-load nslookup host.docker.internal >/dev/null 2>&1; then
    echo "    ✓ host.docker.internal 解析成功"
else
    echo "    ❌ host.docker.internal 解析失败"
    echo "    检查 extra_hosts 配置"
fi

echo ""

# 测试容器内代理连接
echo "🔍 测试容器内代理连接..."
echo "  测试从容器内通过代理访问外网..."

# 测试容器内是否能通过代理访问外网
container_proxy_test=$(docker compose exec gpt-load timeout 10 curl -s --socks5 host.docker.internal:1080 https://api.ipify.org 2>/dev/null || echo "失败")

if [ "$container_proxy_test" != "失败" ]; then
    echo "    ✓ 容器内代理连接成功"
    echo "    容器通过代理获取的 IP: $container_proxy_test"
    
    # 验证 IP 是否与宿主机代理 IP 一致
    if [ "$container_proxy_test" = "$proxy_ip" ]; then
        echo "    ✓ 容器代理 IP 与宿主机代理 IP 一致"
    else
        echo "    ⚠️  容器代理 IP 与宿主机代理 IP 不一致"
    fi
else
    echo "    ❌ 容器内代理连接失败"
    echo "    请检查网络配置和防火墙设置"
fi

echo ""

# 测试 OpenAI API 访问
echo "🔍 测试通过代理访问 AI 服务..."
echo "  测试访问 OpenAI API (通过代理):"

# 从宿主机测试
openai_response_host=$(timeout 10 curl -s --socks5 127.0.0.1:1080 -w "%{http_code}" -o /dev/null https://api.openai.com/v1/models 2>/dev/null || echo "000")
if [ "$openai_response_host" = "401" ] || [ "$openai_response_host" = "200" ]; then
    echo "    ✓ 宿主机通过代理可访问 OpenAI API (HTTP $openai_response_host)"
else
    echo "    ❌ 宿主机通过代理访问 OpenAI API 失败 (HTTP $openai_response_host)"
fi

# 从容器内测试
openai_response_container=$(docker compose exec gpt-load timeout 10 curl -s --socks5 host.docker.internal:1080 -w "%{http_code}" -o /dev/null https://api.openai.com/v1/models 2>/dev/null || echo "000")
if [ "$openai_response_container" = "401" ] || [ "$openai_response_container" = "200" ]; then
    echo "    ✓ 容器内通过代理可访问 OpenAI API (HTTP $openai_response_container)"
else
    echo "    ❌ 容器内通过代理访问 OpenAI API 失败 (HTTP $openai_response_container)"
fi

echo ""

# 测试 GPT-Load 服务健康状态
echo "🔍 测试 GPT-Load 服务健康状态..."
health_response=$(timeout 10 curl -s -w "%{http_code}" -o /dev/null http://localhost:3001/health 2>/dev/null || echo "000")
if [ "$health_response" = "200" ]; then
    echo "  ✓ GPT-Load 健康检查通过 (HTTP $health_response)"
    
    # 获取健康状态详情
    health_info=$(timeout 5 curl -s http://localhost:3001/health 2>/dev/null || echo "无法获取详情")
    echo "  健康状态: $health_info"
else
    echo "  ❌ GPT-Load 健康检查失败 (HTTP $health_response)"
fi

echo ""

# 检查应用日志中的代理相关信息
echo "🔍 检查应用日志..."
echo "  查看最近的应用日志 (代理相关):"
docker compose logs --tail=20 gpt-load | grep -i -E "(proxy|socks|connect)" || echo "  没有发现代理相关日志"

echo ""

# 显示配置总结
echo "📋 当前代理配置总结:"
echo "  代理类型: SOCKS5"
echo "  代理地址: hajimi-warp (127.0.0.1:1080)"
echo "  容器访问: host.docker.internal:1080"
echo "  当前 IP: $current_ip"
echo "  代理 IP: $proxy_ip"
echo "  GPT-Load 状态: $([ "$health_response" = "200" ] && echo "健康" || echo "异常")"

echo ""

# 提供使用建议
echo "💡 使用建议:"
echo "  1. 代理已启用，GPT-Load 将通过 hajimi-warp 访问上游 AI 服务"
echo "  2. 管理界面: http://localhost:3001"
echo "  3. 认证密钥: $(grep AUTH_KEY .env | cut -d'=' -f2 || echo '未设置')"
echo "  4. 如遇问题，查看日志: docker compose logs -f gpt-load"
echo "  5. 重启服务: docker compose restart gpt-load"

echo ""

# 最终状态判断
if [ "$proxy_ip" != "无法获取" ] && [ "$proxy_ip" != "$current_ip" ] && [ "$health_response" = "200" ]; then
    echo "✅ 代理集成测试完成 - 所有测试通过！"
    echo "🎉 GPT-Load 现在通过 hajimi-warp 代理访问外部服务"
    exit 0
else
    echo "⚠️  代理集成测试完成 - 部分测试未通过"
    echo "请检查上述错误信息并进行相应调整"
    exit 1
fi
