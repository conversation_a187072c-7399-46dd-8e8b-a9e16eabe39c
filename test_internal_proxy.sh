#!/bin/bash

# GPT-Load 内部代理测试脚本
# 测试内部 warp 容器的代理功能

echo "=== GPT-Load 内部代理测试 ==="
echo ""

# 1. 检查所有服务状态
echo "🔍 检查服务状态..."
echo "服务列表:"
docker compose ps --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}"

echo ""

# 2. 检查内部 warp 容器
echo "🔍 检查内部 warp 容器..."
if docker compose ps | grep -q "gpt-load-warp.*Up.*healthy"; then
    echo "  ✓ 内部 warp 容器运行正常"
    
    # 检查 WARP 连接状态
    warp_status=$(docker compose exec warp warp-cli status 2>/dev/null || echo "无法获取状态")
    echo "  WARP 状态: $warp_status"
    
    # 检查网络接口
    warp_ip=$(docker compose exec warp ip addr show CloudflareWARP | grep "inet " | awk '{print $2}' | cut -d'/' -f1 2>/dev/null || echo "未连接")
    echo "  WARP IP: $warp_ip"
else
    echo "  ❌ 内部 warp 容器未正常运行"
    exit 1
fi

echo ""

# 3. 测试网络连通性
echo "🔍 测试网络连通性..."

# 测试 GPT-Load 到 warp 的连接
echo "  GPT-Load 到 warp 容器的网络连接:"
if docker compose exec gpt-load nslookup warp >/dev/null 2>&1; then
    warp_internal_ip=$(docker compose exec gpt-load nslookup warp | grep "Address:" | tail -1 | awk '{print $2}')
    echo "    ✓ 可以解析 warp 容器: $warp_internal_ip"
else
    echo "    ❌ 无法解析 warp 容器"
    exit 1
fi

echo ""

# 4. 检查代理环境变量
echo "🔍 检查代理环境变量..."
http_proxy=$(docker compose exec gpt-load printenv HTTP_PROXY 2>/dev/null || echo "未设置")
https_proxy=$(docker compose exec gpt-load printenv HTTPS_PROXY 2>/dev/null || echo "未设置")
no_proxy=$(docker compose exec gpt-load printenv NO_PROXY 2>/dev/null || echo "未设置")

echo "  HTTP_PROXY: $http_proxy"
echo "  HTTPS_PROXY: $https_proxy"
echo "  NO_PROXY: $no_proxy"

if [[ "$http_proxy" == "socks5://warp:1080" ]]; then
    echo "  ✓ 代理环境变量配置正确"
else
    echo "  ❌ 代理环境变量配置错误"
fi

echo ""

# 5. 测试宿主机代理连接
echo "🔍 测试宿主机代理连接..."
echo "  当前 IP (不使用代理):"
current_ip=$(curl -s https://api.ipify.org 2>/dev/null || echo "获取失败")
echo "    $current_ip"

echo "  内部代理 IP (通过 localhost:1081):"
internal_proxy_ip=$(curl -s --connect-timeout 10 --socks5 127.0.0.1:1081 https://api.ipify.org 2>/dev/null || echo "获取失败")
echo "    $internal_proxy_ip"

if [ "$current_ip" != "$internal_proxy_ip" ] && [ "$internal_proxy_ip" != "获取失败" ]; then
    echo "  ✓ 内部代理工作正常，IP 地址已改变"
else
    echo "  ❌ 内部代理未生效"
fi

echo ""

# 6. 测试 API 代理功能
echo "🔍 测试 API 代理功能..."

# 测试 Gemini API
echo "  测试 Gemini API 代理:"
gemini_response=$(curl -s -w "HTTP_CODE:%{http_code}" \
    -H "Authorization: Bearer test-key" \
    -H "Content-Type: application/json" \
    http://localhost:3001/proxy/gemini/v1beta/models/gemini-2.0-flash-lite:generateContent 2>/dev/null || echo "请求失败")

echo "    响应: $gemini_response"

if [[ "$gemini_response" == *"HTTP_CODE:401"* ]] || [[ "$gemini_response" == *"HTTP_CODE:200"* ]]; then
    echo "    ✓ Gemini API 代理正常 (通过内部 warp)"
else
    echo "    ❌ Gemini API 代理失败"
fi

# 测试 OpenAI API (targon 分组)
echo "  测试 OpenAI API 代理 (targon):"
openai_response=$(curl -s -w "HTTP_CODE:%{http_code}" \
    -H "Authorization: Bearer test-key" \
    -H "Content-Type: application/json" \
    http://localhost:3001/proxy/targon/v1/models 2>/dev/null || echo "请求失败")

echo "    响应: $openai_response"

if [[ "$openai_response" == *"HTTP_CODE:401"* ]] || [[ "$openai_response" == *"HTTP_CODE:200"* ]]; then
    echo "    ✓ OpenAI API 代理正常 (通过内部 warp)"
else
    echo "    ❌ OpenAI API 代理失败"
fi

echo ""

# 7. 检查应用日志
echo "🔍 检查应用日志..."
echo "  最近的代理请求日志:"
docker compose logs --tail=5 gpt-load | grep -E "(proxy|request|error)" || echo "    没有发现相关日志"

echo ""

# 8. 检查数据库请求记录
echo "🔍 检查数据库请求记录..."
recent_requests=$(docker compose exec postgres psql -U postgres -d gpt-load -t -c "SELECT COUNT(*) FROM request_logs WHERE timestamp > NOW() - INTERVAL '2 minutes';" 2>/dev/null | xargs || echo "0")
echo "  最近 2 分钟的请求记录: $recent_requests 条"

if [ "$recent_requests" -gt 0 ]; then
    echo "  ✓ 有新的请求记录，代理功能正在工作"
    
    # 显示最新的请求记录
    latest_request=$(docker compose exec postgres psql -U postgres -d gpt-load -t -c "SELECT request_path, status_code, duration FROM request_logs ORDER BY timestamp DESC LIMIT 1;" 2>/dev/null | xargs || echo "无法获取")
    echo "  最新请求: $latest_request"
else
    echo "  ⚠️  没有新的请求记录"
fi

echo ""

# 9. 网络架构总结
echo "📋 当前网络架构:"
echo "  ┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐"
echo "  │   GPT-Load      │───▶│  内部 warp      │───▶│ Cloudflare WARP │"
echo "  │  (172.23.0.x)   │    │  (172.23.0.2)   │    │  (172.16.0.2)   │"
echo "  └─────────────────┘    └─────────────────┘    └─────────────────┘"
echo "           │                       │                       │"
echo "           ▼                       ▼                       ▼"
echo "  ┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐"
echo "  │   PostgreSQL    │    │     Redis       │    │   外部 AI API   │"
echo "  │  (172.23.0.x)   │    │  (172.23.0.x)   │    │ (通过 WARP 访问) │"
echo "  └─────────────────┘    └─────────────────┘    └─────────────────┘"

echo ""

# 10. 总结
echo "📊 测试总结:"
echo "  内部 warp 容器: $(docker compose ps | grep -q "gpt-load-warp.*Up.*healthy" && echo "正常" || echo "异常")"
echo "  代理环境变量: $([ "$http_proxy" = "socks5://warp:1080" ] && echo "正确" || echo "错误")"
echo "  网络连通性: $([ "$warp_internal_ip" != "" ] && echo "正常" || echo "异常")"
echo "  代理功能: $([ "$internal_proxy_ip" != "获取失败" ] && [ "$current_ip" != "$internal_proxy_ip" ] && echo "正常" || echo "异常")"
echo "  API 代理: $([ "$gemini_response" == *"HTTP_CODE:401"* ] && echo "正常" || echo "需检查")"

echo ""
echo "💡 使用说明:"
echo "  1. 管理界面: http://localhost:3001"
echo "  2. 内部代理端口: warp:1080 (容器间通信)"
echo "  3. 外部代理端口: localhost:1081 (宿主机访问)"
echo "  4. API 格式: http://localhost:3001/proxy/{group_name}/{api_path}"

echo ""
if [ "$internal_proxy_ip" != "获取失败" ] && [ "$current_ip" != "$internal_proxy_ip" ] && [[ "$gemini_response" == *"HTTP_CODE:401"* ]]; then
    echo "✅ 内部代理集成测试通过！"
    echo "🎉 GPT-Load 现在通过内部 warp 容器访问外部服务"
else
    echo "⚠️  部分测试未通过，请检查配置"
fi
