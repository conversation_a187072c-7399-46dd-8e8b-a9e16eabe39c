#!/bin/bash

# GPT-Load 前台显示功能验证脚本
# 验证前台是否能正常显示密钥和数据

echo "=== GPT-Load 前台显示功能验证 ==="
echo ""

AUTH_KEY=$(grep AUTH_KEY .env | cut -d'=' -f2)
BASE_URL="http://localhost:3001"

# 1. 健康检查
echo "🔍 健康检查..."
health_response=$(curl -s "$BASE_URL/health" 2>/dev/null || echo "连接失败")
if [[ "$health_response" == *"healthy"* ]]; then
    echo "  ✓ 服务健康"
else
    echo "  ❌ 服务异常: $health_response"
    exit 1
fi

# 2. 测试仪表板统计
echo ""
echo "🔍 测试仪表板统计..."
stats_response=$(curl -s -H "Authorization: Bearer $AUTH_KEY" "$BASE_URL/api/dashboard/stats" 2>/dev/null || echo "请求失败")

if [[ "$stats_response" == *"key_count"* ]]; then
    echo "  ✓ 仪表板统计正常"
    
    # 提取密钥数量
    key_count=$(echo "$stats_response" | grep -o '"key_count":{"value":[0-9]*' | grep -o '[0-9]*$')
    echo "  密钥总数: $key_count"
    
    if [ "$key_count" -ge 15000 ]; then
        echo "  ✓ 密钥数量正确"
    else
        echo "  ❌ 密钥数量异常"
    fi
else
    echo "  ❌ 仪表板统计失败: $stats_response"
fi

# 3. 测试 API 分组列表
echo ""
echo "🔍 测试 API 分组列表..."
groups_response=$(curl -s -H "Authorization: Bearer $AUTH_KEY" "$BASE_URL/api/groups" 2>/dev/null || echo "请求失败")

if [[ "$groups_response" == *"targon"* ]] && [[ "$groups_response" == *"gemini"* ]]; then
    echo "  ✓ API 分组列表正常"
    echo "  分组: targon, gemini"
else
    echo "  ❌ API 分组列表异常: $groups_response"
fi

# 4. 测试密钥列表 (targon 分组)
echo ""
echo "🔍 测试密钥列表 (targon 分组)..."
keys_targon_response=$(curl -s -H "Authorization: Bearer $AUTH_KEY" "$BASE_URL/api/keys?group_id=1&page=1&page_size=10" 2>/dev/null || echo "请求失败")

if [[ "$keys_targon_response" == *"data"* ]] && [[ "$keys_targon_response" == *"total"* ]]; then
    echo "  ✓ targon 分组密钥列表正常"
    
    # 提取总数
    total_targon=$(echo "$keys_targon_response" | grep -o '"total":[0-9]*' | grep -o '[0-9]*$')
    echo "  targon 分组密钥数: $total_targon"
else
    echo "  ❌ targon 分组密钥列表异常: $keys_targon_response"
fi

# 5. 测试密钥列表 (gemini 分组)
echo ""
echo "🔍 测试密钥列表 (gemini 分组)..."
keys_gemini_response=$(curl -s -H "Authorization: Bearer $AUTH_KEY" "$BASE_URL/api/keys?group_id=2&page=1&page_size=10" 2>/dev/null || echo "请求失败")

if [[ "$keys_gemini_response" == *"data"* ]] && [[ "$keys_gemini_response" == *"total"* ]]; then
    echo "  ✓ gemini 分组密钥列表正常"
    
    # 提取总数
    total_gemini=$(echo "$keys_gemini_response" | grep -o '"total":[0-9]*' | grep -o '[0-9]*$')
    echo "  gemini 分组密钥数: $total_gemini"
else
    echo "  ❌ gemini 分组密钥列表异常: $keys_gemini_response"
fi

# 6. 测试分组统计
echo ""
echo "🔍 测试分组统计..."
group_stats_response=$(curl -s -H "Authorization: Bearer $AUTH_KEY" "$BASE_URL/api/groups/1/stats" 2>/dev/null || echo "请求失败")

if [[ "$group_stats_response" == *"total_keys"* ]]; then
    echo "  ✓ 分组统计正常"
    
    # 提取统计信息
    total_keys=$(echo "$group_stats_response" | grep -o '"total_keys":[0-9]*' | grep -o '[0-9]*$')
    echo "  分组统计密钥数: $total_keys"
else
    echo "  ❌ 分组统计异常: $group_stats_response"
fi

# 7. 测试图表数据
echo ""
echo "🔍 测试图表数据..."
chart_response=$(curl -s -H "Authorization: Bearer $AUTH_KEY" "$BASE_URL/api/dashboard/chart" 2>/dev/null || echo "请求失败")

if [[ "$chart_response" == *"data"* ]]; then
    echo "  ✓ 图表数据正常"
else
    echo "  ❌ 图表数据异常: $chart_response"
fi

# 8. 测试任务状态
echo ""
echo "🔍 测试任务状态..."
task_response=$(curl -s -H "Authorization: Bearer $AUTH_KEY" "$BASE_URL/api/tasks/status" 2>/dev/null || echo "请求失败")

if [[ "$task_response" == *"code"* ]]; then
    echo "  ✓ 任务状态正常"
else
    echo "  ❌ 任务状态异常: $task_response"
fi

# 9. 检查数据库连接
echo ""
echo "🔍 检查数据库连接..."
db_test=$(docker compose exec postgres psql -U postgres -d gpt-load -t -c "SELECT COUNT(*) FROM api_keys;" 2>/dev/null | xargs || echo "0")
echo "  数据库中的密钥数量: $db_test"

if [ "$db_test" -ge 15000 ]; then
    echo "  ✓ 数据库连接正常"
else
    echo "  ❌ 数据库连接异常"
fi

# 10. 检查 Redis 缓存
echo ""
echo "🔍 检查 Redis 缓存..."
redis_test=$(docker compose exec redis redis-cli dbsize 2>/dev/null || echo "0")
echo "  Redis 缓存键数量: $redis_test"

if [ "$redis_test" -ge 15000 ]; then
    echo "  ✓ Redis 缓存正常"
else
    echo "  ❌ Redis 缓存异常"
fi

# 11. 检查应用日志
echo ""
echo "🔍 检查应用日志..."
echo "  最近的 API 请求日志:"
docker compose logs --tail=5 gpt-load | grep -E "(GET|POST)" | tail -3 || echo "    没有发现 API 请求日志"

# 检查是否有错误
error_count=$(docker compose logs --tail=20 gpt-load | grep -c -i "error" || echo "0")
echo "  最近 20 条日志中的错误数量: $error_count"

if [ "$error_count" -eq 0 ]; then
    echo "  ✓ 没有发现错误日志"
else
    echo "  ⚠️  发现 $error_count 个错误，请检查日志"
fi

# 12. 总结
echo ""
echo "📊 前台显示功能验证总结:"
echo "  服务健康: $([ "$health_response" == *"healthy"* ] && echo "正常" || echo "异常")"
echo "  仪表板统计: $([ "$stats_response" == *"key_count"* ] && echo "正常" || echo "异常")"
echo "  API 分组: $([ "$groups_response" == *"targon"* ] && echo "正常" || echo "异常")"
echo "  密钥列表: $([ "$keys_targon_response" == *"total"* ] && echo "正常" || echo "异常")"
echo "  数据库连接: $([ "$db_test" -ge 15000 ] && echo "正常" || echo "异常")"
echo "  Redis 缓存: $([ "$redis_test" -ge 15000 ] && echo "正常" || echo "异常")"

echo ""
echo "💡 前台访问信息:"
echo "  管理界面: $BASE_URL"
echo "  认证密钥: $AUTH_KEY"
echo "  密钥总数: $key_count"
echo "  targon 分组: $total_targon 个密钥"
echo "  gemini 分组: $total_gemini 个密钥"

echo ""
if [[ "$stats_response" == *"key_count"* ]] && [[ "$groups_response" == *"targon"* ]] && [[ "$keys_targon_response" == *"total"* ]]; then
    echo "✅ 前台显示功能验证完全通过！"
    echo "🎉 所有密钥和数据都可以在前台正常显示"
    
    # 创建验证成功标记
    echo "$(date): 前台显示功能验证通过" >> data/frontend_verified.log
else
    echo "⚠️  前台显示功能验证部分失败"
    echo "请检查上述错误信息"
    exit 1
fi
