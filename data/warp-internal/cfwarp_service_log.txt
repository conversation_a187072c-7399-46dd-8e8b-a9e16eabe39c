2025-08-05T00:00:09.414Z DEBUG slog: got a response for stream_id=10356, headers=[":status: 200", "cf-team: 29192058c600000482d03da400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T00:00:10.355Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
2025-08-05T00:00:16.755Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
NewNeighbour; Destination: **********; 
2025-08-05T00:00:24.414Z DEBUG slog: got a response for stream_id=10360, headers=[":status: 200", "cf-team: 291920935e00000482d044e400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T00:00:39.415Z DEBUG slog: got a response for stream_id=10364, headers=[":status: 200", "cf-team: 291920cdf600000482d04a2400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T00:00:43.123Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
2025-08-05T00:00:48.759Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
NewNeighbour; Destination: **********; 
2025-08-05T00:00:54.414Z DEBUG slog: got a response for stream_id=10368, headers=[":status: 200", "cf-team: 291921088e00000482d04d6400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T00:01:02.734Z DEBUG upload_stats{stats=StatsPayload { counters: [CounterPayload { name: "device-state.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "main-loop-compat.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "registration-verifier.commands_recv", labels: {}, count: 1 }, CounterPayload { name: "message_bus.bus_driver_commands_sent", labels: {}, count: 4 }, CounterPayload { name: "main-loop-compat.commands_recv", labels: {}, count: 2 }, CounterPayload { name: "statistics.commands_recv", labels: {}, count: 1 }, CounterPayload { name: "alternate-network.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "message_bus.bus_driver_updates_sent", labels: {}, count: 1 }, CounterPayload { name: "dns_proxy.doh_err_other", labels: {}, count: 3 }, CounterPayload { name: "watchdog.requests_recv", labels: {}, count: 388750 }], summaries: [SummaryPayload { name: "message_bus.bus_driver_request_duration_sec_nano_sec", labels: {}, sum: 15.611102874999773, count: 388750 }, SummaryPayload { name: "firewall.fw_avg_ms", labels: {}, sum: 274.0, count: 14 }, SummaryPayload { name: "happy_eyeballs.conn_attempts_all", labels: {}, sum: 1.0, count: 1 }, SummaryPayload { name: "dns_proxy.doh_avg_ms", labels: {}, sum: 7000.0, count: 6438 }], gauges: [GaugesPayload { name: "ntp-clock.min_since_ntp_sync", labels: {}, value: 59.999337526 }, GaugesPayload { name: "happy_eyeballs.conn_attempts_current", labels: {}, value: 1.0 }] } context="timer"}: actor_statistics::handler: Starting to upload stats
2025-08-05T00:01:02.734Z  WARN upload_stats{stats=StatsPayload { counters: [CounterPayload { name: "device-state.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "main-loop-compat.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "registration-verifier.commands_recv", labels: {}, count: 1 }, CounterPayload { name: "message_bus.bus_driver_commands_sent", labels: {}, count: 4 }, CounterPayload { name: "main-loop-compat.commands_recv", labels: {}, count: 2 }, CounterPayload { name: "statistics.commands_recv", labels: {}, count: 1 }, CounterPayload { name: "alternate-network.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "message_bus.bus_driver_updates_sent", labels: {}, count: 1 }, CounterPayload { name: "dns_proxy.doh_err_other", labels: {}, count: 3 }, CounterPayload { name: "watchdog.requests_recv", labels: {}, count: 388750 }], summaries: [SummaryPayload { name: "message_bus.bus_driver_request_duration_sec_nano_sec", labels: {}, sum: 15.611102874999773, count: 388750 }, SummaryPayload { name: "firewall.fw_avg_ms", labels: {}, sum: 274.0, count: 14 }, SummaryPayload { name: "happy_eyeballs.conn_attempts_all", labels: {}, sum: 1.0, count: 1 }, SummaryPayload { name: "dns_proxy.doh_avg_ms", labels: {}, sum: 7000.0, count: 6438 }], gauges: [GaugesPayload { name: "ntp-clock.min_since_ntp_sync", labels: {}, value: 59.999337526 }, GaugesPayload { name: "happy_eyeballs.conn_attempts_current", labels: {}, value: 1.0 }] } context="timer"}: actor_statistics::handler: Skipped uploading aggregate stats because registration was none!
2025-08-05T00:01:09.414Z DEBUG slog: got a response for stream_id=10372, headers=[":status: 200", "cf-team: 291921432600000482d0544400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T00:01:09.487Z DEBUG tunnel_loop{protocol="masque" con_id="6d3539c6af1e789ae39aef05871da34927b6218a"}: warp_edge::tunnel_loop: Tunnel loop statistics stats=TunnelLoopStats { tun_packet_too_large: 0, edge_packet_too_large: 0, tun_packet_too_small: 0, edge_packet_too_small: 0, tun_packet_martian: 0, tun_packet_unhandled: 20, dropped_edge_packet: 0, dropped_tun_packet: 0, dropped_dns_packet: 0, successfully_retried_tun_packet: 0 }
2025-08-05T00:01:13.843Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
2025-08-05T00:01:20.759Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
NewNeighbour; Destination: **********; 
2025-08-05T00:01:24.414Z DEBUG slog: got a response for stream_id=10376, headers=[":status: 200", "cf-team: 2919217dbd00000482d05c9400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T00:01:39.414Z DEBUG slog: got a response for stream_id=10380, headers=[":status: 200", "cf-team: 291921b85500000482d066b400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T00:01:46.611Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
2025-08-05T00:01:52.755Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
NewNeighbour; Destination: **********; 
2025-08-05T00:01:54.414Z DEBUG slog: got a response for stream_id=10384, headers=[":status: 200", "cf-team: 291921f2ed00000482d06b1400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T00:02:09.414Z DEBUG slog: got a response for stream_id=10388, headers=[":status: 200", "cf-team: 2919222d8600000482d06f7400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T00:02:24.413Z DEBUG slog: got a response for stream_id=10392, headers=[":status: 200", "cf-team: 291922681d00000482d0788400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T00:02:32.691Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
2025-08-05T00:02:39.347Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
NewNeighbour; Destination: **********; 
2025-08-05T00:02:39.414Z DEBUG slog: got a response for stream_id=10396, headers=[":status: 200", "cf-team: 291922a2b500000482d0829400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T00:02:54.414Z DEBUG slog: got a response for stream_id=10400, headers=[":status: 200", "cf-team: 291922dd4d00000482d087c400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T00:03:02.733Z DEBUG upload_stats{stats=StatsPayload { counters: [CounterPayload { name: "alternate-network.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "main-loop-compat.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "device-state.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "main-loop-compat.commands_recv", labels: {}, count: 2 }, CounterPayload { name: "message_bus.bus_driver_commands_sent", labels: {}, count: 4 }, CounterPayload { name: "registration-verifier.commands_recv", labels: {}, count: 1 }, CounterPayload { name: "message_bus.bus_driver_updates_sent", labels: {}, count: 1 }, CounterPayload { name: "statistics.commands_recv", labels: {}, count: 1 }, CounterPayload { name: "watchdog.requests_recv", labels: {}, count: 389950 }, CounterPayload { name: "dns_proxy.doh_err_other", labels: {}, count: 3 }], summaries: [SummaryPayload { name: "dns_proxy.doh_avg_ms", labels: {}, sum: 7014.0, count: 6458 }, SummaryPayload { name: "firewall.fw_avg_ms", labels: {}, sum: 274.0, count: 14 }, SummaryPayload { name: "message_bus.bus_driver_request_duration_sec_nano_sec", labels: {}, sum: 15.660729288999741, count: 389950 }, SummaryPayload { name: "happy_eyeballs.conn_attempts_all", labels: {}, sum: 1.0, count: 1 }], gauges: [GaugesPayload { name: "ntp-clock.min_since_ntp_sync", labels: {}, value: 59.99992108 }, GaugesPayload { name: "happy_eyeballs.conn_attempts_current", labels: {}, value: 1.0 }] } context="timer"}: actor_statistics::handler: Starting to upload stats
2025-08-05T00:03:02.733Z  WARN upload_stats{stats=StatsPayload { counters: [CounterPayload { name: "alternate-network.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "main-loop-compat.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "device-state.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "main-loop-compat.commands_recv", labels: {}, count: 2 }, CounterPayload { name: "message_bus.bus_driver_commands_sent", labels: {}, count: 4 }, CounterPayload { name: "registration-verifier.commands_recv", labels: {}, count: 1 }, CounterPayload { name: "message_bus.bus_driver_updates_sent", labels: {}, count: 1 }, CounterPayload { name: "statistics.commands_recv", labels: {}, count: 1 }, CounterPayload { name: "watchdog.requests_recv", labels: {}, count: 389950 }, CounterPayload { name: "dns_proxy.doh_err_other", labels: {}, count: 3 }], summaries: [SummaryPayload { name: "dns_proxy.doh_avg_ms", labels: {}, sum: 7014.0, count: 6458 }, SummaryPayload { name: "firewall.fw_avg_ms", labels: {}, sum: 274.0, count: 14 }, SummaryPayload { name: "message_bus.bus_driver_request_duration_sec_nano_sec", labels: {}, sum: 15.660729288999741, count: 389950 }, SummaryPayload { name: "happy_eyeballs.conn_attempts_all", labels: {}, sum: 1.0, count: 1 }], gauges: [GaugesPayload { name: "ntp-clock.min_since_ntp_sync", labels: {}, value: 59.99992108 }, GaugesPayload { name: "happy_eyeballs.conn_attempts_current", labels: {}, value: 1.0 }] } context="timer"}: actor_statistics::handler: Skipped uploading aggregate stats because registration was none!
2025-08-05T00:03:09.414Z DEBUG slog: got a response for stream_id=10404, headers=[":status: 200", "cf-team: 29192317e500000482d0927400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T00:03:09.486Z DEBUG tunnel_loop{protocol="masque" con_id="6d3539c6af1e789ae39aef05871da34927b6218a"}: warp_edge::tunnel_loop: Tunnel loop statistics stats=TunnelLoopStats { tun_packet_too_large: 0, edge_packet_too_large: 0, tun_packet_too_small: 0, edge_packet_too_small: 0, tun_packet_martian: 0, tun_packet_unhandled: 20, dropped_edge_packet: 0, dropped_tun_packet: 0, dropped_dns_packet: 0, successfully_retried_tun_packet: 0 }
2025-08-05T00:03:20.819Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
2025-08-05T00:03:24.414Z DEBUG slog: got a response for stream_id=10408, headers=[":status: 200", "cf-team: 291923527d00000482d09d1400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T00:03:28.759Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
NewNeighbour; Destination: **********; 
2025-08-05T00:03:39.414Z DEBUG slog: got a response for stream_id=10412, headers=[":status: 200", "cf-team: 2919238d1500000482d0a8f400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T00:03:54.414Z DEBUG slog: got a response for stream_id=10416, headers=[":status: 200", "cf-team: 291923c7ad00000482d0b08400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T00:04:09.414Z DEBUG slog: got a response for stream_id=10420, headers=[":status: 200", "cf-team: 291924024500000482d0b50400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T00:04:09.971Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
2025-08-05T00:04:16.631Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
NewNeighbour; Destination: **********; 
2025-08-05T00:04:24.413Z DEBUG slog: got a response for stream_id=10424, headers=[":status: 200", "cf-team: 2919243cdc00000482d0bae400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T00:04:39.414Z DEBUG slog: got a response for stream_id=10428, headers=[":status: 200", "cf-team: 291924777500000482d0c66400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T00:04:54.414Z DEBUG slog: got a response for stream_id=10432, headers=[":status: 200", "cf-team: 291924b20d00000482d0d56400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T00:04:57.075Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
2025-08-05T00:05:02.733Z DEBUG upload_stats{stats=StatsPayload { counters: [CounterPayload { name: "dns_proxy.doh_err_other", labels: {}, count: 3 }, CounterPayload { name: "message_bus.bus_driver_commands_sent", labels: {}, count: 4 }, CounterPayload { name: "device-state.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "main-loop-compat.commands_recv", labels: {}, count: 2 }, CounterPayload { name: "watchdog.requests_recv", labels: {}, count: 391150 }, CounterPayload { name: "registration-verifier.commands_recv", labels: {}, count: 1 }, CounterPayload { name: "statistics.commands_recv", labels: {}, count: 1 }, CounterPayload { name: "message_bus.bus_driver_updates_sent", labels: {}, count: 1 }, CounterPayload { name: "alternate-network.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "main-loop-compat.updates_recv", labels: {}, count: 1 }], summaries: [SummaryPayload { name: "message_bus.bus_driver_request_duration_sec_nano_sec", labels: {}, sum: 15.709083819999758, count: 391150 }, SummaryPayload { name: "happy_eyeballs.conn_attempts_all", labels: {}, sum: 1.0, count: 1 }, SummaryPayload { name: "firewall.fw_avg_ms", labels: {}, sum: 274.0, count: 14 }, SummaryPayload { name: "dns_proxy.doh_avg_ms", labels: {}, sum: 7037.0, count: 6478 }], gauges: [GaugesPayload { name: "ntp-clock.min_since_ntp_sync", labels: {}, value: 59.999059715 }, GaugesPayload { name: "happy_eyeballs.conn_attempts_current", labels: {}, value: 1.0 }] } context="timer"}: actor_statistics::handler: Starting to upload stats
2025-08-05T00:05:02.733Z  WARN upload_stats{stats=StatsPayload { counters: [CounterPayload { name: "dns_proxy.doh_err_other", labels: {}, count: 3 }, CounterPayload { name: "message_bus.bus_driver_commands_sent", labels: {}, count: 4 }, CounterPayload { name: "device-state.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "main-loop-compat.commands_recv", labels: {}, count: 2 }, CounterPayload { name: "watchdog.requests_recv", labels: {}, count: 391150 }, CounterPayload { name: "registration-verifier.commands_recv", labels: {}, count: 1 }, CounterPayload { name: "statistics.commands_recv", labels: {}, count: 1 }, CounterPayload { name: "message_bus.bus_driver_updates_sent", labels: {}, count: 1 }, CounterPayload { name: "alternate-network.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "main-loop-compat.updates_recv", labels: {}, count: 1 }], summaries: [SummaryPayload { name: "message_bus.bus_driver_request_duration_sec_nano_sec", labels: {}, sum: 15.709083819999758, count: 391150 }, SummaryPayload { name: "happy_eyeballs.conn_attempts_all", labels: {}, sum: 1.0, count: 1 }, SummaryPayload { name: "firewall.fw_avg_ms", labels: {}, sum: 274.0, count: 14 }, SummaryPayload { name: "dns_proxy.doh_avg_ms", labels: {}, sum: 7037.0, count: 6478 }], gauges: [GaugesPayload { name: "ntp-clock.min_since_ntp_sync", labels: {}, value: 59.999059715 }, GaugesPayload { name: "happy_eyeballs.conn_attempts_current", labels: {}, value: 1.0 }] } context="timer"}: actor_statistics::handler: Skipped uploading aggregate stats because registration was none!
2025-08-05T00:05:04.755Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
NewNeighbour; Destination: **********; 
2025-08-05T00:05:09.413Z DEBUG slog: got a response for stream_id=10436, headers=[":status: 200", "cf-team: 291924eca400000482d0dda400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T00:05:09.487Z DEBUG tunnel_loop{protocol="masque" con_id="6d3539c6af1e789ae39aef05871da34927b6218a"}: warp_edge::tunnel_loop: Tunnel loop statistics stats=TunnelLoopStats { tun_packet_too_large: 0, edge_packet_too_large: 0, tun_packet_too_small: 0, edge_packet_too_small: 0, tun_packet_martian: 0, tun_packet_unhandled: 20, dropped_edge_packet: 0, dropped_tun_packet: 0, dropped_dns_packet: 0, successfully_retried_tun_packet: 0 }
2025-08-05T00:05:24.414Z DEBUG slog: got a response for stream_id=10440, headers=[":status: 200", "cf-team: 291925273c00000482d0e51400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T00:05:39.414Z DEBUG slog: got a response for stream_id=10444, headers=[":status: 200", "cf-team: 29192561d400000482d0f20400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T00:05:46.227Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
2025-08-05T00:05:52.627Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
NewNeighbour; Destination: **********; 
2025-08-05T00:05:54.414Z DEBUG slog: got a response for stream_id=10448, headers=[":status: 200", "cf-team: 2919259c6d00000482d1008400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T00:06:09.413Z DEBUG slog: got a response for stream_id=10452, headers=[":status: 200", "cf-team: 291925d70400000482d1099400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T00:06:24.414Z DEBUG slog: got a response for stream_id=10456, headers=[":status: 200", "cf-team: 291926119d00000482d10cb400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T00:06:33.331Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
2025-08-05T00:06:39.413Z DEBUG slog: got a response for stream_id=10460, headers=[":status: 200", "cf-team: 2919264c3400000482d11fb400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T00:06:40.755Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
NewNeighbour; Destination: **********; 
2025-08-05T00:06:54.414Z DEBUG slog: got a response for stream_id=10464, headers=[":status: 200", "cf-team: 29192686cc00000482d1249400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T00:07:02.734Z DEBUG upload_stats{stats=StatsPayload { counters: [CounterPayload { name: "main-loop-compat.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "alternate-network.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "device-state.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "registration-verifier.commands_recv", labels: {}, count: 1 }, CounterPayload { name: "dns_proxy.doh_err_other", labels: {}, count: 3 }, CounterPayload { name: "message_bus.bus_driver_updates_sent", labels: {}, count: 1 }, CounterPayload { name: "watchdog.requests_recv", labels: {}, count: 392350 }, CounterPayload { name: "main-loop-compat.commands_recv", labels: {}, count: 2 }, CounterPayload { name: "message_bus.bus_driver_commands_sent", labels: {}, count: 4 }, CounterPayload { name: "statistics.commands_recv", labels: {}, count: 1 }], summaries: [SummaryPayload { name: "happy_eyeballs.conn_attempts_all", labels: {}, sum: 1.0, count: 1 }, SummaryPayload { name: "firewall.fw_avg_ms", labels: {}, sum: 274.0, count: 14 }, SummaryPayload { name: "dns_proxy.doh_avg_ms", labels: {}, sum: 7052.0, count: 6498 }, SummaryPayload { name: "message_bus.bus_driver_request_duration_sec_nano_sec", labels: {}, sum: 15.757204290999745, count: 392350 }], gauges: [GaugesPayload { name: "ntp-clock.min_since_ntp_sync", labels: {}, value: 60.000440298 }, GaugesPayload { name: "happy_eyeballs.conn_attempts_current", labels: {}, value: 1.0 }] } context="timer"}: actor_statistics::handler: Starting to upload stats
2025-08-05T00:07:02.735Z  WARN upload_stats{stats=StatsPayload { counters: [CounterPayload { name: "main-loop-compat.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "alternate-network.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "device-state.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "registration-verifier.commands_recv", labels: {}, count: 1 }, CounterPayload { name: "dns_proxy.doh_err_other", labels: {}, count: 3 }, CounterPayload { name: "message_bus.bus_driver_updates_sent", labels: {}, count: 1 }, CounterPayload { name: "watchdog.requests_recv", labels: {}, count: 392350 }, CounterPayload { name: "main-loop-compat.commands_recv", labels: {}, count: 2 }, CounterPayload { name: "message_bus.bus_driver_commands_sent", labels: {}, count: 4 }, CounterPayload { name: "statistics.commands_recv", labels: {}, count: 1 }], summaries: [SummaryPayload { name: "happy_eyeballs.conn_attempts_all", labels: {}, sum: 1.0, count: 1 }, SummaryPayload { name: "firewall.fw_avg_ms", labels: {}, sum: 274.0, count: 14 }, SummaryPayload { name: "dns_proxy.doh_avg_ms", labels: {}, sum: 7052.0, count: 6498 }, SummaryPayload { name: "message_bus.bus_driver_request_duration_sec_nano_sec", labels: {}, sum: 15.757204290999745, count: 392350 }], gauges: [GaugesPayload { name: "ntp-clock.min_since_ntp_sync", labels: {}, value: 60.000440298 }, GaugesPayload { name: "happy_eyeballs.conn_attempts_current", labels: {}, value: 1.0 }] } context="timer"}: actor_statistics::handler: Skipped uploading aggregate stats because registration was none!
2025-08-05T00:07:09.414Z DEBUG slog: got a response for stream_id=10468, headers=[":status: 200", "cf-team: 291926c16500000482d12a3400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T00:07:09.486Z DEBUG tunnel_loop{protocol="masque" con_id="6d3539c6af1e789ae39aef05871da34927b6218a"}: warp_edge::tunnel_loop: Tunnel loop statistics stats=TunnelLoopStats { tun_packet_too_large: 0, edge_packet_too_large: 0, tun_packet_too_small: 0, edge_packet_too_small: 0, tun_packet_martian: 0, tun_packet_unhandled: 20, dropped_edge_packet: 0, dropped_tun_packet: 0, dropped_dns_packet: 0, successfully_retried_tun_packet: 0 }
2025-08-05T00:07:22.483Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
2025-08-05T00:07:24.414Z DEBUG slog: got a response for stream_id=10472, headers=[":status: 200", "cf-team: 291926fbfd00000482d12fb400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T00:07:27.859Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
NewNeighbour; Destination: **********; 
2025-08-05T00:07:39.414Z DEBUG slog: got a response for stream_id=10476, headers=[":status: 200", "cf-team: 291927369400000482d1328400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T00:07:54.414Z DEBUG slog: got a response for stream_id=10480, headers=[":status: 200", "cf-team: 291927712c00000482d134d400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T00:08:09.414Z DEBUG slog: got a response for stream_id=10484, headers=[":status: 200", "cf-team: 291927abc500000482d13a6400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T00:08:09.587Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
2025-08-05T00:08:16.755Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
NewNeighbour; Destination: **********; 
2025-08-05T00:08:24.414Z DEBUG slog: got a response for stream_id=10488, headers=[":status: 200", "cf-team: 291927e65d00000482d13f5400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T00:08:39.414Z DEBUG slog: got a response for stream_id=10492, headers=[":status: 200", "cf-team: 29192820f500000482d1428400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T00:08:54.414Z DEBUG slog: got a response for stream_id=10496, headers=[":status: 200", "cf-team: 2919285b8c00000482d144e400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T00:08:58.739Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
2025-08-05T00:09:02.733Z DEBUG upload_stats{stats=StatsPayload { counters: [CounterPayload { name: "alternate-network.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "statistics.commands_recv", labels: {}, count: 1 }, CounterPayload { name: "main-loop-compat.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "message_bus.bus_driver_updates_sent", labels: {}, count: 1 }, CounterPayload { name: "device-state.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "main-loop-compat.commands_recv", labels: {}, count: 2 }, CounterPayload { name: "message_bus.bus_driver_commands_sent", labels: {}, count: 4 }, CounterPayload { name: "watchdog.requests_recv", labels: {}, count: 393550 }, CounterPayload { name: "dns_proxy.doh_err_other", labels: {}, count: 3 }, CounterPayload { name: "registration-verifier.commands_recv", labels: {}, count: 1 }], summaries: [SummaryPayload { name: "dns_proxy.doh_avg_ms", labels: {}, sum: 7060.0, count: 6518 }, SummaryPayload { name: "message_bus.bus_driver_request_duration_sec_nano_sec", labels: {}, sum: 15.804692583999714, count: 393550 }, SummaryPayload { name: "firewall.fw_avg_ms", labels: {}, sum: 274.0, count: 14 }, SummaryPayload { name: "happy_eyeballs.conn_attempts_all", labels: {}, sum: 1.0, count: 1 }], gauges: [GaugesPayload { name: "happy_eyeballs.conn_attempts_current", labels: {}, value: 1.0 }, GaugesPayload { name: "ntp-clock.min_since_ntp_sync", labels: {}, value: 60.011066885 }] } context="timer"}: actor_statistics::handler: Starting to upload stats
2025-08-05T00:09:02.734Z  WARN upload_stats{stats=StatsPayload { counters: [CounterPayload { name: "alternate-network.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "statistics.commands_recv", labels: {}, count: 1 }, CounterPayload { name: "main-loop-compat.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "message_bus.bus_driver_updates_sent", labels: {}, count: 1 }, CounterPayload { name: "device-state.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "main-loop-compat.commands_recv", labels: {}, count: 2 }, CounterPayload { name: "message_bus.bus_driver_commands_sent", labels: {}, count: 4 }, CounterPayload { name: "watchdog.requests_recv", labels: {}, count: 393550 }, CounterPayload { name: "dns_proxy.doh_err_other", labels: {}, count: 3 }, CounterPayload { name: "registration-verifier.commands_recv", labels: {}, count: 1 }], summaries: [SummaryPayload { name: "dns_proxy.doh_avg_ms", labels: {}, sum: 7060.0, count: 6518 }, SummaryPayload { name: "message_bus.bus_driver_request_duration_sec_nano_sec", labels: {}, sum: 15.804692583999714, count: 393550 }, SummaryPayload { name: "firewall.fw_avg_ms", labels: {}, sum: 274.0, count: 14 }, SummaryPayload { name: "happy_eyeballs.conn_attempts_all", labels: {}, sum: 1.0, count: 1 }], gauges: [GaugesPayload { name: "happy_eyeballs.conn_attempts_current", labels: {}, value: 1.0 }, GaugesPayload { name: "ntp-clock.min_since_ntp_sync", labels: {}, value: 60.011066885 }] } context="timer"}: actor_statistics::handler: Skipped uploading aggregate stats because registration was none!
2025-08-05T00:09:04.627Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
NewNeighbour; Destination: **********; 
2025-08-05T00:09:09.414Z DEBUG slog: got a response for stream_id=10500, headers=[":status: 200", "cf-team: 291928962500000482d1478400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T00:09:09.487Z DEBUG tunnel_loop{protocol="masque" con_id="6d3539c6af1e789ae39aef05871da34927b6218a"}: warp_edge::tunnel_loop: Tunnel loop statistics stats=TunnelLoopStats { tun_packet_too_large: 0, edge_packet_too_large: 0, tun_packet_too_small: 0, edge_packet_too_small: 0, tun_packet_martian: 0, tun_packet_unhandled: 20, dropped_edge_packet: 0, dropped_tun_packet: 0, dropped_dns_packet: 0, successfully_retried_tun_packet: 0 }
2025-08-05T00:09:24.414Z DEBUG slog: got a response for stream_id=10504, headers=[":status: 200", "cf-team: 291928d0bc00000482d14d5400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T00:09:39.414Z DEBUG slog: got a response for stream_id=10508, headers=[":status: 200", "cf-team: 2919290b5400000482d14f7400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T00:09:45.843Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
2025-08-05T00:09:52.755Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
NewNeighbour; Destination: **********; 
2025-08-05T00:09:54.414Z DEBUG slog: got a response for stream_id=10512, headers=[":status: 200", "cf-team: 29192945ec00000482d1543400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T00:10:09.414Z DEBUG slog: got a response for stream_id=10516, headers=[":status: 200", "cf-team: 291929808400000482d158d400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T00:10:24.415Z DEBUG slog: got a response for stream_id=10520, headers=[":status: 200", "cf-team: 291929bb1c00000482d15d8400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T00:10:34.995Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
2025-08-05T00:10:39.414Z DEBUG slog: got a response for stream_id=10524, headers=[":status: 200", "cf-team: 291929f5b400000482d1605400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T00:10:40.627Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
NewNeighbour; Destination: **********; 
2025-08-05T00:10:54.414Z DEBUG slog: got a response for stream_id=10528, headers=[":status: 200", "cf-team: 29192a304c00000482d1655400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T00:11:02.733Z DEBUG upload_stats{stats=StatsPayload { counters: [CounterPayload { name: "device-state.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "dns_proxy.doh_err_other", labels: {}, count: 3 }, CounterPayload { name: "main-loop-compat.commands_recv", labels: {}, count: 2 }, CounterPayload { name: "main-loop-compat.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "registration-verifier.commands_recv", labels: {}, count: 1 }, CounterPayload { name: "statistics.commands_recv", labels: {}, count: 1 }, CounterPayload { name: "message_bus.bus_driver_commands_sent", labels: {}, count: 4 }, CounterPayload { name: "message_bus.bus_driver_updates_sent", labels: {}, count: 1 }, CounterPayload { name: "alternate-network.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "watchdog.requests_recv", labels: {}, count: 394750 }], summaries: [SummaryPayload { name: "dns_proxy.doh_avg_ms", labels: {}, sum: 7074.0, count: 6538 }, SummaryPayload { name: "happy_eyeballs.conn_attempts_all", labels: {}, sum: 1.0, count: 1 }, SummaryPayload { name: "message_bus.bus_driver_request_duration_sec_nano_sec", labels: {}, sum: 15.85176270999968, count: 394750 }, SummaryPayload { name: "firewall.fw_avg_ms", labels: {}, sum: 274.0, count: 14 }], gauges: [GaugesPayload { name: "ntp-clock.min_since_ntp_sync", labels: {}, value: 59.99353387 }, GaugesPayload { name: "happy_eyeballs.conn_attempts_current", labels: {}, value: 1.0 }] } context="timer"}: actor_statistics::handler: Starting to upload stats
2025-08-05T00:11:02.733Z  WARN upload_stats{stats=StatsPayload { counters: [CounterPayload { name: "device-state.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "dns_proxy.doh_err_other", labels: {}, count: 3 }, CounterPayload { name: "main-loop-compat.commands_recv", labels: {}, count: 2 }, CounterPayload { name: "main-loop-compat.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "registration-verifier.commands_recv", labels: {}, count: 1 }, CounterPayload { name: "statistics.commands_recv", labels: {}, count: 1 }, CounterPayload { name: "message_bus.bus_driver_commands_sent", labels: {}, count: 4 }, CounterPayload { name: "message_bus.bus_driver_updates_sent", labels: {}, count: 1 }, CounterPayload { name: "alternate-network.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "watchdog.requests_recv", labels: {}, count: 394750 }], summaries: [SummaryPayload { name: "dns_proxy.doh_avg_ms", labels: {}, sum: 7074.0, count: 6538 }, SummaryPayload { name: "happy_eyeballs.conn_attempts_all", labels: {}, sum: 1.0, count: 1 }, SummaryPayload { name: "message_bus.bus_driver_request_duration_sec_nano_sec", labels: {}, sum: 15.85176270999968, count: 394750 }, SummaryPayload { name: "firewall.fw_avg_ms", labels: {}, sum: 274.0, count: 14 }], gauges: [GaugesPayload { name: "ntp-clock.min_since_ntp_sync", labels: {}, value: 59.99353387 }, GaugesPayload { name: "happy_eyeballs.conn_attempts_current", labels: {}, value: 1.0 }] } context="timer"}: actor_statistics::handler: Skipped uploading aggregate stats because registration was none!
2025-08-05T00:11:09.414Z DEBUG slog: got a response for stream_id=10532, headers=[":status: 200", "cf-team: 29192a6ae400000482d1681400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T00:11:09.486Z DEBUG tunnel_loop{protocol="masque" con_id="6d3539c6af1e789ae39aef05871da34927b6218a"}: warp_edge::tunnel_loop: Tunnel loop statistics stats=TunnelLoopStats { tun_packet_too_large: 0, edge_packet_too_large: 0, tun_packet_too_small: 0, edge_packet_too_small: 0, tun_packet_martian: 0, tun_packet_unhandled: 20, dropped_edge_packet: 0, dropped_tun_packet: 0, dropped_dns_packet: 0, successfully_retried_tun_packet: 0 }
2025-08-05T00:11:22.099Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
2025-08-05T00:11:24.414Z DEBUG slog: got a response for stream_id=10536, headers=[":status: 200", "cf-team: 29192aa57c00000482d16d3400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T00:11:28.755Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
NewNeighbour; Destination: **********; 
2025-08-05T00:11:39.414Z DEBUG slog: got a response for stream_id=10540, headers=[":status: 200", "cf-team: 29192ae01400000482d1734400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T00:11:54.413Z DEBUG slog: got a response for stream_id=10544, headers=[":status: 200", "cf-team: 29192b1aac00000482d1793400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T00:12:09.414Z DEBUG slog: got a response for stream_id=10548, headers=[":status: 200", "cf-team: 29192b554400000482d17ee400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T00:12:11.255Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
2025-08-05T00:12:16.371Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
NewNeighbour; Destination: **********; 
2025-08-05T00:12:24.414Z DEBUG slog: got a response for stream_id=10552, headers=[":status: 200", "cf-team: 29192b8fdb00000482d1817400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T00:12:39.414Z DEBUG slog: got a response for stream_id=10556, headers=[":status: 200", "cf-team: 29192bca7400000482d1861400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T00:12:54.259Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
2025-08-05T00:12:54.414Z DEBUG slog: got a response for stream_id=10560, headers=[":status: 200", "cf-team: 29192c050c00000482d18a1400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T00:12:59.635Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
NewNeighbour; Destination: **********; 
2025-08-05T00:13:02.733Z DEBUG upload_stats{stats=StatsPayload { counters: [CounterPayload { name: "alternate-network.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "registration-verifier.commands_recv", labels: {}, count: 1 }, CounterPayload { name: "message_bus.bus_driver_updates_sent", labels: {}, count: 1 }, CounterPayload { name: "message_bus.bus_driver_commands_sent", labels: {}, count: 4 }, CounterPayload { name: "main-loop-compat.commands_recv", labels: {}, count: 2 }, CounterPayload { name: "device-state.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "statistics.commands_recv", labels: {}, count: 1 }, CounterPayload { name: "watchdog.requests_recv", labels: {}, count: 395950 }, CounterPayload { name: "main-loop-compat.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "dns_proxy.doh_err_other", labels: {}, count: 3 }], summaries: [SummaryPayload { name: "happy_eyeballs.conn_attempts_all", labels: {}, sum: 1.0, count: 1 }, SummaryPayload { name: "message_bus.bus_driver_request_duration_sec_nano_sec", labels: {}, sum: 15.898400009999694, count: 395950 }, SummaryPayload { name: "dns_proxy.doh_avg_ms", labels: {}, sum: 7095.0, count: 6558 }, SummaryPayload { name: "firewall.fw_avg_ms", labels: {}, sum: 274.0, count: 14 }], gauges: [GaugesPayload { name: "happy_eyeballs.conn_attempts_current", labels: {}, value: 1.0 }, GaugesPayload { name: "ntp-clock.min_since_ntp_sync", labels: {}, value: 59.999928912 }] } context="timer"}: actor_statistics::handler: Starting to upload stats
2025-08-05T00:13:02.733Z  WARN upload_stats{stats=StatsPayload { counters: [CounterPayload { name: "alternate-network.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "registration-verifier.commands_recv", labels: {}, count: 1 }, CounterPayload { name: "message_bus.bus_driver_updates_sent", labels: {}, count: 1 }, CounterPayload { name: "message_bus.bus_driver_commands_sent", labels: {}, count: 4 }, CounterPayload { name: "main-loop-compat.commands_recv", labels: {}, count: 2 }, CounterPayload { name: "device-state.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "statistics.commands_recv", labels: {}, count: 1 }, CounterPayload { name: "watchdog.requests_recv", labels: {}, count: 395950 }, CounterPayload { name: "main-loop-compat.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "dns_proxy.doh_err_other", labels: {}, count: 3 }], summaries: [SummaryPayload { name: "happy_eyeballs.conn_attempts_all", labels: {}, sum: 1.0, count: 1 }, SummaryPayload { name: "message_bus.bus_driver_request_duration_sec_nano_sec", labels: {}, sum: 15.898400009999694, count: 395950 }, SummaryPayload { name: "dns_proxy.doh_avg_ms", labels: {}, sum: 7095.0, count: 6558 }, SummaryPayload { name: "firewall.fw_avg_ms", labels: {}, sum: 274.0, count: 14 }], gauges: [GaugesPayload { name: "happy_eyeballs.conn_attempts_current", labels: {}, value: 1.0 }, GaugesPayload { name: "ntp-clock.min_since_ntp_sync", labels: {}, value: 59.999928912 }] } context="timer"}: actor_statistics::handler: Skipped uploading aggregate stats because registration was none!
2025-08-05T00:13:09.414Z DEBUG slog: got a response for stream_id=10564, headers=[":status: 200", "cf-team: 29192c3fa300000482d1901400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T00:13:09.487Z DEBUG tunnel_loop{protocol="masque" con_id="6d3539c6af1e789ae39aef05871da34927b6218a"}: warp_edge::tunnel_loop: Tunnel loop statistics stats=TunnelLoopStats { tun_packet_too_large: 0, edge_packet_too_large: 0, tun_packet_too_small: 0, edge_packet_too_small: 0, tun_packet_martian: 0, tun_packet_unhandled: 20, dropped_edge_packet: 0, dropped_tun_packet: 0, dropped_dns_packet: 0, successfully_retried_tun_packet: 0 }
2025-08-05T00:13:24.414Z DEBUG slog: got a response for stream_id=10568, headers=[":status: 200", "cf-team: 29192c7a3c00000482d1921400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T00:13:39.319Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
2025-08-05T00:13:39.414Z DEBUG slog: got a response for stream_id=10572, headers=[":status: 200", "cf-team: 29192cb4d400000482d1941400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T00:13:44.435Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
NewNeighbour; Destination: **********; 
2025-08-05T00:13:54.414Z DEBUG slog: got a response for stream_id=10576, headers=[":status: 200", "cf-team: 29192cef6c00000482d1961400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T00:14:09.413Z DEBUG slog: got a response for stream_id=10580, headers=[":status: 200", "cf-team: 29192d2a0300000482d19d6400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T00:14:22.323Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
2025-08-05T00:14:24.414Z DEBUG slog: got a response for stream_id=10584, headers=[":status: 200", "cf-team: 29192d649c00000482d1a43400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T00:14:28.723Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
NewNeighbour; Destination: **********; 
2025-08-05T00:14:39.414Z DEBUG slog: got a response for stream_id=10588, headers=[":status: 200", "cf-team: 29192d9f3300000482d1a7d400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T00:14:54.414Z DEBUG slog: got a response for stream_id=10592, headers=[":status: 200", "cf-team: 29192dd9cb00000482d1aca400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T00:15:02.736Z DEBUG upload_stats{stats=StatsPayload { counters: [CounterPayload { name: "main-loop-compat.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "dns_proxy.doh_err_other", labels: {}, count: 3 }, CounterPayload { name: "statistics.commands_recv", labels: {}, count: 1 }, CounterPayload { name: "message_bus.bus_driver_updates_sent", labels: {}, count: 1 }, CounterPayload { name: "alternate-network.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "device-state.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "main-loop-compat.commands_recv", labels: {}, count: 2 }, CounterPayload { name: "registration-verifier.commands_recv", labels: {}, count: 1 }, CounterPayload { name: "watchdog.requests_recv", labels: {}, count: 397150 }, CounterPayload { name: "message_bus.bus_driver_commands_sent", labels: {}, count: 4 }], summaries: [SummaryPayload { name: "firewall.fw_avg_ms", labels: {}, sum: 274.0, count: 14 }, SummaryPayload { name: "message_bus.bus_driver_request_duration_sec_nano_sec", labels: {}, sum: 15.948151958999714, count: 397150 }, SummaryPayload { name: "happy_eyeballs.conn_attempts_all", labels: {}, sum: 1.0, count: 1 }, SummaryPayload { name: "dns_proxy.doh_avg_ms", labels: {}, sum: 7111.0, count: 6578 }], gauges: [GaugesPayload { name: "ntp-clock.min_since_ntp_sync", labels: {}, value: 60.008410668 }, GaugesPayload { name: "happy_eyeballs.conn_attempts_current", labels: {}, value: 1.0 }] } context="timer"}: actor_statistics::handler: Starting to upload stats
2025-08-05T00:15:02.736Z  WARN upload_stats{stats=StatsPayload { counters: [CounterPayload { name: "main-loop-compat.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "dns_proxy.doh_err_other", labels: {}, count: 3 }, CounterPayload { name: "statistics.commands_recv", labels: {}, count: 1 }, CounterPayload { name: "message_bus.bus_driver_updates_sent", labels: {}, count: 1 }, CounterPayload { name: "alternate-network.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "device-state.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "main-loop-compat.commands_recv", labels: {}, count: 2 }, CounterPayload { name: "registration-verifier.commands_recv", labels: {}, count: 1 }, CounterPayload { name: "watchdog.requests_recv", labels: {}, count: 397150 }, CounterPayload { name: "message_bus.bus_driver_commands_sent", labels: {}, count: 4 }], summaries: [SummaryPayload { name: "firewall.fw_avg_ms", labels: {}, sum: 274.0, count: 14 }, SummaryPayload { name: "message_bus.bus_driver_request_duration_sec_nano_sec", labels: {}, sum: 15.948151958999714, count: 397150 }, SummaryPayload { name: "happy_eyeballs.conn_attempts_all", labels: {}, sum: 1.0, count: 1 }, SummaryPayload { name: "dns_proxy.doh_avg_ms", labels: {}, sum: 7111.0, count: 6578 }], gauges: [GaugesPayload { name: "ntp-clock.min_since_ntp_sync", labels: {}, value: 60.008410668 }, GaugesPayload { name: "happy_eyeballs.conn_attempts_current", labels: {}, value: 1.0 }] } context="timer"}: actor_statistics::handler: Skipped uploading aggregate stats because registration was none!
2025-08-05T00:15:07.379Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
2025-08-05T00:15:09.414Z DEBUG slog: got a response for stream_id=10596, headers=[":status: 200", "cf-team: 29192e146300000482d1b47400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T00:15:09.486Z DEBUG tunnel_loop{protocol="masque" con_id="6d3539c6af1e789ae39aef05871da34927b6218a"}: warp_edge::tunnel_loop: Tunnel loop statistics stats=TunnelLoopStats { tun_packet_too_large: 0, edge_packet_too_large: 0, tun_packet_too_small: 0, edge_packet_too_small: 0, tun_packet_martian: 0, tun_packet_unhandled: 20, dropped_edge_packet: 0, dropped_tun_packet: 0, dropped_dns_packet: 0, successfully_retried_tun_packet: 0 }
2025-08-05T00:15:12.755Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
NewNeighbour; Destination: **********; 
2025-08-05T00:15:24.413Z DEBUG slog: got a response for stream_id=10600, headers=[":status: 200", "cf-team: 29192e4efb00000482d1b87400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T00:15:39.414Z DEBUG slog: got a response for stream_id=10604, headers=[":status: 200", "cf-team: 29192e899300000482d1bb6400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T00:15:52.435Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
2025-08-05T00:15:54.413Z DEBUG slog: got a response for stream_id=10608, headers=[":status: 200", "cf-team: 29192ec42b00000482d1c27400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T00:15:59.603Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
NewNeighbour; Destination: **********; 
2025-08-05T00:16:09.414Z DEBUG slog: got a response for stream_id=10612, headers=[":status: 200", "cf-team: 29192efec400000482d1c4e400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T00:16:24.414Z DEBUG slog: got a response for stream_id=10616, headers=[":status: 200", "cf-team: 29192f395c00000482d1cb6400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T00:16:37.491Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
2025-08-05T00:16:39.414Z DEBUG slog: got a response for stream_id=10620, headers=[":status: 200", "cf-team: 29192f73f300000482d1cea400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T00:16:44.659Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
NewNeighbour; Destination: **********; 
2025-08-05T00:16:54.414Z DEBUG slog: got a response for stream_id=10624, headers=[":status: 200", "cf-team: 29192fae8c00000482d1d2b400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T00:17:02.735Z DEBUG upload_stats{stats=StatsPayload { counters: [CounterPayload { name: "main-loop-compat.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "dns_proxy.doh_err_other", labels: {}, count: 3 }, CounterPayload { name: "watchdog.requests_recv", labels: {}, count: 398350 }, CounterPayload { name: "registration-verifier.commands_recv", labels: {}, count: 1 }, CounterPayload { name: "statistics.commands_recv", labels: {}, count: 1 }, CounterPayload { name: "message_bus.bus_driver_commands_sent", labels: {}, count: 4 }, CounterPayload { name: "device-state.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "main-loop-compat.commands_recv", labels: {}, count: 2 }, CounterPayload { name: "message_bus.bus_driver_updates_sent", labels: {}, count: 1 }, CounterPayload { name: "alternate-network.updates_recv", labels: {}, count: 1 }], summaries: [SummaryPayload { name: "dns_proxy.doh_avg_ms", labels: {}, sum: 7132.0, count: 6598 }, SummaryPayload { name: "firewall.fw_avg_ms", labels: {}, sum: 274.0, count: 14 }, SummaryPayload { name: "happy_eyeballs.conn_attempts_all", labels: {}, sum: 1.0, count: 1 }, SummaryPayload { name: "message_bus.bus_driver_request_duration_sec_nano_sec", labels: {}, sum: 15.994483233999743, count: 398350 }], gauges: [GaugesPayload { name: "ntp-clock.min_since_ntp_sync", labels: {}, value: 59.994315908 }, GaugesPayload { name: "happy_eyeballs.conn_attempts_current", labels: {}, value: 1.0 }] } context="timer"}: actor_statistics::handler: Starting to upload stats
2025-08-05T00:17:02.735Z  WARN upload_stats{stats=StatsPayload { counters: [CounterPayload { name: "main-loop-compat.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "dns_proxy.doh_err_other", labels: {}, count: 3 }, CounterPayload { name: "watchdog.requests_recv", labels: {}, count: 398350 }, CounterPayload { name: "registration-verifier.commands_recv", labels: {}, count: 1 }, CounterPayload { name: "statistics.commands_recv", labels: {}, count: 1 }, CounterPayload { name: "message_bus.bus_driver_commands_sent", labels: {}, count: 4 }, CounterPayload { name: "device-state.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "main-loop-compat.commands_recv", labels: {}, count: 2 }, CounterPayload { name: "message_bus.bus_driver_updates_sent", labels: {}, count: 1 }, CounterPayload { name: "alternate-network.updates_recv", labels: {}, count: 1 }], summaries: [SummaryPayload { name: "dns_proxy.doh_avg_ms", labels: {}, sum: 7132.0, count: 6598 }, SummaryPayload { name: "firewall.fw_avg_ms", labels: {}, sum: 274.0, count: 14 }, SummaryPayload { name: "happy_eyeballs.conn_attempts_all", labels: {}, sum: 1.0, count: 1 }, SummaryPayload { name: "message_bus.bus_driver_request_duration_sec_nano_sec", labels: {}, sum: 15.994483233999743, count: 398350 }], gauges: [GaugesPayload { name: "ntp-clock.min_since_ntp_sync", labels: {}, value: 59.994315908 }, GaugesPayload { name: "happy_eyeballs.conn_attempts_current", labels: {}, value: 1.0 }] } context="timer"}: actor_statistics::handler: Skipped uploading aggregate stats because registration was none!
2025-08-05T00:17:09.414Z DEBUG slog: got a response for stream_id=10628, headers=[":status: 200", "cf-team: 29192fe92400000482d1d5d400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T00:17:09.487Z DEBUG tunnel_loop{protocol="masque" con_id="6d3539c6af1e789ae39aef05871da34927b6218a"}: warp_edge::tunnel_loop: Tunnel loop statistics stats=TunnelLoopStats { tun_packet_too_large: 0, edge_packet_too_large: 0, tun_packet_too_small: 0, edge_packet_too_small: 0, tun_packet_martian: 0, tun_packet_unhandled: 20, dropped_edge_packet: 0, dropped_tun_packet: 0, dropped_dns_packet: 0, successfully_retried_tun_packet: 0 }
2025-08-05T00:17:22.547Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
2025-08-05T00:17:24.414Z DEBUG slog: got a response for stream_id=10632, headers=[":status: 200", "cf-team: 29193023bb00000482d1dc1400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T00:17:28.691Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
NewNeighbour; Destination: **********; 
2025-08-05T00:17:39.414Z DEBUG slog: got a response for stream_id=10636, headers=[":status: 200", "cf-team: 2919305e5300000482d1e4d400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T00:17:54.414Z DEBUG slog: got a response for stream_id=10640, headers=[":status: 200", "cf-team: 29193098ec00000482d1ea9400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T00:17:55.315Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
2025-08-05T00:18:00.691Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
NewNeighbour; Destination: **********; 
2025-08-05T00:18:09.414Z DEBUG slog: got a response for stream_id=10644, headers=[":status: 200", "cf-team: 291930d38400000482d1ecb400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T00:18:24.414Z DEBUG slog: got a response for stream_id=10648, headers=[":status: 200", "cf-team: 2919310e1b00000482d1f2a400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T00:18:26.035Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
2025-08-05T00:18:32.691Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
NewNeighbour; Destination: **********; 
2025-08-05T00:18:39.415Z DEBUG slog: got a response for stream_id=10652, headers=[":status: 200", "cf-team: 29193148b400000482d1f55400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T00:18:54.414Z DEBUG slog: got a response for stream_id=10656, headers=[":status: 200", "cf-team: 291931834b00000482d1f97400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T00:18:58.803Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
2025-08-05T00:19:02.736Z DEBUG upload_stats{stats=StatsPayload { counters: [CounterPayload { name: "statistics.commands_recv", labels: {}, count: 1 }, CounterPayload { name: "device-state.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "main-loop-compat.commands_recv", labels: {}, count: 2 }, CounterPayload { name: "message_bus.bus_driver_updates_sent", labels: {}, count: 1 }, CounterPayload { name: "watchdog.requests_recv", labels: {}, count: 399550 }, CounterPayload { name: "registration-verifier.commands_recv", labels: {}, count: 1 }, CounterPayload { name: "message_bus.bus_driver_commands_sent", labels: {}, count: 4 }, CounterPayload { name: "dns_proxy.doh_err_other", labels: {}, count: 3 }, CounterPayload { name: "main-loop-compat.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "alternate-network.updates_recv", labels: {}, count: 1 }], summaries: [SummaryPayload { name: "message_bus.bus_driver_request_duration_sec_nano_sec", labels: {}, sum: 16.040093844999774, count: 399550 }, SummaryPayload { name: "happy_eyeballs.conn_attempts_all", labels: {}, sum: 1.0, count: 1 }, SummaryPayload { name: "firewall.fw_avg_ms", labels: {}, sum: 274.0, count: 14 }, SummaryPayload { name: "dns_proxy.doh_avg_ms", labels: {}, sum: 7139.0, count: 6618 }], gauges: [GaugesPayload { name: "happy_eyeballs.conn_attempts_current", labels: {}, value: 1.0 }, GaugesPayload { name: "ntp-clock.min_since_ntp_sync", labels: {}, value: 60.000514202 }] } context="timer"}: actor_statistics::handler: Starting to upload stats
2025-08-05T00:19:02.736Z  WARN upload_stats{stats=StatsPayload { counters: [CounterPayload { name: "statistics.commands_recv", labels: {}, count: 1 }, CounterPayload { name: "device-state.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "main-loop-compat.commands_recv", labels: {}, count: 2 }, CounterPayload { name: "message_bus.bus_driver_updates_sent", labels: {}, count: 1 }, CounterPayload { name: "watchdog.requests_recv", labels: {}, count: 399550 }, CounterPayload { name: "registration-verifier.commands_recv", labels: {}, count: 1 }, CounterPayload { name: "message_bus.bus_driver_commands_sent", labels: {}, count: 4 }, CounterPayload { name: "dns_proxy.doh_err_other", labels: {}, count: 3 }, CounterPayload { name: "main-loop-compat.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "alternate-network.updates_recv", labels: {}, count: 1 }], summaries: [SummaryPayload { name: "message_bus.bus_driver_request_duration_sec_nano_sec", labels: {}, sum: 16.040093844999774, count: 399550 }, SummaryPayload { name: "happy_eyeballs.conn_attempts_all", labels: {}, sum: 1.0, count: 1 }, SummaryPayload { name: "firewall.fw_avg_ms", labels: {}, sum: 274.0, count: 14 }, SummaryPayload { name: "dns_proxy.doh_avg_ms", labels: {}, sum: 7139.0, count: 6618 }], gauges: [GaugesPayload { name: "happy_eyeballs.conn_attempts_current", labels: {}, value: 1.0 }, GaugesPayload { name: "ntp-clock.min_since_ntp_sync", labels: {}, value: 60.000514202 }] } context="timer"}: actor_statistics::handler: Skipped uploading aggregate stats because registration was none!
2025-08-05T00:19:04.691Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
NewNeighbour; Destination: **********; 
2025-08-05T00:19:09.414Z DEBUG slog: got a response for stream_id=10660, headers=[":status: 200", "cf-team: 291931bde300000482d1fdb400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T00:19:09.486Z DEBUG tunnel_loop{protocol="masque" con_id="6d3539c6af1e789ae39aef05871da34927b6218a"}: warp_edge::tunnel_loop: Tunnel loop statistics stats=TunnelLoopStats { tun_packet_too_large: 0, edge_packet_too_large: 0, tun_packet_too_small: 0, edge_packet_too_small: 0, tun_packet_martian: 0, tun_packet_unhandled: 20, dropped_edge_packet: 0, dropped_tun_packet: 0, dropped_dns_packet: 0, successfully_retried_tun_packet: 0 }
2025-08-05T00:19:24.414Z DEBUG slog: got a response for stream_id=10664, headers=[":status: 200", "cf-team: 291931f87b00000482d201e400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T00:19:31.571Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
2025-08-05T00:19:39.414Z DEBUG slog: got a response for stream_id=10668, headers=[":status: 200", "cf-team: 291932331400000482d2053400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T00:19:40.531Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
NewNeighbour; Destination: **********; 
2025-08-05T00:19:54.414Z DEBUG slog: got a response for stream_id=10672, headers=[":status: 200", "cf-team: 2919326dab00000482d208e400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T00:20:06.391Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
2025-08-05T00:20:09.414Z DEBUG slog: got a response for stream_id=10676, headers=[":status: 200", "cf-team: 291932a84300000482d20b8400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T00:20:12.531Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
NewNeighbour; Destination: **********; 
2025-08-05T00:20:24.415Z DEBUG slog: got a response for stream_id=10680, headers=[":status: 200", "cf-team: 291932e2db00000482d20f5400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T00:20:39.155Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
2025-08-05T00:20:39.413Z DEBUG slog: got a response for stream_id=10684, headers=[":status: 200", "cf-team: 2919331d7300000482d210d400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T00:20:44.531Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
NewNeighbour; Destination: **********; 
2025-08-05T00:20:54.414Z DEBUG slog: got a response for stream_id=10688, headers=[":status: 200", "cf-team: 291933580c00000482d2159400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T00:21:02.733Z DEBUG upload_stats{stats=StatsPayload { counters: [CounterPayload { name: "device-state.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "dns_proxy.doh_err_other", labels: {}, count: 3 }, CounterPayload { name: "main-loop-compat.commands_recv", labels: {}, count: 2 }, CounterPayload { name: "alternate-network.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "main-loop-compat.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "message_bus.bus_driver_commands_sent", labels: {}, count: 4 }, CounterPayload { name: "message_bus.bus_driver_updates_sent", labels: {}, count: 1 }, CounterPayload { name: "registration-verifier.commands_recv", labels: {}, count: 1 }, CounterPayload { name: "statistics.commands_recv", labels: {}, count: 1 }, CounterPayload { name: "watchdog.requests_recv", labels: {}, count: 400750 }], summaries: [SummaryPayload { name: "firewall.fw_avg_ms", labels: {}, sum: 274.0, count: 14 }, SummaryPayload { name: "dns_proxy.doh_avg_ms", labels: {}, sum: 7160.0, count: 6638 }, SummaryPayload { name: "happy_eyeballs.conn_attempts_all", labels: {}, sum: 1.0, count: 1 }, SummaryPayload { name: "message_bus.bus_driver_request_duration_sec_nano_sec", labels: {}, sum: 16.08628894299972, count: 400750 }], gauges: [GaugesPayload { name: "happy_eyeballs.conn_attempts_current", labels: {}, value: 1.0 }, GaugesPayload { name: "ntp-clock.min_since_ntp_sync", labels: {}, value: 60.000337443 }] } context="timer"}: actor_statistics::handler: Starting to upload stats
2025-08-05T00:21:02.733Z  WARN upload_stats{stats=StatsPayload { counters: [CounterPayload { name: "device-state.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "dns_proxy.doh_err_other", labels: {}, count: 3 }, CounterPayload { name: "main-loop-compat.commands_recv", labels: {}, count: 2 }, CounterPayload { name: "alternate-network.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "main-loop-compat.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "message_bus.bus_driver_commands_sent", labels: {}, count: 4 }, CounterPayload { name: "message_bus.bus_driver_updates_sent", labels: {}, count: 1 }, CounterPayload { name: "registration-verifier.commands_recv", labels: {}, count: 1 }, CounterPayload { name: "statistics.commands_recv", labels: {}, count: 1 }, CounterPayload { name: "watchdog.requests_recv", labels: {}, count: 400750 }], summaries: [SummaryPayload { name: "firewall.fw_avg_ms", labels: {}, sum: 274.0, count: 14 }, SummaryPayload { name: "dns_proxy.doh_avg_ms", labels: {}, sum: 7160.0, count: 6638 }, SummaryPayload { name: "happy_eyeballs.conn_attempts_all", labels: {}, sum: 1.0, count: 1 }, SummaryPayload { name: "message_bus.bus_driver_request_duration_sec_nano_sec", labels: {}, sum: 16.08628894299972, count: 400750 }], gauges: [GaugesPayload { name: "happy_eyeballs.conn_attempts_current", labels: {}, value: 1.0 }, GaugesPayload { name: "ntp-clock.min_since_ntp_sync", labels: {}, value: 60.000337443 }] } context="timer"}: actor_statistics::handler: Skipped uploading aggregate stats because registration was none!
2025-08-05T00:21:09.414Z DEBUG slog: got a response for stream_id=10692, headers=[":status: 200", "cf-team: 29193392a300000482d219c400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T00:21:09.486Z DEBUG tunnel_loop{protocol="masque" con_id="6d3539c6af1e789ae39aef05871da34927b6218a"}: warp_edge::tunnel_loop: Tunnel loop statistics stats=TunnelLoopStats { tun_packet_too_large: 0, edge_packet_too_large: 0, tun_packet_too_small: 0, edge_packet_too_small: 0, tun_packet_martian: 0, tun_packet_unhandled: 20, dropped_edge_packet: 0, dropped_tun_packet: 0, dropped_dns_packet: 0, successfully_retried_tun_packet: 0 }
2025-08-05T00:21:09.875Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
2025-08-05T00:21:16.531Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
NewNeighbour; Destination: **********; 
2025-08-05T00:21:24.414Z DEBUG slog: got a response for stream_id=10696, headers=[":status: 200", "cf-team: 291933cd3b00000482d21e6400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T00:21:39.414Z DEBUG slog: got a response for stream_id=10700, headers=[":status: 200", "cf-team: 29193407d400000482d221d400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T00:21:42.643Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
2025-08-05T00:21:48.531Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
NewNeighbour; Destination: **********; 
2025-08-05T00:21:54.414Z DEBUG slog: got a response for stream_id=10704, headers=[":status: 200", "cf-team: 291934426b00000482d2281400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T00:22:09.414Z DEBUG slog: got a response for stream_id=10708, headers=[":status: 200", "cf-team: 2919347d0300000482d22e9400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T00:22:24.414Z DEBUG slog: got a response for stream_id=10712, headers=[":status: 200", "cf-team: 291934b79b00000482d2339400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T00:22:24.883Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
2025-08-05T00:22:32.563Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
NewNeighbour; Destination: **********; 
2025-08-05T00:22:39.414Z DEBUG slog: got a response for stream_id=10716, headers=[":status: 200", "cf-team: 291934f23300000482d2384400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T00:22:54.414Z DEBUG slog: got a response for stream_id=10720, headers=[":status: 200", "cf-team: 2919352ccb00000482d23fa400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T00:23:02.736Z DEBUG upload_stats{stats=StatsPayload { counters: [CounterPayload { name: "watchdog.requests_recv", labels: {}, count: 401950 }, CounterPayload { name: "main-loop-compat.commands_recv", labels: {}, count: 2 }, CounterPayload { name: "dns_proxy.doh_err_other", labels: {}, count: 3 }, CounterPayload { name: "device-state.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "alternate-network.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "main-loop-compat.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "statistics.commands_recv", labels: {}, count: 1 }, CounterPayload { name: "registration-verifier.commands_recv", labels: {}, count: 1 }, CounterPayload { name: "message_bus.bus_driver_commands_sent", labels: {}, count: 4 }, CounterPayload { name: "message_bus.bus_driver_updates_sent", labels: {}, count: 1 }], summaries: [SummaryPayload { name: "firewall.fw_avg_ms", labels: {}, sum: 274.0, count: 14 }, SummaryPayload { name: "happy_eyeballs.conn_attempts_all", labels: {}, sum: 1.0, count: 1 }, SummaryPayload { name: "message_bus.bus_driver_request_duration_sec_nano_sec", labels: {}, sum: 16.13275911599966, count: 401950 }, SummaryPayload { name: "dns_proxy.doh_avg_ms", labels: {}, sum: 7167.0, count: 6656 }], gauges: [GaugesPayload { name: "ntp-clock.min_since_ntp_sync", labels: {}, value: 60.001013503 }, GaugesPayload { name: "happy_eyeballs.conn_attempts_current", labels: {}, value: 1.0 }] } context="timer"}: actor_statistics::handler: Starting to upload stats
2025-08-05T00:23:02.736Z  WARN upload_stats{stats=StatsPayload { counters: [CounterPayload { name: "watchdog.requests_recv", labels: {}, count: 401950 }, CounterPayload { name: "main-loop-compat.commands_recv", labels: {}, count: 2 }, CounterPayload { name: "dns_proxy.doh_err_other", labels: {}, count: 3 }, CounterPayload { name: "device-state.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "alternate-network.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "main-loop-compat.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "statistics.commands_recv", labels: {}, count: 1 }, CounterPayload { name: "registration-verifier.commands_recv", labels: {}, count: 1 }, CounterPayload { name: "message_bus.bus_driver_commands_sent", labels: {}, count: 4 }, CounterPayload { name: "message_bus.bus_driver_updates_sent", labels: {}, count: 1 }], summaries: [SummaryPayload { name: "firewall.fw_avg_ms", labels: {}, sum: 274.0, count: 14 }, SummaryPayload { name: "happy_eyeballs.conn_attempts_all", labels: {}, sum: 1.0, count: 1 }, SummaryPayload { name: "message_bus.bus_driver_request_duration_sec_nano_sec", labels: {}, sum: 16.13275911599966, count: 401950 }, SummaryPayload { name: "dns_proxy.doh_avg_ms", labels: {}, sum: 7167.0, count: 6656 }], gauges: [GaugesPayload { name: "ntp-clock.min_since_ntp_sync", labels: {}, value: 60.001013503 }, GaugesPayload { name: "happy_eyeballs.conn_attempts_current", labels: {}, value: 1.0 }] } context="timer"}: actor_statistics::handler: Skipped uploading aggregate stats because registration was none!
2025-08-05T00:23:09.414Z DEBUG slog: got a response for stream_id=10724, headers=[":status: 200", "cf-team: 291935676300000482d243b400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T00:23:09.487Z DEBUG tunnel_loop{protocol="masque" con_id="6d3539c6af1e789ae39aef05871da34927b6218a"}: warp_edge::tunnel_loop: Tunnel loop statistics stats=TunnelLoopStats { tun_packet_too_large: 0, edge_packet_too_large: 0, tun_packet_too_small: 0, edge_packet_too_small: 0, tun_packet_martian: 0, tun_packet_unhandled: 20, dropped_edge_packet: 0, dropped_tun_packet: 0, dropped_dns_packet: 0, successfully_retried_tun_packet: 0 }
2025-08-05T00:23:10.707Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
2025-08-05T00:23:16.595Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
NewNeighbour; Destination: **********; 
2025-08-05T00:23:24.414Z DEBUG slog: got a response for stream_id=10728, headers=[":status: 200", "cf-team: 291935a1fb00000482d2475400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T00:23:39.414Z DEBUG slog: got a response for stream_id=10732, headers=[":status: 200", "cf-team: 291935dc9300000482d24ae400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T00:23:53.715Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
2025-08-05T00:23:54.415Z DEBUG slog: got a response for stream_id=10736, headers=[":status: 200", "cf-team: 291936172c00000482d24e8400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T00:23:59.603Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
NewNeighbour; Destination: **********; 
2025-08-05T00:24:09.414Z DEBUG slog: got a response for stream_id=10740, headers=[":status: 200", "cf-team: 29193651c300000482d252a400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T00:24:24.414Z DEBUG slog: got a response for stream_id=10744, headers=[":status: 200", "cf-team: 2919368c5b00000482d2559400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T00:24:36.723Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
2025-08-05T00:24:39.414Z DEBUG slog: got a response for stream_id=10748, headers=[":status: 200", "cf-team: 291936c6f300000482d25b2400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T00:24:44.659Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
NewNeighbour; Destination: **********; 
2025-08-05T00:24:54.414Z DEBUG slog: got a response for stream_id=10752, headers=[":status: 200", "cf-team: 291937018b00000482d25fc400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T00:25:02.737Z DEBUG upload_stats{stats=StatsPayload { counters: [CounterPayload { name: "main-loop-compat.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "message_bus.bus_driver_updates_sent", labels: {}, count: 1 }, CounterPayload { name: "dns_proxy.doh_err_other", labels: {}, count: 3 }, CounterPayload { name: "alternate-network.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "statistics.commands_recv", labels: {}, count: 1 }, CounterPayload { name: "watchdog.requests_recv", labels: {}, count: 403150 }, CounterPayload { name: "registration-verifier.commands_recv", labels: {}, count: 1 }, CounterPayload { name: "main-loop-compat.commands_recv", labels: {}, count: 2 }, CounterPayload { name: "device-state.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "message_bus.bus_driver_commands_sent", labels: {}, count: 4 }], summaries: [SummaryPayload { name: "happy_eyeballs.conn_attempts_all", labels: {}, sum: 1.0, count: 1 }, SummaryPayload { name: "firewall.fw_avg_ms", labels: {}, sum: 274.0, count: 14 }, SummaryPayload { name: "dns_proxy.doh_avg_ms", labels: {}, sum: 7175.0, count: 6676 }, SummaryPayload { name: "message_bus.bus_driver_request_duration_sec_nano_sec", labels: {}, sum: 16.178095291999693, count: 403150 }], gauges: [GaugesPayload { name: "happy_eyeballs.conn_attempts_current", labels: {}, value: 1.0 }, GaugesPayload { name: "ntp-clock.min_since_ntp_sync", labels: {}, value: 60.00810859 }] } context="timer"}: actor_statistics::handler: Starting to upload stats
2025-08-05T00:25:02.737Z  WARN upload_stats{stats=StatsPayload { counters: [CounterPayload { name: "main-loop-compat.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "message_bus.bus_driver_updates_sent", labels: {}, count: 1 }, CounterPayload { name: "dns_proxy.doh_err_other", labels: {}, count: 3 }, CounterPayload { name: "alternate-network.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "statistics.commands_recv", labels: {}, count: 1 }, CounterPayload { name: "watchdog.requests_recv", labels: {}, count: 403150 }, CounterPayload { name: "registration-verifier.commands_recv", labels: {}, count: 1 }, CounterPayload { name: "main-loop-compat.commands_recv", labels: {}, count: 2 }, CounterPayload { name: "device-state.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "message_bus.bus_driver_commands_sent", labels: {}, count: 4 }], summaries: [SummaryPayload { name: "happy_eyeballs.conn_attempts_all", labels: {}, sum: 1.0, count: 1 }, SummaryPayload { name: "firewall.fw_avg_ms", labels: {}, sum: 274.0, count: 14 }, SummaryPayload { name: "dns_proxy.doh_avg_ms", labels: {}, sum: 7175.0, count: 6676 }, SummaryPayload { name: "message_bus.bus_driver_request_duration_sec_nano_sec", labels: {}, sum: 16.178095291999693, count: 403150 }], gauges: [GaugesPayload { name: "happy_eyeballs.conn_attempts_current", labels: {}, value: 1.0 }, GaugesPayload { name: "ntp-clock.min_since_ntp_sync", labels: {}, value: 60.00810859 }] } context="timer"}: actor_statistics::handler: Skipped uploading aggregate stats because registration was none!
2025-08-05T00:25:09.413Z DEBUG slog: got a response for stream_id=10756, headers=[":status: 200", "cf-team: 2919373c2300000482d2612400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T00:25:09.487Z DEBUG tunnel_loop{protocol="masque" con_id="6d3539c6af1e789ae39aef05871da34927b6218a"}: warp_edge::tunnel_loop: Tunnel loop statistics stats=TunnelLoopStats { tun_packet_too_large: 0, edge_packet_too_large: 0, tun_packet_too_small: 0, edge_packet_too_small: 0, tun_packet_martian: 0, tun_packet_unhandled: 20, dropped_edge_packet: 0, dropped_tun_packet: 0, dropped_dns_packet: 0, successfully_retried_tun_packet: 0 }
2025-08-05T00:25:21.779Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
2025-08-05T00:25:24.414Z DEBUG slog: got a response for stream_id=10760, headers=[":status: 200", "cf-team: 29193776bb00000482d2660400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T00:25:28.691Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
2025-08-05T00:25:28.692Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
2025-08-05T00:25:39.414Z DEBUG slog: got a response for stream_id=10764, headers=[":status: 200", "cf-team: 291937b15300000482d2685400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T00:25:54.414Z DEBUG slog: got a response for stream_id=10768, headers=[":status: 200", "cf-team: 291937ebeb00000482d26c5400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T00:26:06.835Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
2025-08-05T00:26:09.414Z DEBUG slog: got a response for stream_id=10772, headers=[":status: 200", "cf-team: 291938268400000482d271e400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T00:26:12.723Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
NewNeighbour; Destination: **********; 
2025-08-05T00:26:24.414Z DEBUG slog: got a response for stream_id=10776, headers=[":status: 200", "cf-team: 291938611b00000482d2789400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T00:26:39.414Z DEBUG slog: got a response for stream_id=10780, headers=[":status: 200", "cf-team: 2919389bb300000482d282b400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T00:26:49.843Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
2025-08-05T00:26:54.414Z DEBUG slog: got a response for stream_id=10784, headers=[":status: 200", "cf-team: 291938d64b00000482d2862400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T00:26:56.755Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
NewNeighbour; Destination: **********; 
2025-08-05T00:27:02.733Z DEBUG upload_stats{stats=StatsPayload { counters: [CounterPayload { name: "main-loop-compat.commands_recv", labels: {}, count: 2 }, CounterPayload { name: "message_bus.bus_driver_updates_sent", labels: {}, count: 1 }, CounterPayload { name: "main-loop-compat.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "alternate-network.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "watchdog.requests_recv", labels: {}, count: 404350 }, CounterPayload { name: "message_bus.bus_driver_commands_sent", labels: {}, count: 4 }, CounterPayload { name: "registration-verifier.commands_recv", labels: {}, count: 1 }, CounterPayload { name: "statistics.commands_recv", labels: {}, count: 1 }, CounterPayload { name: "dns_proxy.doh_err_other", labels: {}, count: 3 }, CounterPayload { name: "device-state.updates_recv", labels: {}, count: 1 }], summaries: [SummaryPayload { name: "message_bus.bus_driver_request_duration_sec_nano_sec", labels: {}, sum: 16.220849373999652, count: 404350 }, SummaryPayload { name: "firewall.fw_avg_ms", labels: {}, sum: 274.0, count: 14 }, SummaryPayload { name: "dns_proxy.doh_avg_ms", labels: {}, sum: 7211.0, count: 6696 }, SummaryPayload { name: "happy_eyeballs.conn_attempts_all", labels: {}, sum: 1.0, count: 1 }], gauges: [GaugesPayload { name: "happy_eyeballs.conn_attempts_current", labels: {}, value: 1.0 }, GaugesPayload { name: "ntp-clock.min_since_ntp_sync", labels: {}, value: 59.992769636 }] } context="timer"}: actor_statistics::handler: Starting to upload stats
2025-08-05T00:27:02.733Z  WARN upload_stats{stats=StatsPayload { counters: [CounterPayload { name: "main-loop-compat.commands_recv", labels: {}, count: 2 }, CounterPayload { name: "message_bus.bus_driver_updates_sent", labels: {}, count: 1 }, CounterPayload { name: "main-loop-compat.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "alternate-network.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "watchdog.requests_recv", labels: {}, count: 404350 }, CounterPayload { name: "message_bus.bus_driver_commands_sent", labels: {}, count: 4 }, CounterPayload { name: "registration-verifier.commands_recv", labels: {}, count: 1 }, CounterPayload { name: "statistics.commands_recv", labels: {}, count: 1 }, CounterPayload { name: "dns_proxy.doh_err_other", labels: {}, count: 3 }, CounterPayload { name: "device-state.updates_recv", labels: {}, count: 1 }], summaries: [SummaryPayload { name: "message_bus.bus_driver_request_duration_sec_nano_sec", labels: {}, sum: 16.220849373999652, count: 404350 }, SummaryPayload { name: "firewall.fw_avg_ms", labels: {}, sum: 274.0, count: 14 }, SummaryPayload { name: "dns_proxy.doh_avg_ms", labels: {}, sum: 7211.0, count: 6696 }, SummaryPayload { name: "happy_eyeballs.conn_attempts_all", labels: {}, sum: 1.0, count: 1 }], gauges: [GaugesPayload { name: "happy_eyeballs.conn_attempts_current", labels: {}, value: 1.0 }, GaugesPayload { name: "ntp-clock.min_since_ntp_sync", labels: {}, value: 59.992769636 }] } context="timer"}: actor_statistics::handler: Skipped uploading aggregate stats because registration was none!
2025-08-05T00:27:09.414Z DEBUG slog: got a response for stream_id=10788, headers=[":status: 200", "cf-team: 29193910e300000482d2898400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T00:27:09.487Z DEBUG tunnel_loop{protocol="masque" con_id="6d3539c6af1e789ae39aef05871da34927b6218a"}: warp_edge::tunnel_loop: Tunnel loop statistics stats=TunnelLoopStats { tun_packet_too_large: 0, edge_packet_too_large: 0, tun_packet_too_small: 0, edge_packet_too_small: 0, tun_packet_martian: 0, tun_packet_unhandled: 20, dropped_edge_packet: 0, dropped_tun_packet: 0, dropped_dns_packet: 0, successfully_retried_tun_packet: 0 }
2025-08-05T00:27:24.415Z DEBUG slog: got a response for stream_id=10792, headers=[":status: 200", "cf-team: 2919394b7c00000482d28e6400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T00:27:34.903Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
2025-08-05T00:27:39.414Z DEBUG slog: got a response for stream_id=10796, headers=[":status: 200", "cf-team: 291939861300000482d2972400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T00:27:40.531Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
NewNeighbour; Destination: **********; 
2025-08-05T00:27:54.414Z DEBUG slog: got a response for stream_id=10800, headers=[":status: 200", "cf-team: 291939c0ab00000482d2a81400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T00:28:09.414Z DEBUG slog: got a response for stream_id=10804, headers=[":status: 200", "cf-team: 291939fb4300000482d2b22400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T00:28:11.767Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
2025-08-05T00:28:20.723Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
NewNeighbour; Destination: **********; 
2025-08-05T00:28:24.413Z DEBUG slog: got a response for stream_id=10808, headers=[":status: 200", "cf-team: 29193a35db00000482d2be1400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T00:28:39.414Z DEBUG slog: got a response for stream_id=10812, headers=[":status: 200", "cf-team: 29193a707300000482d2c8b400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T00:28:50.675Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
2025-08-05T00:28:54.414Z DEBUG slog: got a response for stream_id=10816, headers=[":status: 200", "cf-team: 29193aab0c00000482d2cb2400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T00:28:56.563Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
NewNeighbour; Destination: **********; 
2025-08-05T00:29:02.737Z DEBUG upload_stats{stats=StatsPayload { counters: [CounterPayload { name: "device-state.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "main-loop-compat.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "alternate-network.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "message_bus.bus_driver_commands_sent", labels: {}, count: 4 }, CounterPayload { name: "dns_proxy.doh_err_other", labels: {}, count: 3 }, CounterPayload { name: "main-loop-compat.commands_recv", labels: {}, count: 2 }, CounterPayload { name: "watchdog.requests_recv", labels: {}, count: 405550 }, CounterPayload { name: "message_bus.bus_driver_updates_sent", labels: {}, count: 1 }, CounterPayload { name: "registration-verifier.commands_recv", labels: {}, count: 1 }, CounterPayload { name: "statistics.commands_recv", labels: {}, count: 1 }], summaries: [SummaryPayload { name: "happy_eyeballs.conn_attempts_all", labels: {}, sum: 1.0, count: 1 }, SummaryPayload { name: "firewall.fw_avg_ms", labels: {}, sum: 274.0, count: 14 }, SummaryPayload { name: "dns_proxy.doh_avg_ms", labels: {}, sum: 7211.0, count: 6716 }, SummaryPayload { name: "message_bus.bus_driver_request_duration_sec_nano_sec", labels: {}, sum: 16.260219844999646, count: 405550 }], gauges: [GaugesPayload { name: "happy_eyeballs.conn_attempts_current", labels: {}, value: 1.0 }, GaugesPayload { name: "ntp-clock.min_since_ntp_sync", labels: {}, value: 60.000596841 }] } context="timer"}: actor_statistics::handler: Starting to upload stats
2025-08-05T00:29:02.737Z  WARN upload_stats{stats=StatsPayload { counters: [CounterPayload { name: "device-state.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "main-loop-compat.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "alternate-network.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "message_bus.bus_driver_commands_sent", labels: {}, count: 4 }, CounterPayload { name: "dns_proxy.doh_err_other", labels: {}, count: 3 }, CounterPayload { name: "main-loop-compat.commands_recv", labels: {}, count: 2 }, CounterPayload { name: "watchdog.requests_recv", labels: {}, count: 405550 }, CounterPayload { name: "message_bus.bus_driver_updates_sent", labels: {}, count: 1 }, CounterPayload { name: "registration-verifier.commands_recv", labels: {}, count: 1 }, CounterPayload { name: "statistics.commands_recv", labels: {}, count: 1 }], summaries: [SummaryPayload { name: "happy_eyeballs.conn_attempts_all", labels: {}, sum: 1.0, count: 1 }, SummaryPayload { name: "firewall.fw_avg_ms", labels: {}, sum: 274.0, count: 14 }, SummaryPayload { name: "dns_proxy.doh_avg_ms", labels: {}, sum: 7211.0, count: 6716 }, SummaryPayload { name: "message_bus.bus_driver_request_duration_sec_nano_sec", labels: {}, sum: 16.260219844999646, count: 405550 }], gauges: [GaugesPayload { name: "happy_eyeballs.conn_attempts_current", labels: {}, value: 1.0 }, GaugesPayload { name: "ntp-clock.min_since_ntp_sync", labels: {}, value: 60.000596841 }] } context="timer"}: actor_statistics::handler: Skipped uploading aggregate stats because registration was none!
2025-08-05T00:29:09.414Z DEBUG slog: got a response for stream_id=10820, headers=[":status: 200", "cf-team: 29193ae5a300000482d2cde400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T00:29:09.487Z DEBUG tunnel_loop{protocol="masque" con_id="6d3539c6af1e789ae39aef05871da34927b6218a"}: warp_edge::tunnel_loop: Tunnel loop statistics stats=TunnelLoopStats { tun_packet_too_large: 0, edge_packet_too_large: 0, tun_packet_too_small: 0, edge_packet_too_small: 0, tun_packet_martian: 0, tun_packet_unhandled: 20, dropped_edge_packet: 0, dropped_tun_packet: 0, dropped_dns_packet: 0, successfully_retried_tun_packet: 0 }
2025-08-05T00:29:24.414Z DEBUG slog: got a response for stream_id=10824, headers=[":status: 200", "cf-team: 29193b203b00000482d2d28400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T00:29:27.543Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
2025-08-05T00:29:36.755Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
NewNeighbour; Destination: **********; 
2025-08-05T00:29:39.414Z DEBUG slog: got a response for stream_id=10828, headers=[":status: 200", "cf-team: 29193b5ad300000482d2d57400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T00:29:54.414Z DEBUG slog: got a response for stream_id=10832, headers=[":status: 200", "cf-team: 29193b956b00000482d2da4400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T00:30:08.499Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
2025-08-05T00:30:09.414Z DEBUG slog: got a response for stream_id=10836, headers=[":status: 200", "cf-team: 29193bd00300000482d2dd9400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T00:30:14.131Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
NewNeighbour; Destination: **********; 
2025-08-05T00:30:24.414Z DEBUG slog: got a response for stream_id=10840, headers=[":status: 200", "cf-team: 29193c0a9b00000482d2e0c400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T00:30:39.414Z DEBUG slog: got a response for stream_id=10844, headers=[":status: 200", "cf-team: 29193c453300000482d2e61400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T00:30:45.363Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
2025-08-05T00:30:52.531Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
NewNeighbour; Destination: **********; 
2025-08-05T00:30:54.413Z DEBUG slog: got a response for stream_id=10848, headers=[":status: 200", "cf-team: 29193c7fca00000482d2ea3400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T00:31:02.734Z DEBUG upload_stats{stats=StatsPayload { counters: [CounterPayload { name: "message_bus.bus_driver_updates_sent", labels: {}, count: 1 }, CounterPayload { name: "main-loop-compat.commands_recv", labels: {}, count: 2 }, CounterPayload { name: "message_bus.bus_driver_commands_sent", labels: {}, count: 4 }, CounterPayload { name: "main-loop-compat.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "watchdog.requests_recv", labels: {}, count: 406750 }, CounterPayload { name: "alternate-network.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "dns_proxy.doh_err_other", labels: {}, count: 3 }, CounterPayload { name: "registration-verifier.commands_recv", labels: {}, count: 1 }, CounterPayload { name: "statistics.commands_recv", labels: {}, count: 1 }, CounterPayload { name: "device-state.updates_recv", labels: {}, count: 1 }], summaries: [SummaryPayload { name: "dns_proxy.doh_avg_ms", labels: {}, sum: 7248.0, count: 6736 }, SummaryPayload { name: "happy_eyeballs.conn_attempts_all", labels: {}, sum: 1.0, count: 1 }, SummaryPayload { name: "firewall.fw_avg_ms", labels: {}, sum: 274.0, count: 14 }, SummaryPayload { name: "message_bus.bus_driver_request_duration_sec_nano_sec", labels: {}, sum: 16.303901629999654, count: 406750 }], gauges: [GaugesPayload { name: "ntp-clock.min_since_ntp_sync", labels: {}, value: 59.998754662 }, GaugesPayload { name: "happy_eyeballs.conn_attempts_current", labels: {}, value: 1.0 }] } context="timer"}: actor_statistics::handler: Starting to upload stats
2025-08-05T00:31:02.734Z  WARN upload_stats{stats=StatsPayload { counters: [CounterPayload { name: "message_bus.bus_driver_updates_sent", labels: {}, count: 1 }, CounterPayload { name: "main-loop-compat.commands_recv", labels: {}, count: 2 }, CounterPayload { name: "message_bus.bus_driver_commands_sent", labels: {}, count: 4 }, CounterPayload { name: "main-loop-compat.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "watchdog.requests_recv", labels: {}, count: 406750 }, CounterPayload { name: "alternate-network.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "dns_proxy.doh_err_other", labels: {}, count: 3 }, CounterPayload { name: "registration-verifier.commands_recv", labels: {}, count: 1 }, CounterPayload { name: "statistics.commands_recv", labels: {}, count: 1 }, CounterPayload { name: "device-state.updates_recv", labels: {}, count: 1 }], summaries: [SummaryPayload { name: "dns_proxy.doh_avg_ms", labels: {}, sum: 7248.0, count: 6736 }, SummaryPayload { name: "happy_eyeballs.conn_attempts_all", labels: {}, sum: 1.0, count: 1 }, SummaryPayload { name: "firewall.fw_avg_ms", labels: {}, sum: 274.0, count: 14 }, SummaryPayload { name: "message_bus.bus_driver_request_duration_sec_nano_sec", labels: {}, sum: 16.303901629999654, count: 406750 }], gauges: [GaugesPayload { name: "ntp-clock.min_since_ntp_sync", labels: {}, value: 59.998754662 }, GaugesPayload { name: "happy_eyeballs.conn_attempts_current", labels: {}, value: 1.0 }] } context="timer"}: actor_statistics::handler: Skipped uploading aggregate stats because registration was none!
2025-08-05T00:31:09.414Z DEBUG slog: got a response for stream_id=10852, headers=[":status: 200", "cf-team: 29193cba6300000482d2ecd400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T00:31:09.486Z DEBUG tunnel_loop{protocol="masque" con_id="6d3539c6af1e789ae39aef05871da34927b6218a"}: warp_edge::tunnel_loop: Tunnel loop statistics stats=TunnelLoopStats { tun_packet_too_large: 0, edge_packet_too_large: 0, tun_packet_too_small: 0, edge_packet_too_small: 0, tun_packet_martian: 0, tun_packet_unhandled: 20, dropped_edge_packet: 0, dropped_tun_packet: 0, dropped_dns_packet: 0, successfully_retried_tun_packet: 0 }
2025-08-05T00:31:24.275Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
2025-08-05T00:31:24.414Z DEBUG slog: got a response for stream_id=10856, headers=[":status: 200", "cf-team: 29193cf4fc00000482d2f0c400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T00:31:29.651Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
NewNeighbour; Destination: **********; 
2025-08-05T00:31:39.415Z DEBUG slog: got a response for stream_id=10860, headers=[":status: 200", "cf-team: 29193d2f9400000482d2f63400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T00:31:54.414Z DEBUG slog: got a response for stream_id=10864, headers=[":status: 200", "cf-team: 29193d6a2c00000482d2f98400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T00:32:01.139Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
2025-08-05T00:32:07.795Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
NewNeighbour; Destination: **********; 
2025-08-05T00:32:09.414Z DEBUG slog: got a response for stream_id=10868, headers=[":status: 200", "cf-team: 29193da4c300000482d300d400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T00:32:24.414Z DEBUG slog: got a response for stream_id=10872, headers=[":status: 200", "cf-team: 29193ddf5b00000482d305a400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T00:32:38.003Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
2025-08-05T00:32:39.414Z DEBUG slog: got a response for stream_id=10876, headers=[":status: 200", "cf-team: 29193e19f300000482d3081400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T00:32:44.659Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
NewNeighbour; Destination: **********; 
2025-08-05T00:32:54.415Z DEBUG slog: got a response for stream_id=10880, headers=[":status: 200", "cf-team: 29193e548b00000482d30ae400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T00:33:02.734Z DEBUG upload_stats{stats=StatsPayload { counters: [CounterPayload { name: "message_bus.bus_driver_commands_sent", labels: {}, count: 4 }, CounterPayload { name: "main-loop-compat.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "dns_proxy.doh_err_other", labels: {}, count: 3 }, CounterPayload { name: "device-state.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "main-loop-compat.commands_recv", labels: {}, count: 2 }, CounterPayload { name: "message_bus.bus_driver_updates_sent", labels: {}, count: 1 }, CounterPayload { name: "statistics.commands_recv", labels: {}, count: 1 }, CounterPayload { name: "watchdog.requests_recv", labels: {}, count: 407950 }, CounterPayload { name: "alternate-network.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "registration-verifier.commands_recv", labels: {}, count: 1 }], summaries: [SummaryPayload { name: "message_bus.bus_driver_request_duration_sec_nano_sec", labels: {}, sum: 16.346701585999636, count: 407950 }, SummaryPayload { name: "happy_eyeballs.conn_attempts_all", labels: {}, sum: 1.0, count: 1 }, SummaryPayload { name: "dns_proxy.doh_avg_ms", labels: {}, sum: 7248.0, count: 6756 }, SummaryPayload { name: "firewall.fw_avg_ms", labels: {}, sum: 274.0, count: 14 }], gauges: [GaugesPayload { name: "happy_eyeballs.conn_attempts_current", labels: {}, value: 1.0 }, GaugesPayload { name: "ntp-clock.min_since_ntp_sync", labels: {}, value: 60.000175689 }] } context="timer"}: actor_statistics::handler: Starting to upload stats
2025-08-05T00:33:02.734Z  WARN upload_stats{stats=StatsPayload { counters: [CounterPayload { name: "message_bus.bus_driver_commands_sent", labels: {}, count: 4 }, CounterPayload { name: "main-loop-compat.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "dns_proxy.doh_err_other", labels: {}, count: 3 }, CounterPayload { name: "device-state.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "main-loop-compat.commands_recv", labels: {}, count: 2 }, CounterPayload { name: "message_bus.bus_driver_updates_sent", labels: {}, count: 1 }, CounterPayload { name: "statistics.commands_recv", labels: {}, count: 1 }, CounterPayload { name: "watchdog.requests_recv", labels: {}, count: 407950 }, CounterPayload { name: "alternate-network.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "registration-verifier.commands_recv", labels: {}, count: 1 }], summaries: [SummaryPayload { name: "message_bus.bus_driver_request_duration_sec_nano_sec", labels: {}, sum: 16.346701585999636, count: 407950 }, SummaryPayload { name: "happy_eyeballs.conn_attempts_all", labels: {}, sum: 1.0, count: 1 }, SummaryPayload { name: "dns_proxy.doh_avg_ms", labels: {}, sum: 7248.0, count: 6756 }, SummaryPayload { name: "firewall.fw_avg_ms", labels: {}, sum: 274.0, count: 14 }], gauges: [GaugesPayload { name: "happy_eyeballs.conn_attempts_current", labels: {}, value: 1.0 }, GaugesPayload { name: "ntp-clock.min_since_ntp_sync", labels: {}, value: 60.000175689 }] } context="timer"}: actor_statistics::handler: Skipped uploading aggregate stats because registration was none!
2025-08-05T00:33:09.414Z DEBUG slog: got a response for stream_id=10884, headers=[":status: 200", "cf-team: 29193e8f2300000482d30fb400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T00:33:09.487Z DEBUG tunnel_loop{protocol="masque" con_id="6d3539c6af1e789ae39aef05871da34927b6218a"}: warp_edge::tunnel_loop: Tunnel loop statistics stats=TunnelLoopStats { tun_packet_too_large: 0, edge_packet_too_large: 0, tun_packet_too_small: 0, edge_packet_too_small: 0, tun_packet_martian: 0, tun_packet_unhandled: 20, dropped_edge_packet: 0, dropped_tun_packet: 0, dropped_dns_packet: 0, successfully_retried_tun_packet: 0 }
2025-08-05T00:33:10.771Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
2025-08-05T00:33:16.147Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
NewNeighbour; Destination: **********; 
2025-08-05T00:33:24.414Z DEBUG slog: got a response for stream_id=10888, headers=[":status: 200", "cf-team: 29193ec9bb00000482d315f400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T00:33:39.414Z DEBUG slog: got a response for stream_id=10892, headers=[":status: 200", "cf-team: 29193f045300000482d31a8400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T00:33:41.495Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
2025-08-05T00:33:46.611Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
NewNeighbour; Destination: **********; 
2025-08-05T00:33:54.414Z DEBUG slog: got a response for stream_id=10896, headers=[":status: 200", "cf-team: 29193f3eeb00000482d31d8400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T00:34:09.414Z DEBUG slog: got a response for stream_id=10900, headers=[":status: 200", "cf-team: 29193f798300000482d31fd400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T00:34:12.211Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
2025-08-05T00:34:20.659Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
NewNeighbour; Destination: **********; 
2025-08-05T00:34:24.414Z DEBUG slog: got a response for stream_id=10904, headers=[":status: 200", "cf-team: 29193fb41c00000482d3251400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T00:34:39.414Z DEBUG slog: got a response for stream_id=10908, headers=[":status: 200", "cf-team: 29193feeb300000482d3286400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T00:34:47.027Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
2025-08-05T00:34:52.659Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
NewNeighbour; Destination: **********; 
2025-08-05T00:34:54.414Z DEBUG slog: got a response for stream_id=10912, headers=[":status: 200", "cf-team: 291940294b00000482d32a3400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T00:35:02.736Z DEBUG upload_stats{stats=StatsPayload { counters: [CounterPayload { name: "main-loop-compat.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "registration-verifier.commands_recv", labels: {}, count: 1 }, CounterPayload { name: "message_bus.bus_driver_commands_sent", labels: {}, count: 4 }, CounterPayload { name: "statistics.commands_recv", labels: {}, count: 1 }, CounterPayload { name: "message_bus.bus_driver_updates_sent", labels: {}, count: 1 }, CounterPayload { name: "device-state.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "dns_proxy.doh_err_other", labels: {}, count: 3 }, CounterPayload { name: "alternate-network.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "main-loop-compat.commands_recv", labels: {}, count: 2 }, CounterPayload { name: "watchdog.requests_recv", labels: {}, count: 409150 }], summaries: [SummaryPayload { name: "happy_eyeballs.conn_attempts_all", labels: {}, sum: 1.0, count: 1 }, SummaryPayload { name: "message_bus.bus_driver_request_duration_sec_nano_sec", labels: {}, sum: 16.393178014999695, count: 409150 }, SummaryPayload { name: "firewall.fw_avg_ms", labels: {}, sum: 274.0, count: 14 }, SummaryPayload { name: "dns_proxy.doh_avg_ms", labels: {}, sum: 7248.0, count: 6776 }], gauges: [GaugesPayload { name: "ntp-clock.min_since_ntp_sync", labels: {}, value: 59.999046483 }, GaugesPayload { name: "happy_eyeballs.conn_attempts_current", labels: {}, value: 1.0 }] } context="timer"}: actor_statistics::handler: Starting to upload stats
2025-08-05T00:35:02.736Z  WARN upload_stats{stats=StatsPayload { counters: [CounterPayload { name: "main-loop-compat.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "registration-verifier.commands_recv", labels: {}, count: 1 }, CounterPayload { name: "message_bus.bus_driver_commands_sent", labels: {}, count: 4 }, CounterPayload { name: "statistics.commands_recv", labels: {}, count: 1 }, CounterPayload { name: "message_bus.bus_driver_updates_sent", labels: {}, count: 1 }, CounterPayload { name: "device-state.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "dns_proxy.doh_err_other", labels: {}, count: 3 }, CounterPayload { name: "alternate-network.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "main-loop-compat.commands_recv", labels: {}, count: 2 }, CounterPayload { name: "watchdog.requests_recv", labels: {}, count: 409150 }], summaries: [SummaryPayload { name: "happy_eyeballs.conn_attempts_all", labels: {}, sum: 1.0, count: 1 }, SummaryPayload { name: "message_bus.bus_driver_request_duration_sec_nano_sec", labels: {}, sum: 16.393178014999695, count: 409150 }, SummaryPayload { name: "firewall.fw_avg_ms", labels: {}, sum: 274.0, count: 14 }, SummaryPayload { name: "dns_proxy.doh_avg_ms", labels: {}, sum: 7248.0, count: 6776 }], gauges: [GaugesPayload { name: "ntp-clock.min_since_ntp_sync", labels: {}, value: 59.999046483 }, GaugesPayload { name: "happy_eyeballs.conn_attempts_current", labels: {}, value: 1.0 }] } context="timer"}: actor_statistics::handler: Skipped uploading aggregate stats because registration was none!
2025-08-05T00:35:09.414Z DEBUG slog: got a response for stream_id=10916, headers=[":status: 200", "cf-team: 29194063e400000482d32ee400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T00:35:09.487Z DEBUG tunnel_loop{protocol="masque" con_id="6d3539c6af1e789ae39aef05871da34927b6218a"}: warp_edge::tunnel_loop: Tunnel loop statistics stats=TunnelLoopStats { tun_packet_too_large: 0, edge_packet_too_large: 0, tun_packet_too_small: 0, edge_packet_too_small: 0, tun_packet_martian: 0, tun_packet_unhandled: 20, dropped_edge_packet: 0, dropped_tun_packet: 0, dropped_dns_packet: 0, successfully_retried_tun_packet: 0 }
2025-08-05T00:35:19.795Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
2025-08-05T00:35:24.414Z DEBUG slog: got a response for stream_id=10920, headers=[":status: 200", "cf-team: 2919409e7b00000482d3339400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T00:35:28.755Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
NewNeighbour; Destination: **********; 
2025-08-05T00:35:39.414Z DEBUG slog: got a response for stream_id=10924, headers=[":status: 200", "cf-team: 291940d91400000482d337b400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T00:35:54.414Z DEBUG slog: got a response for stream_id=10928, headers=[":status: 200", "cf-team: 29194113ac00000482d33bd400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T00:35:54.615Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
2025-08-05T00:36:00.755Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
2025-08-05T00:36:00.757Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
2025-08-05T00:36:09.414Z DEBUG slog: got a response for stream_id=10932, headers=[":status: 200", "cf-team: 2919414e4400000482d33e9400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T00:36:24.414Z DEBUG slog: got a response for stream_id=10936, headers=[":status: 200", "cf-team: 29194188dc00000482d3438400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T00:36:27.379Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
2025-08-05T00:36:32.759Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
NewNeighbour; Destination: **********; 
2025-08-05T00:36:39.413Z DEBUG slog: got a response for stream_id=10940, headers=[":status: 200", "cf-team: 291941c37400000482d3493400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T00:36:54.414Z DEBUG slog: got a response for stream_id=10944, headers=[":status: 200", "cf-team: 291941fe0d00000482d34b6400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T00:36:58.099Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
2025-08-05T00:37:02.733Z DEBUG upload_stats{stats=StatsPayload { counters: [CounterPayload { name: "dns_proxy.doh_err_other", labels: {}, count: 3 }, CounterPayload { name: "main-loop-compat.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "message_bus.bus_driver_updates_sent", labels: {}, count: 1 }, CounterPayload { name: "alternate-network.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "registration-verifier.commands_recv", labels: {}, count: 1 }, CounterPayload { name: "main-loop-compat.commands_recv", labels: {}, count: 2 }, CounterPayload { name: "message_bus.bus_driver_commands_sent", labels: {}, count: 4 }, CounterPayload { name: "statistics.commands_recv", labels: {}, count: 1 }, CounterPayload { name: "device-state.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "watchdog.requests_recv", labels: {}, count: 410350 }], summaries: [SummaryPayload { name: "firewall.fw_avg_ms", labels: {}, sum: 274.0, count: 14 }, SummaryPayload { name: "dns_proxy.doh_avg_ms", labels: {}, sum: 7285.0, count: 6796 }, SummaryPayload { name: "message_bus.bus_driver_request_duration_sec_nano_sec", labels: {}, sum: 16.439772384999653, count: 410350 }, SummaryPayload { name: "happy_eyeballs.conn_attempts_all", labels: {}, sum: 1.0, count: 1 }], gauges: [GaugesPayload { name: "ntp-clock.min_since_ntp_sync", labels: {}, value: 60.000303818 }, GaugesPayload { name: "happy_eyeballs.conn_attempts_current", labels: {}, value: 1.0 }] } context="timer"}: actor_statistics::handler: Starting to upload stats
2025-08-05T00:37:02.733Z  WARN upload_stats{stats=StatsPayload { counters: [CounterPayload { name: "dns_proxy.doh_err_other", labels: {}, count: 3 }, CounterPayload { name: "main-loop-compat.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "message_bus.bus_driver_updates_sent", labels: {}, count: 1 }, CounterPayload { name: "alternate-network.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "registration-verifier.commands_recv", labels: {}, count: 1 }, CounterPayload { name: "main-loop-compat.commands_recv", labels: {}, count: 2 }, CounterPayload { name: "message_bus.bus_driver_commands_sent", labels: {}, count: 4 }, CounterPayload { name: "statistics.commands_recv", labels: {}, count: 1 }, CounterPayload { name: "device-state.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "watchdog.requests_recv", labels: {}, count: 410350 }], summaries: [SummaryPayload { name: "firewall.fw_avg_ms", labels: {}, sum: 274.0, count: 14 }, SummaryPayload { name: "dns_proxy.doh_avg_ms", labels: {}, sum: 7285.0, count: 6796 }, SummaryPayload { name: "message_bus.bus_driver_request_duration_sec_nano_sec", labels: {}, sum: 16.439772384999653, count: 410350 }, SummaryPayload { name: "happy_eyeballs.conn_attempts_all", labels: {}, sum: 1.0, count: 1 }], gauges: [GaugesPayload { name: "ntp-clock.min_since_ntp_sync", labels: {}, value: 60.000303818 }, GaugesPayload { name: "happy_eyeballs.conn_attempts_current", labels: {}, value: 1.0 }] } context="timer"}: actor_statistics::handler: Skipped uploading aggregate stats because registration was none!
2025-08-05T00:37:03.987Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
NewNeighbour; Destination: **********; 
2025-08-05T00:37:09.414Z DEBUG slog: got a response for stream_id=10948, headers=[":status: 200", "cf-team: 29194238a500000482d34de400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T00:37:09.487Z DEBUG tunnel_loop{protocol="masque" con_id="6d3539c6af1e789ae39aef05871da34927b6218a"}: warp_edge::tunnel_loop: Tunnel loop statistics stats=TunnelLoopStats { tun_packet_too_large: 0, edge_packet_too_large: 0, tun_packet_too_small: 0, edge_packet_too_small: 0, tun_packet_martian: 0, tun_packet_unhandled: 20, dropped_edge_packet: 0, dropped_tun_packet: 0, dropped_dns_packet: 0, successfully_retried_tun_packet: 0 }
2025-08-05T00:37:24.414Z DEBUG slog: got a response for stream_id=10952, headers=[":status: 200", "cf-team: 291942733c00000482d34fd400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T00:37:30.867Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
2025-08-05T00:37:36.755Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
NewNeighbour; Destination: **********; 
2025-08-05T00:37:39.414Z DEBUG slog: got a response for stream_id=10956, headers=[":status: 200", "cf-team: 291942add400000482d3527400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T00:37:54.414Z DEBUG slog: got a response for stream_id=10960, headers=[":status: 200", "cf-team: 291942e86c00000482d358c400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T00:38:03.635Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
2025-08-05T00:38:09.415Z DEBUG slog: got a response for stream_id=10964, headers=[":status: 200", "cf-team: 291943230500000482d35a3400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T00:38:12.599Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
NewNeighbour; Destination: **********; 
2025-08-05T00:38:24.413Z DEBUG slog: got a response for stream_id=10968, headers=[":status: 200", "cf-team: 2919435d9c00000482d35ef400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T00:38:39.414Z DEBUG slog: got a response for stream_id=10972, headers=[":status: 200", "cf-team: 291943983400000482d361b400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T00:38:40.499Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
2025-08-05T00:38:48.691Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
NewNeighbour; Destination: **********; 
2025-08-05T00:38:54.414Z DEBUG slog: got a response for stream_id=10976, headers=[":status: 200", "cf-team: 291943d2cd00000482d3647400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T00:39:02.734Z DEBUG upload_stats{stats=StatsPayload { counters: [CounterPayload { name: "statistics.commands_recv", labels: {}, count: 1 }, CounterPayload { name: "registration-verifier.commands_recv", labels: {}, count: 1 }, CounterPayload { name: "main-loop-compat.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "dns_proxy.doh_err_other", labels: {}, count: 3 }, CounterPayload { name: "device-state.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "message_bus.bus_driver_commands_sent", labels: {}, count: 4 }, CounterPayload { name: "watchdog.requests_recv", labels: {}, count: 411550 }, CounterPayload { name: "message_bus.bus_driver_updates_sent", labels: {}, count: 1 }, CounterPayload { name: "main-loop-compat.commands_recv", labels: {}, count: 2 }, CounterPayload { name: "alternate-network.updates_recv", labels: {}, count: 1 }], summaries: [SummaryPayload { name: "happy_eyeballs.conn_attempts_all", labels: {}, sum: 1.0, count: 1 }, SummaryPayload { name: "message_bus.bus_driver_request_duration_sec_nano_sec", labels: {}, sum: 16.486009393999616, count: 411550 }, SummaryPayload { name: "firewall.fw_avg_ms", labels: {}, sum: 274.0, count: 14 }, SummaryPayload { name: "dns_proxy.doh_avg_ms", labels: {}, sum: 7292.0, count: 6816 }], gauges: [GaugesPayload { name: "ntp-clock.min_since_ntp_sync", labels: {}, value: 59.99911504 }, GaugesPayload { name: "happy_eyeballs.conn_attempts_current", labels: {}, value: 1.0 }] } context="timer"}: actor_statistics::handler: Starting to upload stats
2025-08-05T00:39:02.734Z  WARN upload_stats{stats=StatsPayload { counters: [CounterPayload { name: "statistics.commands_recv", labels: {}, count: 1 }, CounterPayload { name: "registration-verifier.commands_recv", labels: {}, count: 1 }, CounterPayload { name: "main-loop-compat.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "dns_proxy.doh_err_other", labels: {}, count: 3 }, CounterPayload { name: "device-state.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "message_bus.bus_driver_commands_sent", labels: {}, count: 4 }, CounterPayload { name: "watchdog.requests_recv", labels: {}, count: 411550 }, CounterPayload { name: "message_bus.bus_driver_updates_sent", labels: {}, count: 1 }, CounterPayload { name: "main-loop-compat.commands_recv", labels: {}, count: 2 }, CounterPayload { name: "alternate-network.updates_recv", labels: {}, count: 1 }], summaries: [SummaryPayload { name: "happy_eyeballs.conn_attempts_all", labels: {}, sum: 1.0, count: 1 }, SummaryPayload { name: "message_bus.bus_driver_request_duration_sec_nano_sec", labels: {}, sum: 16.486009393999616, count: 411550 }, SummaryPayload { name: "firewall.fw_avg_ms", labels: {}, sum: 274.0, count: 14 }, SummaryPayload { name: "dns_proxy.doh_avg_ms", labels: {}, sum: 7292.0, count: 6816 }], gauges: [GaugesPayload { name: "ntp-clock.min_since_ntp_sync", labels: {}, value: 59.99911504 }, GaugesPayload { name: "happy_eyeballs.conn_attempts_current", labels: {}, value: 1.0 }] } context="timer"}: actor_statistics::handler: Skipped uploading aggregate stats because registration was none!
2025-08-05T00:39:09.413Z DEBUG slog: got a response for stream_id=10980, headers=[":status: 200", "cf-team: 2919440d6400000482d367f400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T00:39:09.487Z DEBUG tunnel_loop{protocol="masque" con_id="6d3539c6af1e789ae39aef05871da34927b6218a"}: warp_edge::tunnel_loop: Tunnel loop statistics stats=TunnelLoopStats { tun_packet_too_large: 0, edge_packet_too_large: 0, tun_packet_too_small: 0, edge_packet_too_small: 0, tun_packet_martian: 0, tun_packet_unhandled: 20, dropped_edge_packet: 0, dropped_tun_packet: 0, dropped_dns_packet: 0, successfully_retried_tun_packet: 0 }
2025-08-05T00:39:15.315Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
2025-08-05T00:39:20.435Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
NewNeighbour; Destination: **********; 
2025-08-05T00:39:24.414Z DEBUG slog: got a response for stream_id=10984, headers=[":status: 200", "cf-team: 29194447fd00000482d36b6400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T00:39:39.414Z DEBUG slog: got a response for stream_id=10988, headers=[":status: 200", "cf-team: 291944829500000482d36d6400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T00:39:48.087Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
2025-08-05T00:39:54.414Z DEBUG slog: got a response for stream_id=10992, headers=[":status: 200", "cf-team: 291944bd2d00000482d3709400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T00:39:56.531Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
NewNeighbour; Destination: **********; 
2025-08-05T00:40:09.414Z DEBUG slog: got a response for stream_id=10996, headers=[":status: 200", "cf-team: 291944f7c500000482d3729400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T00:40:22.899Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
2025-08-05T00:40:24.413Z DEBUG slog: got a response for stream_id=11000, headers=[":status: 200", "cf-team: 291945325c00000482d3759400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T00:40:28.535Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
NewNeighbour; Destination: **********; 
2025-08-05T00:40:39.414Z DEBUG slog: got a response for stream_id=11004, headers=[":status: 200", "cf-team: 2919456cf500000482d378e400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T00:40:54.414Z DEBUG slog: got a response for stream_id=11008, headers=[":status: 200", "cf-team: 291945a78d00000482d37c2400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T00:40:55.667Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
2025-08-05T00:41:02.735Z DEBUG upload_stats{stats=StatsPayload { counters: [CounterPayload { name: "watchdog.requests_recv", labels: {}, count: 412750 }, CounterPayload { name: "dns_proxy.doh_err_other", labels: {}, count: 3 }, CounterPayload { name: "main-loop-compat.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "device-state.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "message_bus.bus_driver_updates_sent", labels: {}, count: 1 }, CounterPayload { name: "alternate-network.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "main-loop-compat.commands_recv", labels: {}, count: 2 }, CounterPayload { name: "statistics.commands_recv", labels: {}, count: 1 }, CounterPayload { name: "message_bus.bus_driver_commands_sent", labels: {}, count: 4 }, CounterPayload { name: "registration-verifier.commands_recv", labels: {}, count: 1 }], summaries: [SummaryPayload { name: "message_bus.bus_driver_request_duration_sec_nano_sec", labels: {}, sum: 16.532151594999597, count: 412750 }, SummaryPayload { name: "firewall.fw_avg_ms", labels: {}, sum: 274.0, count: 14 }, SummaryPayload { name: "happy_eyeballs.conn_attempts_all", labels: {}, sum: 1.0, count: 1 }, SummaryPayload { name: "dns_proxy.doh_avg_ms", labels: {}, sum: 7306.0, count: 6836 }], gauges: [GaugesPayload { name: "ntp-clock.min_since_ntp_sync", labels: {}, value: 60.005721131 }, GaugesPayload { name: "happy_eyeballs.conn_attempts_current", labels: {}, value: 1.0 }] } context="timer"}: actor_statistics::handler: Starting to upload stats
2025-08-05T00:41:02.735Z  WARN upload_stats{stats=StatsPayload { counters: [CounterPayload { name: "watchdog.requests_recv", labels: {}, count: 412750 }, CounterPayload { name: "dns_proxy.doh_err_other", labels: {}, count: 3 }, CounterPayload { name: "main-loop-compat.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "device-state.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "message_bus.bus_driver_updates_sent", labels: {}, count: 1 }, CounterPayload { name: "alternate-network.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "main-loop-compat.commands_recv", labels: {}, count: 2 }, CounterPayload { name: "statistics.commands_recv", labels: {}, count: 1 }, CounterPayload { name: "message_bus.bus_driver_commands_sent", labels: {}, count: 4 }, CounterPayload { name: "registration-verifier.commands_recv", labels: {}, count: 1 }], summaries: [SummaryPayload { name: "message_bus.bus_driver_request_duration_sec_nano_sec", labels: {}, sum: 16.532151594999597, count: 412750 }, SummaryPayload { name: "firewall.fw_avg_ms", labels: {}, sum: 274.0, count: 14 }, SummaryPayload { name: "happy_eyeballs.conn_attempts_all", labels: {}, sum: 1.0, count: 1 }, SummaryPayload { name: "dns_proxy.doh_avg_ms", labels: {}, sum: 7306.0, count: 6836 }], gauges: [GaugesPayload { name: "ntp-clock.min_since_ntp_sync", labels: {}, value: 60.005721131 }, GaugesPayload { name: "happy_eyeballs.conn_attempts_current", labels: {}, value: 1.0 }] } context="timer"}: actor_statistics::handler: Skipped uploading aggregate stats because registration was none!
2025-08-05T00:41:04.631Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
NewNeighbour; Destination: **********; 
2025-08-05T00:41:09.413Z DEBUG slog: got a response for stream_id=11012, headers=[":status: 200", "cf-team: 291945e22400000482d37f3400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T00:41:09.487Z DEBUG tunnel_loop{protocol="masque" con_id="6d3539c6af1e789ae39aef05871da34927b6218a"}: warp_edge::tunnel_loop: Tunnel loop statistics stats=TunnelLoopStats { tun_packet_too_large: 0, edge_packet_too_large: 0, tun_packet_too_small: 0, edge_packet_too_small: 0, tun_packet_martian: 0, tun_packet_unhandled: 20, dropped_edge_packet: 0, dropped_tun_packet: 0, dropped_dns_packet: 0, successfully_retried_tun_packet: 0 }
2025-08-05T00:41:24.414Z DEBUG slog: got a response for stream_id=11016, headers=[":status: 200", "cf-team: 2919461cbd00000482d3822400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T00:41:32.531Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
2025-08-05T00:41:39.414Z DEBUG slog: got a response for stream_id=11020, headers=[":status: 200", "cf-team: 291946575500000482d385f400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T00:41:40.723Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
NewNeighbour; Destination: **********; 
2025-08-05T00:41:54.414Z DEBUG slog: got a response for stream_id=11024, headers=[":status: 200", "cf-team: 29194691ee00000482d38b7400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T00:42:07.347Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
2025-08-05T00:42:09.414Z DEBUG slog: got a response for stream_id=11028, headers=[":status: 200", "cf-team: 291946cc8500000482d38df400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T00:42:12.723Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
NewNeighbour; Destination: **********; 
2025-08-05T00:42:24.414Z DEBUG slog: got a response for stream_id=11032, headers=[":status: 200", "cf-team: 291947071d00000482d3903400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T00:42:39.414Z DEBUG slog: got a response for stream_id=11036, headers=[":status: 200", "cf-team: 29194741b600000482d3944400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T00:42:40.115Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
2025-08-05T00:42:48.563Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
NewNeighbour; Destination: **********; 
2025-08-05T00:42:54.413Z DEBUG slog: got a response for stream_id=11040, headers=[":status: 200", "cf-team: 2919477c4d00000482d39be400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T00:43:02.734Z DEBUG upload_stats{stats=StatsPayload { counters: [CounterPayload { name: "watchdog.requests_recv", labels: {}, count: 413950 }, CounterPayload { name: "alternate-network.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "main-loop-compat.commands_recv", labels: {}, count: 2 }, CounterPayload { name: "message_bus.bus_driver_commands_sent", labels: {}, count: 4 }, CounterPayload { name: "device-state.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "message_bus.bus_driver_updates_sent", labels: {}, count: 1 }, CounterPayload { name: "main-loop-compat.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "statistics.commands_recv", labels: {}, count: 1 }, CounterPayload { name: "dns_proxy.doh_err_other", labels: {}, count: 3 }, CounterPayload { name: "registration-verifier.commands_recv", labels: {}, count: 1 }], summaries: [SummaryPayload { name: "firewall.fw_avg_ms", labels: {}, sum: 274.0, count: 14 }, SummaryPayload { name: "dns_proxy.doh_avg_ms", labels: {}, sum: 7321.0, count: 6854 }, SummaryPayload { name: "happy_eyeballs.conn_attempts_all", labels: {}, sum: 1.0, count: 1 }, SummaryPayload { name: "message_bus.bus_driver_request_duration_sec_nano_sec", labels: {}, sum: 16.57828997899967, count: 413950 }], gauges: [GaugesPayload { name: "happy_eyeballs.conn_attempts_current", labels: {}, value: 1.0 }, GaugesPayload { name: "ntp-clock.min_since_ntp_sync", labels: {}, value: 59.9930628 }] } context="timer"}: actor_statistics::handler: Starting to upload stats
2025-08-05T00:43:02.735Z  WARN upload_stats{stats=StatsPayload { counters: [CounterPayload { name: "watchdog.requests_recv", labels: {}, count: 413950 }, CounterPayload { name: "alternate-network.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "main-loop-compat.commands_recv", labels: {}, count: 2 }, CounterPayload { name: "message_bus.bus_driver_commands_sent", labels: {}, count: 4 }, CounterPayload { name: "device-state.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "message_bus.bus_driver_updates_sent", labels: {}, count: 1 }, CounterPayload { name: "main-loop-compat.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "statistics.commands_recv", labels: {}, count: 1 }, CounterPayload { name: "dns_proxy.doh_err_other", labels: {}, count: 3 }, CounterPayload { name: "registration-verifier.commands_recv", labels: {}, count: 1 }], summaries: [SummaryPayload { name: "firewall.fw_avg_ms", labels: {}, sum: 274.0, count: 14 }, SummaryPayload { name: "dns_proxy.doh_avg_ms", labels: {}, sum: 7321.0, count: 6854 }, SummaryPayload { name: "happy_eyeballs.conn_attempts_all", labels: {}, sum: 1.0, count: 1 }, SummaryPayload { name: "message_bus.bus_driver_request_duration_sec_nano_sec", labels: {}, sum: 16.57828997899967, count: 413950 }], gauges: [GaugesPayload { name: "happy_eyeballs.conn_attempts_current", labels: {}, value: 1.0 }, GaugesPayload { name: "ntp-clock.min_since_ntp_sync", labels: {}, value: 59.9930628 }] } context="timer"}: actor_statistics::handler: Skipped uploading aggregate stats because registration was none!
2025-08-05T00:43:09.414Z DEBUG slog: got a response for stream_id=11044, headers=[":status: 200", "cf-team: 291947b6e500000482d39ea400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T00:43:09.487Z DEBUG tunnel_loop{protocol="masque" con_id="6d3539c6af1e789ae39aef05871da34927b6218a"}: warp_edge::tunnel_loop: Tunnel loop statistics stats=TunnelLoopStats { tun_packet_too_large: 0, edge_packet_too_large: 0, tun_packet_too_small: 0, edge_packet_too_small: 0, tun_packet_martian: 0, tun_packet_unhandled: 20, dropped_edge_packet: 0, dropped_tun_packet: 0, dropped_dns_packet: 0, successfully_retried_tun_packet: 0 }
2025-08-05T00:43:14.931Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
2025-08-05T00:43:20.567Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
NewNeighbour; Destination: **********; 
2025-08-05T00:43:24.414Z DEBUG slog: got a response for stream_id=11048, headers=[":status: 200", "cf-team: 291947f17d00000482d3a23400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T00:43:39.414Z DEBUG slog: got a response for stream_id=11052, headers=[":status: 200", "cf-team: 2919482c1600000482d3a93400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T00:43:45.651Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
2025-08-05T00:43:52.563Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
2025-08-05T00:43:52.565Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
2025-08-05T00:43:54.414Z DEBUG slog: got a response for stream_id=11056, headers=[":status: 200", "cf-team: 29194866ad00000482d3ad9400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T00:44:09.414Z DEBUG slog: got a response for stream_id=11060, headers=[":status: 200", "cf-team: 291948a14600000482d3afb400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T00:44:18.419Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
2025-08-05T00:44:23.795Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
NewNeighbour; Destination: **********; 
2025-08-05T00:44:24.414Z DEBUG slog: got a response for stream_id=11064, headers=[":status: 200", "cf-team: 291948dbdd00000482d3b24400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T00:44:39.414Z DEBUG slog: got a response for stream_id=11068, headers=[":status: 200", "cf-team: 291949167500000482d3b68400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T00:44:49.139Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
2025-08-05T00:44:54.259Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
NewNeighbour; Destination: **********; 
2025-08-05T00:44:54.414Z DEBUG slog: got a response for stream_id=11072, headers=[":status: 200", "cf-team: 291949510d00000482d3b99400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T00:45:02.733Z DEBUG upload_stats{stats=StatsPayload { counters: [CounterPayload { name: "message_bus.bus_driver_updates_sent", labels: {}, count: 1 }, CounterPayload { name: "message_bus.bus_driver_commands_sent", labels: {}, count: 4 }, CounterPayload { name: "main-loop-compat.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "alternate-network.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "statistics.commands_recv", labels: {}, count: 1 }, CounterPayload { name: "registration-verifier.commands_recv", labels: {}, count: 1 }, CounterPayload { name: "watchdog.requests_recv", labels: {}, count: 415150 }, CounterPayload { name: "dns_proxy.doh_err_other", labels: {}, count: 3 }, CounterPayload { name: "device-state.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "main-loop-compat.commands_recv", labels: {}, count: 2 }], summaries: [SummaryPayload { name: "dns_proxy.doh_avg_ms", labels: {}, sum: 7321.0, count: 6874 }, SummaryPayload { name: "firewall.fw_avg_ms", labels: {}, sum: 274.0, count: 14 }, SummaryPayload { name: "message_bus.bus_driver_request_duration_sec_nano_sec", labels: {}, sum: 16.6249291069997, count: 415150 }, SummaryPayload { name: "happy_eyeballs.conn_attempts_all", labels: {}, sum: 1.0, count: 1 }], gauges: [GaugesPayload { name: "ntp-clock.min_since_ntp_sync", labels: {}, value: 59.999603282 }, GaugesPayload { name: "happy_eyeballs.conn_attempts_current", labels: {}, value: 1.0 }] } context="timer"}: actor_statistics::handler: Starting to upload stats
2025-08-05T00:45:02.733Z  WARN upload_stats{stats=StatsPayload { counters: [CounterPayload { name: "message_bus.bus_driver_updates_sent", labels: {}, count: 1 }, CounterPayload { name: "message_bus.bus_driver_commands_sent", labels: {}, count: 4 }, CounterPayload { name: "main-loop-compat.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "alternate-network.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "statistics.commands_recv", labels: {}, count: 1 }, CounterPayload { name: "registration-verifier.commands_recv", labels: {}, count: 1 }, CounterPayload { name: "watchdog.requests_recv", labels: {}, count: 415150 }, CounterPayload { name: "dns_proxy.doh_err_other", labels: {}, count: 3 }, CounterPayload { name: "device-state.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "main-loop-compat.commands_recv", labels: {}, count: 2 }], summaries: [SummaryPayload { name: "dns_proxy.doh_avg_ms", labels: {}, sum: 7321.0, count: 6874 }, SummaryPayload { name: "firewall.fw_avg_ms", labels: {}, sum: 274.0, count: 14 }, SummaryPayload { name: "message_bus.bus_driver_request_duration_sec_nano_sec", labels: {}, sum: 16.6249291069997, count: 415150 }, SummaryPayload { name: "happy_eyeballs.conn_attempts_all", labels: {}, sum: 1.0, count: 1 }], gauges: [GaugesPayload { name: "ntp-clock.min_since_ntp_sync", labels: {}, value: 59.999603282 }, GaugesPayload { name: "happy_eyeballs.conn_attempts_current", labels: {}, value: 1.0 }] } context="timer"}: actor_statistics::handler: Skipped uploading aggregate stats because registration was none!
2025-08-05T00:45:09.414Z DEBUG slog: got a response for stream_id=11076, headers=[":status: 200", "cf-team: 2919498ba600000482d3bdd400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T00:45:09.486Z DEBUG tunnel_loop{protocol="masque" con_id="6d3539c6af1e789ae39aef05871da34927b6218a"}: warp_edge::tunnel_loop: Tunnel loop statistics stats=TunnelLoopStats { tun_packet_too_large: 0, edge_packet_too_large: 0, tun_packet_too_small: 0, edge_packet_too_small: 0, tun_packet_martian: 0, tun_packet_unhandled: 20, dropped_edge_packet: 0, dropped_tun_packet: 0, dropped_dns_packet: 0, successfully_retried_tun_packet: 0 }
2025-08-05T00:45:19.859Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
2025-08-05T00:45:24.414Z DEBUG slog: got a response for stream_id=11080, headers=[":status: 200", "cf-team: 291949c63e00000482d3c5c400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T00:45:28.567Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
NewNeighbour; Destination: **********; 
2025-08-05T00:45:39.414Z DEBUG slog: got a response for stream_id=11084, headers=[":status: 200", "cf-team: 29194a00d500000482d3c9f400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T00:45:54.413Z DEBUG slog: got a response for stream_id=11088, headers=[":status: 200", "cf-team: 29194a3b6d00000482d3cd9400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T00:45:54.675Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
2025-08-05T00:46:00.563Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
NewNeighbour; Destination: **********; 
2025-08-05T00:46:09.414Z DEBUG slog: got a response for stream_id=11092, headers=[":status: 200", "cf-team: 29194a760600000482d3cf3400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T00:46:24.414Z DEBUG slog: got a response for stream_id=11096, headers=[":status: 200", "cf-team: 29194ab09d00000482d3d2e400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T00:46:27.443Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
2025-08-05T00:46:32.563Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
NewNeighbour; Destination: **********; 
2025-08-05T00:46:39.414Z DEBUG slog: got a response for stream_id=11100, headers=[":status: 200", "cf-team: 29194aeb3600000482d3d50400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T00:46:54.414Z DEBUG slog: got a response for stream_id=11104, headers=[":status: 200", "cf-team: 29194b25cd00000482d3d79400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T00:46:58.163Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
2025-08-05T00:47:02.734Z DEBUG upload_stats{stats=StatsPayload { counters: [CounterPayload { name: "main-loop-compat.commands_recv", labels: {}, count: 2 }, CounterPayload { name: "message_bus.bus_driver_commands_sent", labels: {}, count: 4 }, CounterPayload { name: "statistics.commands_recv", labels: {}, count: 1 }, CounterPayload { name: "registration-verifier.commands_recv", labels: {}, count: 1 }, CounterPayload { name: "main-loop-compat.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "message_bus.bus_driver_updates_sent", labels: {}, count: 1 }, CounterPayload { name: "watchdog.requests_recv", labels: {}, count: 416350 }, CounterPayload { name: "alternate-network.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "device-state.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "dns_proxy.doh_err_other", labels: {}, count: 3 }], summaries: [SummaryPayload { name: "dns_proxy.doh_avg_ms", labels: {}, sum: 7356.0, count: 6894 }, SummaryPayload { name: "happy_eyeballs.conn_attempts_all", labels: {}, sum: 1.0, count: 1 }, SummaryPayload { name: "firewall.fw_avg_ms", labels: {}, sum: 274.0, count: 14 }, SummaryPayload { name: "message_bus.bus_driver_request_duration_sec_nano_sec", labels: {}, sum: 16.671586134999714, count: 416350 }], gauges: [GaugesPayload { name: "happy_eyeballs.conn_attempts_current", labels: {}, value: 1.0 }, GaugesPayload { name: "ntp-clock.min_since_ntp_sync", labels: {}, value: 59.99956591 }] } context="timer"}: actor_statistics::handler: Starting to upload stats
2025-08-05T00:47:02.734Z  WARN upload_stats{stats=StatsPayload { counters: [CounterPayload { name: "main-loop-compat.commands_recv", labels: {}, count: 2 }, CounterPayload { name: "message_bus.bus_driver_commands_sent", labels: {}, count: 4 }, CounterPayload { name: "statistics.commands_recv", labels: {}, count: 1 }, CounterPayload { name: "registration-verifier.commands_recv", labels: {}, count: 1 }, CounterPayload { name: "main-loop-compat.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "message_bus.bus_driver_updates_sent", labels: {}, count: 1 }, CounterPayload { name: "watchdog.requests_recv", labels: {}, count: 416350 }, CounterPayload { name: "alternate-network.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "device-state.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "dns_proxy.doh_err_other", labels: {}, count: 3 }], summaries: [SummaryPayload { name: "dns_proxy.doh_avg_ms", labels: {}, sum: 7356.0, count: 6894 }, SummaryPayload { name: "happy_eyeballs.conn_attempts_all", labels: {}, sum: 1.0, count: 1 }, SummaryPayload { name: "firewall.fw_avg_ms", labels: {}, sum: 274.0, count: 14 }, SummaryPayload { name: "message_bus.bus_driver_request_duration_sec_nano_sec", labels: {}, sum: 16.671586134999714, count: 416350 }], gauges: [GaugesPayload { name: "happy_eyeballs.conn_attempts_current", labels: {}, value: 1.0 }, GaugesPayload { name: "ntp-clock.min_since_ntp_sync", labels: {}, value: 59.99956591 }] } context="timer"}: actor_statistics::handler: Skipped uploading aggregate stats because registration was none!
2025-08-05T00:47:04.563Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
NewNeighbour; Destination: **********; 
2025-08-05T00:47:09.414Z DEBUG slog: got a response for stream_id=11108, headers=[":status: 200", "cf-team: 29194b606600000482d3d9d400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T00:47:09.487Z DEBUG tunnel_loop{protocol="masque" con_id="6d3539c6af1e789ae39aef05871da34927b6218a"}: warp_edge::tunnel_loop: Tunnel loop statistics stats=TunnelLoopStats { tun_packet_too_large: 0, edge_packet_too_large: 0, tun_packet_too_small: 0, edge_packet_too_small: 0, tun_packet_martian: 0, tun_packet_unhandled: 20, dropped_edge_packet: 0, dropped_tun_packet: 0, dropped_dns_packet: 0, successfully_retried_tun_packet: 0 }
2025-08-05T00:47:24.414Z DEBUG slog: got a response for stream_id=11112, headers=[":status: 200", "cf-team: 29194b9afe00000482d3dcd400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T00:47:30.931Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
2025-08-05T00:47:36.563Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
2025-08-05T00:47:36.564Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
2025-08-05T00:47:39.414Z DEBUG slog: got a response for stream_id=11116, headers=[":status: 200", "cf-team: 29194bd59600000482d3e16400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T00:47:54.414Z DEBUG slog: got a response for stream_id=11120, headers=[":status: 200", "cf-team: 29194c102e00000482d3e30400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T00:48:03.699Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
2025-08-05T00:48:09.414Z DEBUG slog: got a response for stream_id=11124, headers=[":status: 200", "cf-team: 29194c4ac600000482d3e4f400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T00:48:11.379Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
NewNeighbour; Destination: **********; 
2025-08-05T00:48:24.414Z DEBUG slog: got a response for stream_id=11128, headers=[":status: 200", "cf-team: 29194c855d00000482d3e8c400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T00:48:38.515Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
2025-08-05T00:48:39.414Z DEBUG slog: got a response for stream_id=11132, headers=[":status: 200", "cf-team: 29194cbff600000482d3ecc400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T00:48:44.663Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
NewNeighbour; Destination: **********; 
2025-08-05T00:48:54.414Z DEBUG slog: got a response for stream_id=11136, headers=[":status: 200", "cf-team: 29194cfa8e00000482d3ef4400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T00:49:02.736Z DEBUG upload_stats{stats=StatsPayload { counters: [CounterPayload { name: "main-loop-compat.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "device-state.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "watchdog.requests_recv", labels: {}, count: 417550 }, CounterPayload { name: "message_bus.bus_driver_commands_sent", labels: {}, count: 4 }, CounterPayload { name: "dns_proxy.doh_err_other", labels: {}, count: 3 }, CounterPayload { name: "message_bus.bus_driver_updates_sent", labels: {}, count: 1 }, CounterPayload { name: "statistics.commands_recv", labels: {}, count: 1 }, CounterPayload { name: "registration-verifier.commands_recv", labels: {}, count: 1 }, CounterPayload { name: "main-loop-compat.commands_recv", labels: {}, count: 2 }, CounterPayload { name: "alternate-network.updates_recv", labels: {}, count: 1 }], summaries: [SummaryPayload { name: "dns_proxy.doh_avg_ms", labels: {}, sum: 7356.0, count: 6914 }, SummaryPayload { name: "happy_eyeballs.conn_attempts_all", labels: {}, sum: 1.0, count: 1 }, SummaryPayload { name: "firewall.fw_avg_ms", labels: {}, sum: 274.0, count: 14 }, SummaryPayload { name: "message_bus.bus_driver_request_duration_sec_nano_sec", labels: {}, sum: 16.717893108999807, count: 417550 }], gauges: [GaugesPayload { name: "ntp-clock.min_since_ntp_sync", labels: {}, value: 60.000466401 }, GaugesPayload { name: "happy_eyeballs.conn_attempts_current", labels: {}, value: 1.0 }] } context="timer"}: actor_statistics::handler: Starting to upload stats
2025-08-05T00:49:02.736Z  WARN upload_stats{stats=StatsPayload { counters: [CounterPayload { name: "main-loop-compat.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "device-state.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "watchdog.requests_recv", labels: {}, count: 417550 }, CounterPayload { name: "message_bus.bus_driver_commands_sent", labels: {}, count: 4 }, CounterPayload { name: "dns_proxy.doh_err_other", labels: {}, count: 3 }, CounterPayload { name: "message_bus.bus_driver_updates_sent", labels: {}, count: 1 }, CounterPayload { name: "statistics.commands_recv", labels: {}, count: 1 }, CounterPayload { name: "registration-verifier.commands_recv", labels: {}, count: 1 }, CounterPayload { name: "main-loop-compat.commands_recv", labels: {}, count: 2 }, CounterPayload { name: "alternate-network.updates_recv", labels: {}, count: 1 }], summaries: [SummaryPayload { name: "dns_proxy.doh_avg_ms", labels: {}, sum: 7356.0, count: 6914 }, SummaryPayload { name: "happy_eyeballs.conn_attempts_all", labels: {}, sum: 1.0, count: 1 }, SummaryPayload { name: "firewall.fw_avg_ms", labels: {}, sum: 274.0, count: 14 }, SummaryPayload { name: "message_bus.bus_driver_request_duration_sec_nano_sec", labels: {}, sum: 16.717893108999807, count: 417550 }], gauges: [GaugesPayload { name: "ntp-clock.min_since_ntp_sync", labels: {}, value: 60.000466401 }, GaugesPayload { name: "happy_eyeballs.conn_attempts_current", labels: {}, value: 1.0 }] } context="timer"}: actor_statistics::handler: Skipped uploading aggregate stats because registration was none!
2025-08-05T00:49:09.414Z DEBUG slog: got a response for stream_id=11140, headers=[":status: 200", "cf-team: 29194d352600000482d3f24400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T00:49:09.487Z DEBUG tunnel_loop{protocol="masque" con_id="6d3539c6af1e789ae39aef05871da34927b6218a"}: warp_edge::tunnel_loop: Tunnel loop statistics stats=TunnelLoopStats { tun_packet_too_large: 0, edge_packet_too_large: 0, tun_packet_too_small: 0, edge_packet_too_small: 0, tun_packet_martian: 0, tun_packet_unhandled: 20, dropped_edge_packet: 0, dropped_tun_packet: 0, dropped_dns_packet: 0, successfully_retried_tun_packet: 0 }
2025-08-05T00:49:11.283Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
2025-08-05T00:49:16.659Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
NewNeighbour; Destination: **********; 
2025-08-05T00:49:24.414Z DEBUG slog: got a response for stream_id=11144, headers=[":status: 200", "cf-team: 29194d6fbe00000482d3f46400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T00:49:39.415Z DEBUG slog: got a response for stream_id=11148, headers=[":status: 200", "cf-team: 29194daa5500000482d3f6b400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T00:49:44.051Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
2025-08-05T00:49:52.755Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
NewNeighbour; Destination: **********; 
2025-08-05T00:49:54.414Z DEBUG slog: got a response for stream_id=11152, headers=[":status: 200", "cf-team: 29194de4ee00000482d3fe3400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T00:50:09.414Z DEBUG slog: got a response for stream_id=11156, headers=[":status: 200", "cf-team: 29194e1f8600000482d400a400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T00:50:18.867Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
2025-08-05T00:50:24.414Z DEBUG slog: got a response for stream_id=11160, headers=[":status: 200", "cf-team: 29194e5a1e00000482d4040400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T00:50:24.755Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
NewNeighbour; Destination: **********; 
2025-08-05T00:50:39.414Z DEBUG slog: got a response for stream_id=11164, headers=[":status: 200", "cf-team: 29194e94b600000482d406b400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T00:50:51.639Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
2025-08-05T00:50:54.414Z DEBUG slog: got a response for stream_id=11168, headers=[":status: 200", "cf-team: 29194ecf4e00000482d40a0400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T00:50:58.295Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
NewNeighbour; Destination: **********; 
2025-08-05T00:51:02.736Z DEBUG upload_stats{stats=StatsPayload { counters: [CounterPayload { name: "statistics.commands_recv", labels: {}, count: 1 }, CounterPayload { name: "main-loop-compat.commands_recv", labels: {}, count: 2 }, CounterPayload { name: "message_bus.bus_driver_updates_sent", labels: {}, count: 1 }, CounterPayload { name: "alternate-network.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "main-loop-compat.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "message_bus.bus_driver_commands_sent", labels: {}, count: 4 }, CounterPayload { name: "registration-verifier.commands_recv", labels: {}, count: 1 }, CounterPayload { name: "dns_proxy.doh_err_other", labels: {}, count: 3 }, CounterPayload { name: "watchdog.requests_recv", labels: {}, count: 418750 }, CounterPayload { name: "device-state.updates_recv", labels: {}, count: 1 }], summaries: [SummaryPayload { name: "firewall.fw_avg_ms", labels: {}, sum: 274.0, count: 14 }, SummaryPayload { name: "happy_eyeballs.conn_attempts_all", labels: {}, sum: 1.0, count: 1 }, SummaryPayload { name: "message_bus.bus_driver_request_duration_sec_nano_sec", labels: {}, sum: 16.76449528199979, count: 418750 }, SummaryPayload { name: "dns_proxy.doh_avg_ms", labels: {}, sum: 7377.0, count: 6934 }], gauges: [GaugesPayload { name: "happy_eyeballs.conn_attempts_current", labels: {}, value: 1.0 }, GaugesPayload { name: "ntp-clock.min_since_ntp_sync", labels: {}, value: 60.006629377 }] } context="timer"}: actor_statistics::handler: Starting to upload stats
2025-08-05T00:51:02.736Z  WARN upload_stats{stats=StatsPayload { counters: [CounterPayload { name: "statistics.commands_recv", labels: {}, count: 1 }, CounterPayload { name: "main-loop-compat.commands_recv", labels: {}, count: 2 }, CounterPayload { name: "message_bus.bus_driver_updates_sent", labels: {}, count: 1 }, CounterPayload { name: "alternate-network.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "main-loop-compat.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "message_bus.bus_driver_commands_sent", labels: {}, count: 4 }, CounterPayload { name: "registration-verifier.commands_recv", labels: {}, count: 1 }, CounterPayload { name: "dns_proxy.doh_err_other", labels: {}, count: 3 }, CounterPayload { name: "watchdog.requests_recv", labels: {}, count: 418750 }, CounterPayload { name: "device-state.updates_recv", labels: {}, count: 1 }], summaries: [SummaryPayload { name: "firewall.fw_avg_ms", labels: {}, sum: 274.0, count: 14 }, SummaryPayload { name: "happy_eyeballs.conn_attempts_all", labels: {}, sum: 1.0, count: 1 }, SummaryPayload { name: "message_bus.bus_driver_request_duration_sec_nano_sec", labels: {}, sum: 16.76449528199979, count: 418750 }, SummaryPayload { name: "dns_proxy.doh_avg_ms", labels: {}, sum: 7377.0, count: 6934 }], gauges: [GaugesPayload { name: "happy_eyeballs.conn_attempts_current", labels: {}, value: 1.0 }, GaugesPayload { name: "ntp-clock.min_since_ntp_sync", labels: {}, value: 60.006629377 }] } context="timer"}: actor_statistics::handler: Skipped uploading aggregate stats because registration was none!
2025-08-05T00:51:09.414Z DEBUG slog: got a response for stream_id=11172, headers=[":status: 200", "cf-team: 29194f09e600000482d40d4400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T00:51:09.486Z DEBUG tunnel_loop{protocol="masque" con_id="6d3539c6af1e789ae39aef05871da34927b6218a"}: warp_edge::tunnel_loop: Tunnel loop statistics stats=TunnelLoopStats { tun_packet_too_large: 0, edge_packet_too_large: 0, tun_packet_too_small: 0, edge_packet_too_small: 0, tun_packet_martian: 0, tun_packet_unhandled: 20, dropped_edge_packet: 0, dropped_tun_packet: 0, dropped_dns_packet: 0, successfully_retried_tun_packet: 0 }
2025-08-05T00:51:24.403Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
2025-08-05T00:51:24.413Z DEBUG slog: got a response for stream_id=11176, headers=[":status: 200", "cf-team: 29194f447e00000482d4104400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T00:51:29.523Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
NewNeighbour; Destination: **********; 
2025-08-05T00:51:39.414Z DEBUG slog: got a response for stream_id=11180, headers=[":status: 200", "cf-team: 29194f7f1600000482d4125400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T00:51:54.414Z DEBUG slog: got a response for stream_id=11184, headers=[":status: 200", "cf-team: 29194fb9ae00000482d4172400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T00:51:57.171Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
2025-08-05T00:52:04.599Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
NewNeighbour; Destination: **********; 
2025-08-05T00:52:09.414Z DEBUG slog: got a response for stream_id=11188, headers=[":status: 200", "cf-team: 29194ff44600000482d419b400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T00:52:24.414Z DEBUG slog: got a response for stream_id=11192, headers=[":status: 200", "cf-team: 2919502ede00000482d41b6400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T00:52:36.851Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
2025-08-05T00:52:39.414Z DEBUG slog: got a response for stream_id=11196, headers=[":status: 200", "cf-team: 291950697600000482d41e7400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T00:52:44.535Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
NewNeighbour; Destination: **********; 
2025-08-05T00:52:54.414Z DEBUG slog: got a response for stream_id=11200, headers=[":status: 200", "cf-team: 291950a40e00000482d4212400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T00:53:02.733Z DEBUG upload_stats{stats=StatsPayload { counters: [CounterPayload { name: "device-state.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "message_bus.bus_driver_commands_sent", labels: {}, count: 4 }, CounterPayload { name: "watchdog.requests_recv", labels: {}, count: 419950 }, CounterPayload { name: "alternate-network.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "statistics.commands_recv", labels: {}, count: 1 }, CounterPayload { name: "dns_proxy.doh_err_other", labels: {}, count: 3 }, CounterPayload { name: "main-loop-compat.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "message_bus.bus_driver_updates_sent", labels: {}, count: 1 }, CounterPayload { name: "registration-verifier.commands_recv", labels: {}, count: 1 }, CounterPayload { name: "main-loop-compat.commands_recv", labels: {}, count: 2 }], summaries: [SummaryPayload { name: "dns_proxy.doh_avg_ms", labels: {}, sum: 7400.0, count: 6954 }, SummaryPayload { name: "happy_eyeballs.conn_attempts_all", labels: {}, sum: 1.0, count: 1 }, SummaryPayload { name: "firewall.fw_avg_ms", labels: {}, sum: 274.0, count: 14 }, SummaryPayload { name: "message_bus.bus_driver_request_duration_sec_nano_sec", labels: {}, sum: 16.8103229039998, count: 419950 }], gauges: [GaugesPayload { name: "ntp-clock.min_since_ntp_sync", labels: {}, value: 59.992450111 }, GaugesPayload { name: "happy_eyeballs.conn_attempts_current", labels: {}, value: 1.0 }] } context="timer"}: actor_statistics::handler: Starting to upload stats
2025-08-05T00:53:02.733Z  WARN upload_stats{stats=StatsPayload { counters: [CounterPayload { name: "device-state.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "message_bus.bus_driver_commands_sent", labels: {}, count: 4 }, CounterPayload { name: "watchdog.requests_recv", labels: {}, count: 419950 }, CounterPayload { name: "alternate-network.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "statistics.commands_recv", labels: {}, count: 1 }, CounterPayload { name: "dns_proxy.doh_err_other", labels: {}, count: 3 }, CounterPayload { name: "main-loop-compat.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "message_bus.bus_driver_updates_sent", labels: {}, count: 1 }, CounterPayload { name: "registration-verifier.commands_recv", labels: {}, count: 1 }, CounterPayload { name: "main-loop-compat.commands_recv", labels: {}, count: 2 }], summaries: [SummaryPayload { name: "dns_proxy.doh_avg_ms", labels: {}, sum: 7400.0, count: 6954 }, SummaryPayload { name: "happy_eyeballs.conn_attempts_all", labels: {}, sum: 1.0, count: 1 }, SummaryPayload { name: "firewall.fw_avg_ms", labels: {}, sum: 274.0, count: 14 }, SummaryPayload { name: "message_bus.bus_driver_request_duration_sec_nano_sec", labels: {}, sum: 16.8103229039998, count: 419950 }], gauges: [GaugesPayload { name: "ntp-clock.min_since_ntp_sync", labels: {}, value: 59.992450111 }, GaugesPayload { name: "happy_eyeballs.conn_attempts_current", labels: {}, value: 1.0 }] } context="timer"}: actor_statistics::handler: Skipped uploading aggregate stats because registration was none!
2025-08-05T00:53:09.414Z DEBUG slog: got a response for stream_id=11204, headers=[":status: 200", "cf-team: 291950dea600000482d4230400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T00:53:09.486Z DEBUG tunnel_loop{protocol="masque" con_id="6d3539c6af1e789ae39aef05871da34927b6218a"}: warp_edge::tunnel_loop: Tunnel loop statistics stats=TunnelLoopStats { tun_packet_too_large: 0, edge_packet_too_large: 0, tun_packet_too_small: 0, edge_packet_too_small: 0, tun_packet_martian: 0, tun_packet_unhandled: 20, dropped_edge_packet: 0, dropped_tun_packet: 0, dropped_dns_packet: 0, successfully_retried_tun_packet: 0 }
2025-08-05T00:53:17.043Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
2025-08-05T00:53:24.414Z DEBUG slog: got a response for stream_id=11208, headers=[":status: 200", "cf-team: 291951193e00000482d427a400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T00:53:24.723Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
NewNeighbour; Destination: **********; 
2025-08-05T00:53:39.414Z DEBUG slog: got a response for stream_id=11212, headers=[":status: 200", "cf-team: 29195153d600000482d42b9400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T00:53:54.414Z DEBUG slog: got a response for stream_id=11216, headers=[":status: 200", "cf-team: 2919518e6e00000482d430a400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T00:53:58.003Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
2025-08-05T00:54:04.659Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
NewNeighbour; Destination: **********; 
2025-08-05T00:54:09.414Z DEBUG slog: got a response for stream_id=11220, headers=[":status: 200", "cf-team: 291951c90600000482d4372400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T00:54:24.414Z DEBUG slog: got a response for stream_id=11224, headers=[":status: 200", "cf-team: 291952039e00000482d438e400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T00:54:36.919Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
2025-08-05T00:54:39.414Z DEBUG slog: got a response for stream_id=11228, headers=[":status: 200", "cf-team: 2919523e3600000482d43d5400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T00:54:44.595Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
NewNeighbour; Destination: **********; 
2025-08-05T00:54:54.414Z DEBUG slog: got a response for stream_id=11232, headers=[":status: 200", "cf-team: 29195278cf00000482d43f4400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T00:55:02.737Z DEBUG upload_stats{stats=StatsPayload { counters: [CounterPayload { name: "statistics.commands_recv", labels: {}, count: 1 }, CounterPayload { name: "message_bus.bus_driver_commands_sent", labels: {}, count: 4 }, CounterPayload { name: "registration-verifier.commands_recv", labels: {}, count: 1 }, CounterPayload { name: "dns_proxy.doh_err_other", labels: {}, count: 3 }, CounterPayload { name: "watchdog.requests_recv", labels: {}, count: 421150 }, CounterPayload { name: "device-state.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "main-loop-compat.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "alternate-network.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "main-loop-compat.commands_recv", labels: {}, count: 2 }, CounterPayload { name: "message_bus.bus_driver_updates_sent", labels: {}, count: 1 }], summaries: [SummaryPayload { name: "firewall.fw_avg_ms", labels: {}, sum: 274.0, count: 14 }, SummaryPayload { name: "dns_proxy.doh_avg_ms", labels: {}, sum: 7400.0, count: 6974 }, SummaryPayload { name: "happy_eyeballs.conn_attempts_all", labels: {}, sum: 1.0, count: 1 }, SummaryPayload { name: "message_bus.bus_driver_request_duration_sec_nano_sec", labels: {}, sum: 16.856847921999822, count: 421150 }], gauges: [GaugesPayload { name: "happy_eyeballs.conn_attempts_current", labels: {}, value: 1.0 }, GaugesPayload { name: "ntp-clock.min_since_ntp_sync", labels: {}, value: 59.999756986 }] } context="timer"}: actor_statistics::handler: Starting to upload stats
2025-08-05T00:55:02.737Z  WARN upload_stats{stats=StatsPayload { counters: [CounterPayload { name: "statistics.commands_recv", labels: {}, count: 1 }, CounterPayload { name: "message_bus.bus_driver_commands_sent", labels: {}, count: 4 }, CounterPayload { name: "registration-verifier.commands_recv", labels: {}, count: 1 }, CounterPayload { name: "dns_proxy.doh_err_other", labels: {}, count: 3 }, CounterPayload { name: "watchdog.requests_recv", labels: {}, count: 421150 }, CounterPayload { name: "device-state.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "main-loop-compat.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "alternate-network.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "main-loop-compat.commands_recv", labels: {}, count: 2 }, CounterPayload { name: "message_bus.bus_driver_updates_sent", labels: {}, count: 1 }], summaries: [SummaryPayload { name: "firewall.fw_avg_ms", labels: {}, sum: 274.0, count: 14 }, SummaryPayload { name: "dns_proxy.doh_avg_ms", labels: {}, sum: 7400.0, count: 6974 }, SummaryPayload { name: "happy_eyeballs.conn_attempts_all", labels: {}, sum: 1.0, count: 1 }, SummaryPayload { name: "message_bus.bus_driver_request_duration_sec_nano_sec", labels: {}, sum: 16.856847921999822, count: 421150 }], gauges: [GaugesPayload { name: "happy_eyeballs.conn_attempts_current", labels: {}, value: 1.0 }, GaugesPayload { name: "ntp-clock.min_since_ntp_sync", labels: {}, value: 59.999756986 }] } context="timer"}: actor_statistics::handler: Skipped uploading aggregate stats because registration was none!
2025-08-05T00:55:09.414Z DEBUG slog: got a response for stream_id=11236, headers=[":status: 200", "cf-team: 291952b36600000482d440f400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T00:55:09.486Z DEBUG tunnel_loop{protocol="masque" con_id="6d3539c6af1e789ae39aef05871da34927b6218a"}: warp_edge::tunnel_loop: Tunnel loop statistics stats=TunnelLoopStats { tun_packet_too_large: 0, edge_packet_too_large: 0, tun_packet_too_small: 0, edge_packet_too_small: 0, tun_packet_martian: 0, tun_packet_unhandled: 20, dropped_edge_packet: 0, dropped_tun_packet: 0, dropped_dns_packet: 0, successfully_retried_tun_packet: 0 }
2025-08-05T00:55:17.879Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
2025-08-05T00:55:24.414Z DEBUG slog: got a response for stream_id=11240, headers=[":status: 200", "cf-team: 291952edfe00000482d4458400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T00:55:24.531Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
NewNeighbour; Destination: **********; 
2025-08-05T00:55:39.413Z DEBUG slog: got a response for stream_id=11244, headers=[":status: 200", "cf-team: 291953289600000482d4490400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T00:55:54.413Z DEBUG slog: got a response for stream_id=11248, headers=[":status: 200", "cf-team: 291953632e00000482d44af400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T00:55:56.787Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
2025-08-05T00:56:04.723Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
NewNeighbour; Destination: **********; 
2025-08-05T00:56:09.414Z DEBUG slog: got a response for stream_id=11252, headers=[":status: 200", "cf-team: 2919539dc600000482d4557400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T00:56:24.414Z DEBUG slog: got a response for stream_id=11256, headers=[":status: 200", "cf-team: 291953d85e00000482d4601400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T00:56:37.747Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
2025-08-05T00:56:39.414Z DEBUG slog: got a response for stream_id=11260, headers=[":status: 200", "cf-team: 29195412f600000482d4640400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T00:56:44.659Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
NewNeighbour; Destination: **********; 
2025-08-05T00:56:54.414Z DEBUG slog: got a response for stream_id=11264, headers=[":status: 200", "cf-team: 2919544d8e00000482d4707400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T00:57:02.734Z DEBUG upload_stats{stats=StatsPayload { counters: [CounterPayload { name: "watchdog.requests_recv", labels: {}, count: 422350 }, CounterPayload { name: "message_bus.bus_driver_updates_sent", labels: {}, count: 1 }, CounterPayload { name: "statistics.commands_recv", labels: {}, count: 1 }, CounterPayload { name: "dns_proxy.doh_err_other", labels: {}, count: 3 }, CounterPayload { name: "registration-verifier.commands_recv", labels: {}, count: 1 }, CounterPayload { name: "alternate-network.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "main-loop-compat.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "device-state.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "main-loop-compat.commands_recv", labels: {}, count: 2 }, CounterPayload { name: "message_bus.bus_driver_commands_sent", labels: {}, count: 4 }], summaries: [SummaryPayload { name: "firewall.fw_avg_ms", labels: {}, sum: 274.0, count: 14 }, SummaryPayload { name: "happy_eyeballs.conn_attempts_all", labels: {}, sum: 1.0, count: 1 }, SummaryPayload { name: "dns_proxy.doh_avg_ms", labels: {}, sum: 7442.0, count: 6994 }, SummaryPayload { name: "message_bus.bus_driver_request_duration_sec_nano_sec", labels: {}, sum: 16.903797788999814, count: 422350 }], gauges: [GaugesPayload { name: "happy_eyeballs.conn_attempts_current", labels: {}, value: 1.0 }, GaugesPayload { name: "ntp-clock.min_since_ntp_sync", labels: {}, value: 60.005806602 }] } context="timer"}: actor_statistics::handler: Starting to upload stats
2025-08-05T00:57:02.734Z  WARN upload_stats{stats=StatsPayload { counters: [CounterPayload { name: "watchdog.requests_recv", labels: {}, count: 422350 }, CounterPayload { name: "message_bus.bus_driver_updates_sent", labels: {}, count: 1 }, CounterPayload { name: "statistics.commands_recv", labels: {}, count: 1 }, CounterPayload { name: "dns_proxy.doh_err_other", labels: {}, count: 3 }, CounterPayload { name: "registration-verifier.commands_recv", labels: {}, count: 1 }, CounterPayload { name: "alternate-network.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "main-loop-compat.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "device-state.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "main-loop-compat.commands_recv", labels: {}, count: 2 }, CounterPayload { name: "message_bus.bus_driver_commands_sent", labels: {}, count: 4 }], summaries: [SummaryPayload { name: "firewall.fw_avg_ms", labels: {}, sum: 274.0, count: 14 }, SummaryPayload { name: "happy_eyeballs.conn_attempts_all", labels: {}, sum: 1.0, count: 1 }, SummaryPayload { name: "dns_proxy.doh_avg_ms", labels: {}, sum: 7442.0, count: 6994 }, SummaryPayload { name: "message_bus.bus_driver_request_duration_sec_nano_sec", labels: {}, sum: 16.903797788999814, count: 422350 }], gauges: [GaugesPayload { name: "happy_eyeballs.conn_attempts_current", labels: {}, value: 1.0 }, GaugesPayload { name: "ntp-clock.min_since_ntp_sync", labels: {}, value: 60.005806602 }] } context="timer"}: actor_statistics::handler: Skipped uploading aggregate stats because registration was none!
2025-08-05T00:57:09.414Z DEBUG slog: got a response for stream_id=11268, headers=[":status: 200", "cf-team: 291954882600000482d4748400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T00:57:09.486Z DEBUG tunnel_loop{protocol="masque" con_id="6d3539c6af1e789ae39aef05871da34927b6218a"}: warp_edge::tunnel_loop: Tunnel loop statistics stats=TunnelLoopStats { tun_packet_too_large: 0, edge_packet_too_large: 0, tun_packet_too_small: 0, edge_packet_too_small: 0, tun_packet_martian: 0, tun_packet_unhandled: 20, dropped_edge_packet: 0, dropped_tun_packet: 0, dropped_dns_packet: 0, successfully_retried_tun_packet: 0 }
2025-08-05T00:57:18.707Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
2025-08-05T00:57:24.414Z DEBUG slog: got a response for stream_id=11272, headers=[":status: 200", "cf-team: 291954c2bf00000482d4767400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T00:57:24.599Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
NewNeighbour; Destination: **********; 
2025-08-05T00:57:39.414Z DEBUG slog: got a response for stream_id=11276, headers=[":status: 200", "cf-team: 291954fd5600000482d47ad400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T00:57:54.414Z DEBUG slog: got a response for stream_id=11280, headers=[":status: 200", "cf-team: 29195537ee00000482d47dc400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T00:58:05.811Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
2025-08-05T00:58:09.415Z DEBUG slog: got a response for stream_id=11284, headers=[":status: 200", "cf-team: 291955728700000482d4810400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T00:58:12.727Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
NewNeighbour; Destination: **********; 
2025-08-05T00:58:24.414Z DEBUG slog: got a response for stream_id=11288, headers=[":status: 200", "cf-team: 291955ad1e00000482d4837400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T00:58:39.414Z DEBUG slog: got a response for stream_id=11292, headers=[":status: 200", "cf-team: 291955e7b600000482d4884400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T00:58:52.919Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
2025-08-05T00:58:54.414Z DEBUG slog: got a response for stream_id=11296, headers=[":status: 200", "cf-team: 291956224e00000482d489c400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T00:58:59.571Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
NewNeighbour; Destination: **********; 
2025-08-05T00:59:02.733Z DEBUG upload_stats{stats=StatsPayload { counters: [CounterPayload { name: "main-loop-compat.commands_recv", labels: {}, count: 2 }, CounterPayload { name: "alternate-network.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "registration-verifier.commands_recv", labels: {}, count: 1 }, CounterPayload { name: "device-state.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "dns_proxy.doh_err_other", labels: {}, count: 3 }, CounterPayload { name: "message_bus.bus_driver_updates_sent", labels: {}, count: 1 }, CounterPayload { name: "message_bus.bus_driver_commands_sent", labels: {}, count: 4 }, CounterPayload { name: "watchdog.requests_recv", labels: {}, count: 423550 }, CounterPayload { name: "main-loop-compat.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "statistics.commands_recv", labels: {}, count: 1 }], summaries: [SummaryPayload { name: "firewall.fw_avg_ms", labels: {}, sum: 274.0, count: 14 }, SummaryPayload { name: "message_bus.bus_driver_request_duration_sec_nano_sec", labels: {}, sum: 16.950499354999817, count: 423550 }, SummaryPayload { name: "dns_proxy.doh_avg_ms", labels: {}, sum: 7449.0, count: 7014 }, SummaryPayload { name: "happy_eyeballs.conn_attempts_all", labels: {}, sum: 1.0, count: 1 }], gauges: [GaugesPayload { name: "happy_eyeballs.conn_attempts_current", labels: {}, value: 1.0 }, GaugesPayload { name: "ntp-clock.min_since_ntp_sync", labels: {}, value: 59.992872287 }] } context="timer"}: actor_statistics::handler: Starting to upload stats
2025-08-05T00:59:02.733Z  WARN upload_stats{stats=StatsPayload { counters: [CounterPayload { name: "main-loop-compat.commands_recv", labels: {}, count: 2 }, CounterPayload { name: "alternate-network.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "registration-verifier.commands_recv", labels: {}, count: 1 }, CounterPayload { name: "device-state.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "dns_proxy.doh_err_other", labels: {}, count: 3 }, CounterPayload { name: "message_bus.bus_driver_updates_sent", labels: {}, count: 1 }, CounterPayload { name: "message_bus.bus_driver_commands_sent", labels: {}, count: 4 }, CounterPayload { name: "watchdog.requests_recv", labels: {}, count: 423550 }, CounterPayload { name: "main-loop-compat.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "statistics.commands_recv", labels: {}, count: 1 }], summaries: [SummaryPayload { name: "firewall.fw_avg_ms", labels: {}, sum: 274.0, count: 14 }, SummaryPayload { name: "message_bus.bus_driver_request_duration_sec_nano_sec", labels: {}, sum: 16.950499354999817, count: 423550 }, SummaryPayload { name: "dns_proxy.doh_avg_ms", labels: {}, sum: 7449.0, count: 7014 }, SummaryPayload { name: "happy_eyeballs.conn_attempts_all", labels: {}, sum: 1.0, count: 1 }], gauges: [GaugesPayload { name: "happy_eyeballs.conn_attempts_current", labels: {}, value: 1.0 }, GaugesPayload { name: "ntp-clock.min_since_ntp_sync", labels: {}, value: 59.992872287 }] } context="timer"}: actor_statistics::handler: Skipped uploading aggregate stats because registration was none!
2025-08-05T00:59:09.414Z DEBUG slog: got a response for stream_id=11300, headers=[":status: 200", "cf-team: 2919565ce700000482d48cd400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T00:59:09.487Z DEBUG tunnel_loop{protocol="masque" con_id="6d3539c6af1e789ae39aef05871da34927b6218a"}: warp_edge::tunnel_loop: Tunnel loop statistics stats=TunnelLoopStats { tun_packet_too_large: 0, edge_packet_too_large: 0, tun_packet_too_small: 0, edge_packet_too_small: 0, tun_packet_martian: 0, tun_packet_unhandled: 20, dropped_edge_packet: 0, dropped_tun_packet: 0, dropped_dns_packet: 0, successfully_retried_tun_packet: 0 }
2025-08-05T00:59:24.413Z DEBUG slog: got a response for stream_id=11304, headers=[":status: 200", "cf-team: 291956977e00000482d48ed400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T00:59:39.414Z DEBUG slog: got a response for stream_id=11308, headers=[":status: 200", "cf-team: 291956d21600000482d4988400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T00:59:40.019Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
2025-08-05T00:59:48.723Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
NewNeighbour; Destination: **********; 
2025-08-05T00:59:54.415Z DEBUG slog: got a response for stream_id=11312, headers=[":status: 200", "cf-team: 2919570caf00000482d49da400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T01:00:09.414Z DEBUG slog: got a response for stream_id=11316, headers=[":status: 200", "cf-team: 291957474700000482d4a4e400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T01:00:24.414Z DEBUG slog: got a response for stream_id=11320, headers=[":status: 200", "cf-team: 29195781de00000482d4aa1400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T01:00:29.175Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
2025-08-05T01:00:35.059Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
NewNeighbour; Destination: **********; 
2025-08-05T01:00:39.414Z DEBUG slog: got a response for stream_id=11324, headers=[":status: 200", "cf-team: 291957bc7600000482d4b01400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T01:00:54.413Z DEBUG slog: got a response for stream_id=11328, headers=[":status: 200", "cf-team: 291957f70e00000482d4b46400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T01:01:02.733Z DEBUG upload_stats{stats=StatsPayload { counters: [CounterPayload { name: "dns_proxy.doh_err_other", labels: {}, count: 3 }, CounterPayload { name: "message_bus.bus_driver_updates_sent", labels: {}, count: 1 }, CounterPayload { name: "main-loop-compat.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "statistics.commands_recv", labels: {}, count: 1 }, CounterPayload { name: "registration-verifier.commands_recv", labels: {}, count: 1 }, CounterPayload { name: "alternate-network.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "watchdog.requests_recv", labels: {}, count: 424750 }, CounterPayload { name: "main-loop-compat.commands_recv", labels: {}, count: 2 }, CounterPayload { name: "device-state.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "message_bus.bus_driver_commands_sent", labels: {}, count: 4 }], summaries: [SummaryPayload { name: "happy_eyeballs.conn_attempts_all", labels: {}, sum: 1.0, count: 1 }, SummaryPayload { name: "message_bus.bus_driver_request_duration_sec_nano_sec", labels: {}, sum: 16.998754852999863, count: 424750 }, SummaryPayload { name: "dns_proxy.doh_avg_ms", labels: {}, sum: 7457.0, count: 7034 }, SummaryPayload { name: "firewall.fw_avg_ms", labels: {}, sum: 274.0, count: 14 }], gauges: [GaugesPayload { name: "ntp-clock.min_since_ntp_sync", labels: {}, value: 59.998012902 }, GaugesPayload { name: "happy_eyeballs.conn_attempts_current", labels: {}, value: 1.0 }] } context="timer"}: actor_statistics::handler: Starting to upload stats
2025-08-05T01:01:02.733Z  WARN upload_stats{stats=StatsPayload { counters: [CounterPayload { name: "dns_proxy.doh_err_other", labels: {}, count: 3 }, CounterPayload { name: "message_bus.bus_driver_updates_sent", labels: {}, count: 1 }, CounterPayload { name: "main-loop-compat.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "statistics.commands_recv", labels: {}, count: 1 }, CounterPayload { name: "registration-verifier.commands_recv", labels: {}, count: 1 }, CounterPayload { name: "alternate-network.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "watchdog.requests_recv", labels: {}, count: 424750 }, CounterPayload { name: "main-loop-compat.commands_recv", labels: {}, count: 2 }, CounterPayload { name: "device-state.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "message_bus.bus_driver_commands_sent", labels: {}, count: 4 }], summaries: [SummaryPayload { name: "happy_eyeballs.conn_attempts_all", labels: {}, sum: 1.0, count: 1 }, SummaryPayload { name: "message_bus.bus_driver_request_duration_sec_nano_sec", labels: {}, sum: 16.998754852999863, count: 424750 }, SummaryPayload { name: "dns_proxy.doh_avg_ms", labels: {}, sum: 7457.0, count: 7034 }, SummaryPayload { name: "firewall.fw_avg_ms", labels: {}, sum: 274.0, count: 14 }], gauges: [GaugesPayload { name: "ntp-clock.min_since_ntp_sync", labels: {}, value: 59.998012902 }, GaugesPayload { name: "happy_eyeballs.conn_attempts_current", labels: {}, value: 1.0 }] } context="timer"}: actor_statistics::handler: Skipped uploading aggregate stats because registration was none!
2025-08-05T01:01:09.414Z DEBUG slog: got a response for stream_id=11332, headers=[":status: 200", "cf-team: 29195831a700000482d4bb1400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T01:01:09.487Z DEBUG tunnel_loop{protocol="masque" con_id="6d3539c6af1e789ae39aef05871da34927b6218a"}: warp_edge::tunnel_loop: Tunnel loop statistics stats=TunnelLoopStats { tun_packet_too_large: 0, edge_packet_too_large: 0, tun_packet_too_small: 0, edge_packet_too_small: 0, tun_packet_martian: 0, tun_packet_unhandled: 20, dropped_edge_packet: 0, dropped_tun_packet: 0, dropped_dns_packet: 0, successfully_retried_tun_packet: 0 }
2025-08-05T01:01:16.275Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
2025-08-05T01:01:24.414Z DEBUG slog: got a response for stream_id=11336, headers=[":status: 200", "cf-team: 2919586c3f00000482d4c08400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T01:01:24.723Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
NewNeighbour; Destination: **********; 
2025-08-05T01:01:39.414Z DEBUG slog: got a response for stream_id=11340, headers=[":status: 200", "cf-team: 291958a6d700000482d4c60400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T01:01:54.414Z DEBUG slog: got a response for stream_id=11344, headers=[":status: 200", "cf-team: 291958e16f00000482d4ca6400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T01:02:05.427Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
2025-08-05T01:02:09.414Z DEBUG slog: got a response for stream_id=11348, headers=[":status: 200", "cf-team: 2919591c0600000482d4cd3400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T01:02:12.599Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
NewNeighbour; Destination: **********; 
2025-08-05T01:02:24.417Z DEBUG slog: got a response for stream_id=11352, headers=[":status: 200", "cf-team: 291959569f00000482d4cef400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T01:02:39.414Z DEBUG slog: got a response for stream_id=11356, headers=[":status: 200", "cf-team: 291959913700000482d4d38400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T01:02:52.531Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
2025-08-05T01:02:54.414Z DEBUG slog: got a response for stream_id=11360, headers=[":status: 200", "cf-team: 291959cbcf00000482d4dbd400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T01:02:59.443Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
NewNeighbour; Destination: **********; 
2025-08-05T01:03:02.735Z DEBUG upload_stats{stats=StatsPayload { counters: [CounterPayload { name: "statistics.commands_recv", labels: {}, count: 1 }, CounterPayload { name: "message_bus.bus_driver_updates_sent", labels: {}, count: 1 }, CounterPayload { name: "registration-verifier.commands_recv", labels: {}, count: 1 }, CounterPayload { name: "main-loop-compat.commands_recv", labels: {}, count: 2 }, CounterPayload { name: "watchdog.requests_recv", labels: {}, count: 425950 }, CounterPayload { name: "message_bus.bus_driver_commands_sent", labels: {}, count: 4 }, CounterPayload { name: "device-state.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "alternate-network.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "dns_proxy.doh_err_other", labels: {}, count: 3 }, CounterPayload { name: "main-loop-compat.updates_recv", labels: {}, count: 1 }], summaries: [SummaryPayload { name: "firewall.fw_avg_ms", labels: {}, sum: 274.0, count: 14 }, SummaryPayload { name: "message_bus.bus_driver_request_duration_sec_nano_sec", labels: {}, sum: 17.04597263699989, count: 425950 }, SummaryPayload { name: "happy_eyeballs.conn_attempts_all", labels: {}, sum: 1.0, count: 1 }, SummaryPayload { name: "dns_proxy.doh_avg_ms", labels: {}, sum: 7492.0, count: 7054 }], gauges: [GaugesPayload { name: "ntp-clock.min_since_ntp_sync", labels: {}, value: 59.992151026 }, GaugesPayload { name: "happy_eyeballs.conn_attempts_current", labels: {}, value: 1.0 }] } context="timer"}: actor_statistics::handler: Starting to upload stats
2025-08-05T01:03:02.735Z  WARN upload_stats{stats=StatsPayload { counters: [CounterPayload { name: "statistics.commands_recv", labels: {}, count: 1 }, CounterPayload { name: "message_bus.bus_driver_updates_sent", labels: {}, count: 1 }, CounterPayload { name: "registration-verifier.commands_recv", labels: {}, count: 1 }, CounterPayload { name: "main-loop-compat.commands_recv", labels: {}, count: 2 }, CounterPayload { name: "watchdog.requests_recv", labels: {}, count: 425950 }, CounterPayload { name: "message_bus.bus_driver_commands_sent", labels: {}, count: 4 }, CounterPayload { name: "device-state.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "alternate-network.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "dns_proxy.doh_err_other", labels: {}, count: 3 }, CounterPayload { name: "main-loop-compat.updates_recv", labels: {}, count: 1 }], summaries: [SummaryPayload { name: "firewall.fw_avg_ms", labels: {}, sum: 274.0, count: 14 }, SummaryPayload { name: "message_bus.bus_driver_request_duration_sec_nano_sec", labels: {}, sum: 17.04597263699989, count: 425950 }, SummaryPayload { name: "happy_eyeballs.conn_attempts_all", labels: {}, sum: 1.0, count: 1 }, SummaryPayload { name: "dns_proxy.doh_avg_ms", labels: {}, sum: 7492.0, count: 7054 }], gauges: [GaugesPayload { name: "ntp-clock.min_since_ntp_sync", labels: {}, value: 59.992151026 }, GaugesPayload { name: "happy_eyeballs.conn_attempts_current", labels: {}, value: 1.0 }] } context="timer"}: actor_statistics::handler: Skipped uploading aggregate stats because registration was none!
2025-08-05T01:03:09.414Z DEBUG slog: got a response for stream_id=11364, headers=[":status: 200", "cf-team: 29195a066700000482d4e30400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T01:03:09.486Z DEBUG tunnel_loop{protocol="masque" con_id="6d3539c6af1e789ae39aef05871da34927b6218a"}: warp_edge::tunnel_loop: Tunnel loop statistics stats=TunnelLoopStats { tun_packet_too_large: 0, edge_packet_too_large: 0, tun_packet_too_small: 0, edge_packet_too_small: 0, tun_packet_martian: 0, tun_packet_unhandled: 21, dropped_edge_packet: 0, dropped_tun_packet: 0, dropped_dns_packet: 0, successfully_retried_tun_packet: 0 }
2025-08-05T01:03:24.414Z DEBUG slog: got a response for stream_id=11368, headers=[":status: 200", "cf-team: 29195a40fe00000482d4eef400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T01:03:39.414Z DEBUG slog: got a response for stream_id=11372, headers=[":status: 200", "cf-team: 29195a7b9700000482d4f3d400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T01:03:39.635Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
2025-08-05T01:03:48.595Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
NewNeighbour; Destination: **********; 
2025-08-05T01:03:54.415Z DEBUG slog: got a response for stream_id=11376, headers=[":status: 200", "cf-team: 29195ab62f00000482d4fa3400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T01:04:09.414Z DEBUG slog: got a response for stream_id=11380, headers=[":status: 200", "cf-team: 29195af0c600000482d4fe8400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T01:04:24.414Z DEBUG slog: got a response for stream_id=11384, headers=[":status: 200", "cf-team: 29195b2b5f00000482d5021400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T01:04:28.787Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
2025-08-05T01:04:36.723Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
NewNeighbour; Destination: **********; 
2025-08-05T01:04:39.414Z DEBUG slog: got a response for stream_id=11388, headers=[":status: 200", "cf-team: 29195b65f600000482d505d400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T01:04:54.413Z DEBUG slog: got a response for stream_id=11392, headers=[":status: 200", "cf-team: 29195ba08e00000482d5202400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T01:05:02.733Z DEBUG upload_stats{stats=StatsPayload { counters: [CounterPayload { name: "message_bus.bus_driver_commands_sent", labels: {}, count: 4 }, CounterPayload { name: "message_bus.bus_driver_updates_sent", labels: {}, count: 1 }, CounterPayload { name: "watchdog.requests_recv", labels: {}, count: 427150 }, CounterPayload { name: "main-loop-compat.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "main-loop-compat.commands_recv", labels: {}, count: 2 }, CounterPayload { name: "registration-verifier.commands_recv", labels: {}, count: 1 }, CounterPayload { name: "alternate-network.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "statistics.commands_recv", labels: {}, count: 1 }, CounterPayload { name: "device-state.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "dns_proxy.doh_err_other", labels: {}, count: 3 }], summaries: [SummaryPayload { name: "dns_proxy.doh_avg_ms", labels: {}, sum: 7492.0, count: 7072 }, SummaryPayload { name: "message_bus.bus_driver_request_duration_sec_nano_sec", labels: {}, sum: 17.09065275099993, count: 427150 }, SummaryPayload { name: "happy_eyeballs.conn_attempts_all", labels: {}, sum: 1.0, count: 1 }, SummaryPayload { name: "firewall.fw_avg_ms", labels: {}, sum: 274.0, count: 14 }], gauges: [GaugesPayload { name: "happy_eyeballs.conn_attempts_current", labels: {}, value: 1.0 }, GaugesPayload { name: "ntp-clock.min_since_ntp_sync", labels: {}, value: 60.00027532 }] } context="timer"}: actor_statistics::handler: Starting to upload stats
2025-08-05T01:05:02.733Z  WARN upload_stats{stats=StatsPayload { counters: [CounterPayload { name: "message_bus.bus_driver_commands_sent", labels: {}, count: 4 }, CounterPayload { name: "message_bus.bus_driver_updates_sent", labels: {}, count: 1 }, CounterPayload { name: "watchdog.requests_recv", labels: {}, count: 427150 }, CounterPayload { name: "main-loop-compat.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "main-loop-compat.commands_recv", labels: {}, count: 2 }, CounterPayload { name: "registration-verifier.commands_recv", labels: {}, count: 1 }, CounterPayload { name: "alternate-network.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "statistics.commands_recv", labels: {}, count: 1 }, CounterPayload { name: "device-state.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "dns_proxy.doh_err_other", labels: {}, count: 3 }], summaries: [SummaryPayload { name: "dns_proxy.doh_avg_ms", labels: {}, sum: 7492.0, count: 7072 }, SummaryPayload { name: "message_bus.bus_driver_request_duration_sec_nano_sec", labels: {}, sum: 17.09065275099993, count: 427150 }, SummaryPayload { name: "happy_eyeballs.conn_attempts_all", labels: {}, sum: 1.0, count: 1 }, SummaryPayload { name: "firewall.fw_avg_ms", labels: {}, sum: 274.0, count: 14 }], gauges: [GaugesPayload { name: "happy_eyeballs.conn_attempts_current", labels: {}, value: 1.0 }, GaugesPayload { name: "ntp-clock.min_since_ntp_sync", labels: {}, value: 60.00027532 }] } context="timer"}: actor_statistics::handler: Skipped uploading aggregate stats because registration was none!
2025-08-05T01:05:09.413Z DEBUG slog: got a response for stream_id=11396, headers=[":status: 200", "cf-team: 29195bdb2600000482d52a7400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T01:05:09.487Z DEBUG tunnel_loop{protocol="masque" con_id="6d3539c6af1e789ae39aef05871da34927b6218a"}: warp_edge::tunnel_loop: Tunnel loop statistics stats=TunnelLoopStats { tun_packet_too_large: 0, edge_packet_too_large: 0, tun_packet_too_small: 0, edge_packet_too_small: 0, tun_packet_martian: 0, tun_packet_unhandled: 21, dropped_edge_packet: 0, dropped_tun_packet: 0, dropped_dns_packet: 0, successfully_retried_tun_packet: 0 }
2025-08-05T01:05:15.891Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
2025-08-05T01:05:23.575Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
NewNeighbour; Destination: **********; 
2025-08-05T01:05:24.414Z DEBUG slog: got a response for stream_id=11400, headers=[":status: 200", "cf-team: 29195c15bf00000482d52df400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T01:05:39.414Z DEBUG slog: got a response for stream_id=11404, headers=[":status: 200", "cf-team: 29195c505700000482d530c400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T01:05:54.414Z DEBUG slog: got a response for stream_id=11408, headers=[":status: 200", "cf-team: 29195c8aef00000482d5333400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T01:06:02.995Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
2025-08-05T01:06:08.627Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
NewNeighbour; Destination: **********; 
2025-08-05T01:06:09.414Z DEBUG slog: got a response for stream_id=11412, headers=[":status: 200", "cf-team: 29195cc58600000482d538b400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T01:06:24.414Z DEBUG slog: got a response for stream_id=11416, headers=[":status: 200", "cf-team: 29195d001f00000482d53c8400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T01:06:39.414Z DEBUG slog: got a response for stream_id=11420, headers=[":status: 200", "cf-team: 29195d3ab600000482d542c400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T01:06:48.051Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
2025-08-05T01:06:54.413Z DEBUG slog: got a response for stream_id=11424, headers=[":status: 200", "cf-team: 29195d754e00000482d5481400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T01:06:54.451Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
NewNeighbour; Destination: **********; 
2025-08-05T01:07:02.733Z DEBUG upload_stats{stats=StatsPayload { counters: [CounterPayload { name: "watchdog.requests_recv", labels: {}, count: 428350 }, CounterPayload { name: "device-state.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "alternate-network.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "registration-verifier.commands_recv", labels: {}, count: 1 }, CounterPayload { name: "message_bus.bus_driver_updates_sent", labels: {}, count: 1 }, CounterPayload { name: "main-loop-compat.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "main-loop-compat.commands_recv", labels: {}, count: 2 }, CounterPayload { name: "message_bus.bus_driver_commands_sent", labels: {}, count: 4 }, CounterPayload { name: "statistics.commands_recv", labels: {}, count: 1 }, CounterPayload { name: "dns_proxy.doh_err_other", labels: {}, count: 3 }], summaries: [SummaryPayload { name: "dns_proxy.doh_avg_ms", labels: {}, sum: 7520.0, count: 7092 }, SummaryPayload { name: "firewall.fw_avg_ms", labels: {}, sum: 274.0, count: 14 }, SummaryPayload { name: "message_bus.bus_driver_request_duration_sec_nano_sec", labels: {}, sum: 17.13443562799996, count: 428350 }, SummaryPayload { name: "happy_eyeballs.conn_attempts_all", labels: {}, sum: 1.0, count: 1 }], gauges: [GaugesPayload { name: "happy_eyeballs.conn_attempts_current", labels: {}, value: 1.0 }, GaugesPayload { name: "ntp-clock.min_since_ntp_sync", labels: {}, value: 60.007482452 }] } context="timer"}: actor_statistics::handler: Starting to upload stats
2025-08-05T01:07:02.733Z  WARN upload_stats{stats=StatsPayload { counters: [CounterPayload { name: "watchdog.requests_recv", labels: {}, count: 428350 }, CounterPayload { name: "device-state.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "alternate-network.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "registration-verifier.commands_recv", labels: {}, count: 1 }, CounterPayload { name: "message_bus.bus_driver_updates_sent", labels: {}, count: 1 }, CounterPayload { name: "main-loop-compat.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "main-loop-compat.commands_recv", labels: {}, count: 2 }, CounterPayload { name: "message_bus.bus_driver_commands_sent", labels: {}, count: 4 }, CounterPayload { name: "statistics.commands_recv", labels: {}, count: 1 }, CounterPayload { name: "dns_proxy.doh_err_other", labels: {}, count: 3 }], summaries: [SummaryPayload { name: "dns_proxy.doh_avg_ms", labels: {}, sum: 7520.0, count: 7092 }, SummaryPayload { name: "firewall.fw_avg_ms", labels: {}, sum: 274.0, count: 14 }, SummaryPayload { name: "message_bus.bus_driver_request_duration_sec_nano_sec", labels: {}, sum: 17.13443562799996, count: 428350 }, SummaryPayload { name: "happy_eyeballs.conn_attempts_all", labels: {}, sum: 1.0, count: 1 }], gauges: [GaugesPayload { name: "happy_eyeballs.conn_attempts_current", labels: {}, value: 1.0 }, GaugesPayload { name: "ntp-clock.min_since_ntp_sync", labels: {}, value: 60.007482452 }] } context="timer"}: actor_statistics::handler: Skipped uploading aggregate stats because registration was none!
2025-08-05T01:07:09.414Z DEBUG slog: got a response for stream_id=11428, headers=[":status: 200", "cf-team: 29195dafe700000482d54f3400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T01:07:09.486Z DEBUG tunnel_loop{protocol="masque" con_id="6d3539c6af1e789ae39aef05871da34927b6218a"}: warp_edge::tunnel_loop: Tunnel loop statistics stats=TunnelLoopStats { tun_packet_too_large: 0, edge_packet_too_large: 0, tun_packet_too_small: 0, edge_packet_too_small: 0, tun_packet_martian: 0, tun_packet_unhandled: 21, dropped_edge_packet: 0, dropped_tun_packet: 0, dropped_dns_packet: 0, successfully_retried_tun_packet: 0 }
2025-08-05T01:07:24.413Z DEBUG slog: got a response for stream_id=11432, headers=[":status: 200", "cf-team: 29195dea7e00000482d5520400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T01:07:39.414Z DEBUG slog: got a response for stream_id=11436, headers=[":status: 200", "cf-team: 29195e251600000482d5564400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T01:07:39.507Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
2025-08-05T01:07:48.723Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
NewNeighbour; Destination: **********; 
2025-08-05T01:07:54.414Z DEBUG slog: got a response for stream_id=11440, headers=[":status: 200", "cf-team: 29195e5fae00000482d55ac400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T01:08:09.414Z DEBUG slog: got a response for stream_id=11444, headers=[":status: 200", "cf-team: 29195e9a4700000482d55e9400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T01:08:24.414Z DEBUG slog: got a response for stream_id=11448, headers=[":status: 200", "cf-team: 29195ed4de00000482d5612400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T01:08:34.547Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
2025-08-05T01:08:39.414Z DEBUG slog: got a response for stream_id=11452, headers=[":status: 200", "cf-team: 29195f0f7600000482d5643400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T01:08:40.691Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
NewNeighbour; Destination: **********; 
2025-08-05T01:08:54.413Z DEBUG slog: got a response for stream_id=11456, headers=[":status: 200", "cf-team: 29195f4a0e00000482d566b400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T01:09:02.733Z DEBUG upload_stats{stats=StatsPayload { counters: [CounterPayload { name: "message_bus.bus_driver_commands_sent", labels: {}, count: 4 }, CounterPayload { name: "dns_proxy.doh_err_other", labels: {}, count: 3 }, CounterPayload { name: "statistics.commands_recv", labels: {}, count: 1 }, CounterPayload { name: "main-loop-compat.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "message_bus.bus_driver_updates_sent", labels: {}, count: 1 }, CounterPayload { name: "watchdog.requests_recv", labels: {}, count: 429550 }, CounterPayload { name: "registration-verifier.commands_recv", labels: {}, count: 1 }, CounterPayload { name: "device-state.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "alternate-network.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "main-loop-compat.commands_recv", labels: {}, count: 2 }], summaries: [SummaryPayload { name: "dns_proxy.doh_avg_ms", labels: {}, sum: 7541.0, count: 7112 }, SummaryPayload { name: "firewall.fw_avg_ms", labels: {}, sum: 274.0, count: 14 }, SummaryPayload { name: "happy_eyeballs.conn_attempts_all", labels: {}, sum: 1.0, count: 1 }, SummaryPayload { name: "message_bus.bus_driver_request_duration_sec_nano_sec", labels: {}, sum: 17.17837748599996, count: 429550 }], gauges: [GaugesPayload { name: "happy_eyeballs.conn_attempts_current", labels: {}, value: 1.0 }, GaugesPayload { name: "ntp-clock.min_since_ntp_sync", labels: {}, value: 59.991962403 }] } context="timer"}: actor_statistics::handler: Starting to upload stats
2025-08-05T01:09:02.733Z  WARN upload_stats{stats=StatsPayload { counters: [CounterPayload { name: "message_bus.bus_driver_commands_sent", labels: {}, count: 4 }, CounterPayload { name: "dns_proxy.doh_err_other", labels: {}, count: 3 }, CounterPayload { name: "statistics.commands_recv", labels: {}, count: 1 }, CounterPayload { name: "main-loop-compat.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "message_bus.bus_driver_updates_sent", labels: {}, count: 1 }, CounterPayload { name: "watchdog.requests_recv", labels: {}, count: 429550 }, CounterPayload { name: "registration-verifier.commands_recv", labels: {}, count: 1 }, CounterPayload { name: "device-state.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "alternate-network.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "main-loop-compat.commands_recv", labels: {}, count: 2 }], summaries: [SummaryPayload { name: "dns_proxy.doh_avg_ms", labels: {}, sum: 7541.0, count: 7112 }, SummaryPayload { name: "firewall.fw_avg_ms", labels: {}, sum: 274.0, count: 14 }, SummaryPayload { name: "happy_eyeballs.conn_attempts_all", labels: {}, sum: 1.0, count: 1 }, SummaryPayload { name: "message_bus.bus_driver_request_duration_sec_nano_sec", labels: {}, sum: 17.17837748599996, count: 429550 }], gauges: [GaugesPayload { name: "happy_eyeballs.conn_attempts_current", labels: {}, value: 1.0 }, GaugesPayload { name: "ntp-clock.min_since_ntp_sync", labels: {}, value: 59.991962403 }] } context="timer"}: actor_statistics::handler: Skipped uploading aggregate stats because registration was none!
2025-08-05T01:09:09.414Z DEBUG slog: got a response for stream_id=11460, headers=[":status: 200", "cf-team: 29195f84a600000482d56c8400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T01:09:09.486Z DEBUG tunnel_loop{protocol="masque" con_id="6d3539c6af1e789ae39aef05871da34927b6218a"}: warp_edge::tunnel_loop: Tunnel loop statistics stats=TunnelLoopStats { tun_packet_too_large: 0, edge_packet_too_large: 0, tun_packet_too_small: 0, edge_packet_too_small: 0, tun_packet_martian: 0, tun_packet_unhandled: 21, dropped_edge_packet: 0, dropped_tun_packet: 0, dropped_dns_packet: 0, successfully_retried_tun_packet: 0 }
2025-08-05T01:09:24.414Z DEBUG slog: got a response for stream_id=11464, headers=[":status: 200", "cf-team: 29195fbf3e00000482d56ea400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T01:09:25.747Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
2025-08-05T01:09:32.659Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
NewNeighbour; Destination: **********; 
2025-08-05T01:09:39.414Z DEBUG slog: got a response for stream_id=11468, headers=[":status: 200", "cf-team: 29195ff9d600000482d5723400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T01:09:54.414Z DEBUG slog: got a response for stream_id=11472, headers=[":status: 200", "cf-team: 291960346e00000482d576c400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T01:10:09.413Z DEBUG slog: got a response for stream_id=11476, headers=[":status: 200", "cf-team: 2919606f0500000482d57ab400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T01:10:18.999Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
2025-08-05T01:10:24.414Z DEBUG slog: got a response for stream_id=11480, headers=[":status: 200", "cf-team: 291960a99d00000482d57ee400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T01:10:24.631Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
NewNeighbour; Destination: **********; 
2025-08-05T01:10:39.414Z DEBUG slog: got a response for stream_id=11484, headers=[":status: 200", "cf-team: 291960e43600000482d5830400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T01:10:54.414Z DEBUG slog: got a response for stream_id=11488, headers=[":status: 200", "cf-team: 2919611ece00000482d585a400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T01:11:02.736Z DEBUG upload_stats{stats=StatsPayload { counters: [CounterPayload { name: "main-loop-compat.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "main-loop-compat.commands_recv", labels: {}, count: 2 }, CounterPayload { name: "alternate-network.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "message_bus.bus_driver_commands_sent", labels: {}, count: 4 }, CounterPayload { name: "registration-verifier.commands_recv", labels: {}, count: 1 }, CounterPayload { name: "dns_proxy.doh_err_other", labels: {}, count: 3 }, CounterPayload { name: "device-state.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "watchdog.requests_recv", labels: {}, count: 430750 }, CounterPayload { name: "statistics.commands_recv", labels: {}, count: 1 }, CounterPayload { name: "message_bus.bus_driver_updates_sent", labels: {}, count: 1 }], summaries: [SummaryPayload { name: "happy_eyeballs.conn_attempts_all", labels: {}, sum: 1.0, count: 1 }, SummaryPayload { name: "message_bus.bus_driver_request_duration_sec_nano_sec", labels: {}, sum: 17.223696303999873, count: 430750 }, SummaryPayload { name: "firewall.fw_avg_ms", labels: {}, sum: 274.0, count: 14 }, SummaryPayload { name: "dns_proxy.doh_avg_ms", labels: {}, sum: 7541.0, count: 7132 }], gauges: [GaugesPayload { name: "ntp-clock.min_since_ntp_sync", labels: {}, value: 59.999246198 }, GaugesPayload { name: "happy_eyeballs.conn_attempts_current", labels: {}, value: 1.0 }] } context="timer"}: actor_statistics::handler: Starting to upload stats
2025-08-05T01:11:02.736Z  WARN upload_stats{stats=StatsPayload { counters: [CounterPayload { name: "main-loop-compat.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "main-loop-compat.commands_recv", labels: {}, count: 2 }, CounterPayload { name: "alternate-network.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "message_bus.bus_driver_commands_sent", labels: {}, count: 4 }, CounterPayload { name: "registration-verifier.commands_recv", labels: {}, count: 1 }, CounterPayload { name: "dns_proxy.doh_err_other", labels: {}, count: 3 }, CounterPayload { name: "device-state.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "watchdog.requests_recv", labels: {}, count: 430750 }, CounterPayload { name: "statistics.commands_recv", labels: {}, count: 1 }, CounterPayload { name: "message_bus.bus_driver_updates_sent", labels: {}, count: 1 }], summaries: [SummaryPayload { name: "happy_eyeballs.conn_attempts_all", labels: {}, sum: 1.0, count: 1 }, SummaryPayload { name: "message_bus.bus_driver_request_duration_sec_nano_sec", labels: {}, sum: 17.223696303999873, count: 430750 }, SummaryPayload { name: "firewall.fw_avg_ms", labels: {}, sum: 274.0, count: 14 }, SummaryPayload { name: "dns_proxy.doh_avg_ms", labels: {}, sum: 7541.0, count: 7132 }], gauges: [GaugesPayload { name: "ntp-clock.min_since_ntp_sync", labels: {}, value: 59.999246198 }, GaugesPayload { name: "happy_eyeballs.conn_attempts_current", labels: {}, value: 1.0 }] } context="timer"}: actor_statistics::handler: Skipped uploading aggregate stats because registration was none!
2025-08-05T01:11:09.414Z DEBUG slog: got a response for stream_id=11492, headers=[":status: 200", "cf-team: 291961596600000482d5892400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T01:11:09.487Z DEBUG tunnel_loop{protocol="masque" con_id="6d3539c6af1e789ae39aef05871da34927b6218a"}: warp_edge::tunnel_loop: Tunnel loop statistics stats=TunnelLoopStats { tun_packet_too_large: 0, edge_packet_too_large: 0, tun_packet_too_small: 0, edge_packet_too_small: 0, tun_packet_martian: 0, tun_packet_unhandled: 21, dropped_edge_packet: 0, dropped_tun_packet: 0, dropped_dns_packet: 0, successfully_retried_tun_packet: 0 }
2025-08-05T01:11:10.195Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
2025-08-05T01:11:16.595Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
NewNeighbour; Destination: **********; 
2025-08-05T01:11:24.414Z DEBUG slog: got a response for stream_id=11496, headers=[":status: 200", "cf-team: 29196193fd00000482d58b0400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T01:11:39.414Z DEBUG slog: got a response for stream_id=11500, headers=[":status: 200", "cf-team: 291961ce9500000482d58c9400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T01:11:54.414Z DEBUG slog: got a response for stream_id=11504, headers=[":status: 200", "cf-team: 291962092d00000482d590c400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T01:12:03.443Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
2025-08-05T01:12:08.563Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
NewNeighbour; Destination: **********; 
2025-08-05T01:12:09.414Z DEBUG slog: got a response for stream_id=11508, headers=[":status: 200", "cf-team: 29196243c500000482d5939400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T01:12:24.414Z DEBUG slog: got a response for stream_id=11512, headers=[":status: 200", "cf-team: 2919627e5d00000482d5957400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T01:12:39.413Z DEBUG slog: got a response for stream_id=11516, headers=[":status: 200", "cf-team: 291962b8f500000482d5977400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T01:12:54.414Z DEBUG slog: got a response for stream_id=11520, headers=[":status: 200", "cf-team: 291962f38d00000482d59b3400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T01:12:54.643Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
2025-08-05T01:13:00.531Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
NewNeighbour; Destination: **********; 
2025-08-05T01:13:02.733Z DEBUG upload_stats{stats=StatsPayload { counters: [CounterPayload { name: "main-loop-compat.commands_recv", labels: {}, count: 2 }, CounterPayload { name: "watchdog.requests_recv", labels: {}, count: 431950 }, CounterPayload { name: "dns_proxy.doh_err_other", labels: {}, count: 3 }, CounterPayload { name: "message_bus.bus_driver_updates_sent", labels: {}, count: 1 }, CounterPayload { name: "statistics.commands_recv", labels: {}, count: 1 }, CounterPayload { name: "main-loop-compat.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "alternate-network.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "device-state.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "message_bus.bus_driver_commands_sent", labels: {}, count: 4 }, CounterPayload { name: "registration-verifier.commands_recv", labels: {}, count: 1 }], summaries: [SummaryPayload { name: "happy_eyeballs.conn_attempts_all", labels: {}, sum: 1.0, count: 1 }, SummaryPayload { name: "message_bus.bus_driver_request_duration_sec_nano_sec", labels: {}, sum: 17.27161196699985, count: 431950 }, SummaryPayload { name: "firewall.fw_avg_ms", labels: {}, sum: 274.0, count: 14 }, SummaryPayload { name: "dns_proxy.doh_avg_ms", labels: {}, sum: 7570.0, count: 7152 }], gauges: [GaugesPayload { name: "ntp-clock.min_since_ntp_sync", labels: {}, value: 60.002169621 }, GaugesPayload { name: "happy_eyeballs.conn_attempts_current", labels: {}, value: 1.0 }] } context="timer"}: actor_statistics::handler: Starting to upload stats
2025-08-05T01:13:02.734Z  WARN upload_stats{stats=StatsPayload { counters: [CounterPayload { name: "main-loop-compat.commands_recv", labels: {}, count: 2 }, CounterPayload { name: "watchdog.requests_recv", labels: {}, count: 431950 }, CounterPayload { name: "dns_proxy.doh_err_other", labels: {}, count: 3 }, CounterPayload { name: "message_bus.bus_driver_updates_sent", labels: {}, count: 1 }, CounterPayload { name: "statistics.commands_recv", labels: {}, count: 1 }, CounterPayload { name: "main-loop-compat.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "alternate-network.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "device-state.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "message_bus.bus_driver_commands_sent", labels: {}, count: 4 }, CounterPayload { name: "registration-verifier.commands_recv", labels: {}, count: 1 }], summaries: [SummaryPayload { name: "happy_eyeballs.conn_attempts_all", labels: {}, sum: 1.0, count: 1 }, SummaryPayload { name: "message_bus.bus_driver_request_duration_sec_nano_sec", labels: {}, sum: 17.27161196699985, count: 431950 }, SummaryPayload { name: "firewall.fw_avg_ms", labels: {}, sum: 274.0, count: 14 }, SummaryPayload { name: "dns_proxy.doh_avg_ms", labels: {}, sum: 7570.0, count: 7152 }], gauges: [GaugesPayload { name: "ntp-clock.min_since_ntp_sync", labels: {}, value: 60.002169621 }, GaugesPayload { name: "happy_eyeballs.conn_attempts_current", labels: {}, value: 1.0 }] } context="timer"}: actor_statistics::handler: Skipped uploading aggregate stats because registration was none!
2025-08-05T01:13:09.414Z DEBUG slog: got a response for stream_id=11524, headers=[":status: 200", "cf-team: 2919632e2500000482d5a69400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T01:13:09.486Z DEBUG tunnel_loop{protocol="masque" con_id="6d3539c6af1e789ae39aef05871da34927b6218a"}: warp_edge::tunnel_loop: Tunnel loop statistics stats=TunnelLoopStats { tun_packet_too_large: 0, edge_packet_too_large: 0, tun_packet_too_small: 0, edge_packet_too_small: 0, tun_packet_martian: 0, tun_packet_unhandled: 21, dropped_edge_packet: 0, dropped_tun_packet: 0, dropped_dns_packet: 0, successfully_retried_tun_packet: 0 }
2025-08-05T01:13:12.736Z  INFO actor_emergency_disconnect::handler: Polling API for latest emergency disconnect state
2025-08-05T01:13:24.414Z DEBUG slog: got a response for stream_id=11528, headers=[":status: 200", "cf-team: 29196368bd00000482d5a93400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T01:13:39.414Z DEBUG slog: got a response for stream_id=11532, headers=[":status: 200", "cf-team: 291963a35500000482d5ac2400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T01:13:41.751Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
2025-08-05T01:13:48.659Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
NewNeighbour; Destination: **********; 
2025-08-05T01:13:54.413Z DEBUG slog: got a response for stream_id=11536, headers=[":status: 200", "cf-team: 291963ddec00000482d5b47400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T01:14:09.414Z DEBUG slog: got a response for stream_id=11540, headers=[":status: 200", "cf-team: 291964188500000482d5b7b400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T01:14:24.414Z DEBUG slog: got a response for stream_id=11544, headers=[":status: 200", "cf-team: 291964531d00000482d5bac400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T01:14:30.899Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
2025-08-05T01:14:36.531Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
NewNeighbour; Destination: **********; 
2025-08-05T01:14:39.414Z DEBUG slog: got a response for stream_id=11548, headers=[":status: 200", "cf-team: 2919648db500000482d5bd0400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T01:14:54.414Z DEBUG slog: got a response for stream_id=11552, headers=[":status: 200", "cf-team: 291964c84d00000482d5c56400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T01:15:02.735Z DEBUG upload_stats{stats=StatsPayload { counters: [CounterPayload { name: "main-loop-compat.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "device-state.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "statistics.commands_recv", labels: {}, count: 1 }, CounterPayload { name: "alternate-network.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "main-loop-compat.commands_recv", labels: {}, count: 2 }, CounterPayload { name: "registration-verifier.commands_recv", labels: {}, count: 1 }, CounterPayload { name: "message_bus.bus_driver_commands_sent", labels: {}, count: 4 }, CounterPayload { name: "watchdog.requests_recv", labels: {}, count: 433150 }, CounterPayload { name: "dns_proxy.doh_err_other", labels: {}, count: 3 }, CounterPayload { name: "message_bus.bus_driver_updates_sent", labels: {}, count: 1 }], summaries: [SummaryPayload { name: "firewall.fw_avg_ms", labels: {}, sum: 274.0, count: 14 }, SummaryPayload { name: "message_bus.bus_driver_request_duration_sec_nano_sec", labels: {}, sum: 17.320521310999847, count: 433150 }, SummaryPayload { name: "happy_eyeballs.conn_attempts_all", labels: {}, sum: 1.0, count: 1 }, SummaryPayload { name: "dns_proxy.doh_avg_ms", labels: {}, sum: 7577.0, count: 7172 }], gauges: [GaugesPayload { name: "happy_eyeballs.conn_attempts_current", labels: {}, value: 1.0 }, GaugesPayload { name: "ntp-clock.min_since_ntp_sync", labels: {}, value: 59.999602368 }] } context="timer"}: actor_statistics::handler: Starting to upload stats
2025-08-05T01:15:02.736Z  WARN upload_stats{stats=StatsPayload { counters: [CounterPayload { name: "main-loop-compat.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "device-state.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "statistics.commands_recv", labels: {}, count: 1 }, CounterPayload { name: "alternate-network.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "main-loop-compat.commands_recv", labels: {}, count: 2 }, CounterPayload { name: "registration-verifier.commands_recv", labels: {}, count: 1 }, CounterPayload { name: "message_bus.bus_driver_commands_sent", labels: {}, count: 4 }, CounterPayload { name: "watchdog.requests_recv", labels: {}, count: 433150 }, CounterPayload { name: "dns_proxy.doh_err_other", labels: {}, count: 3 }, CounterPayload { name: "message_bus.bus_driver_updates_sent", labels: {}, count: 1 }], summaries: [SummaryPayload { name: "firewall.fw_avg_ms", labels: {}, sum: 274.0, count: 14 }, SummaryPayload { name: "message_bus.bus_driver_request_duration_sec_nano_sec", labels: {}, sum: 17.320521310999847, count: 433150 }, SummaryPayload { name: "happy_eyeballs.conn_attempts_all", labels: {}, sum: 1.0, count: 1 }, SummaryPayload { name: "dns_proxy.doh_avg_ms", labels: {}, sum: 7577.0, count: 7172 }], gauges: [GaugesPayload { name: "happy_eyeballs.conn_attempts_current", labels: {}, value: 1.0 }, GaugesPayload { name: "ntp-clock.min_since_ntp_sync", labels: {}, value: 59.999602368 }] } context="timer"}: actor_statistics::handler: Skipped uploading aggregate stats because registration was none!
2025-08-05T01:15:09.414Z DEBUG slog: got a response for stream_id=11556, headers=[":status: 200", "cf-team: 29196502e500000482d5c78400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T01:15:09.486Z DEBUG tunnel_loop{protocol="masque" con_id="6d3539c6af1e789ae39aef05871da34927b6218a"}: warp_edge::tunnel_loop: Tunnel loop statistics stats=TunnelLoopStats { tun_packet_too_large: 0, edge_packet_too_large: 0, tun_packet_too_small: 0, edge_packet_too_small: 0, tun_packet_martian: 0, tun_packet_unhandled: 21, dropped_edge_packet: 0, dropped_tun_packet: 0, dropped_dns_packet: 0, successfully_retried_tun_packet: 0 }
2025-08-05T01:15:18.003Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
2025-08-05T01:15:24.414Z DEBUG slog: got a response for stream_id=11560, headers=[":status: 200", "cf-team: 2919653d7d00000482d5c9e400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T01:15:24.659Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
NewNeighbour; Destination: **********; 
2025-08-05T01:15:39.414Z DEBUG slog: got a response for stream_id=11564, headers=[":status: 200", "cf-team: 291965781400000482d5ccb400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T01:15:54.414Z DEBUG slog: got a response for stream_id=11568, headers=[":status: 200", "cf-team: 291965b2ad00000482d5d11400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T01:16:07.159Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
2025-08-05T01:16:09.414Z DEBUG slog: got a response for stream_id=11572, headers=[":status: 200", "cf-team: 291965ed4400000482d5d77400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T01:16:12.531Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
NewNeighbour; Destination: **********; 
2025-08-05T01:16:24.414Z DEBUG slog: got a response for stream_id=11576, headers=[":status: 200", "cf-team: 29196627dc00000482d5daf400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T01:16:39.414Z DEBUG slog: got a response for stream_id=11580, headers=[":status: 200", "cf-team: 291966627500000482d5dde400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T01:16:54.263Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
2025-08-05T01:16:54.413Z DEBUG slog: got a response for stream_id=11584, headers=[":status: 200", "cf-team: 2919669d0c00000482d5e3d400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T01:16:59.635Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
NewNeighbour; Destination: **********; 
2025-08-05T01:17:02.733Z DEBUG upload_stats{stats=StatsPayload { counters: [CounterPayload { name: "main-loop-compat.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "device-state.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "dns_proxy.doh_err_other", labels: {}, count: 3 }, CounterPayload { name: "message_bus.bus_driver_updates_sent", labels: {}, count: 1 }, CounterPayload { name: "message_bus.bus_driver_commands_sent", labels: {}, count: 4 }, CounterPayload { name: "main-loop-compat.commands_recv", labels: {}, count: 2 }, CounterPayload { name: "alternate-network.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "statistics.commands_recv", labels: {}, count: 1 }, CounterPayload { name: "registration-verifier.commands_recv", labels: {}, count: 1 }, CounterPayload { name: "watchdog.requests_recv", labels: {}, count: 434350 }], summaries: [SummaryPayload { name: "happy_eyeballs.conn_attempts_all", labels: {}, sum: 1.0, count: 1 }, SummaryPayload { name: "firewall.fw_avg_ms", labels: {}, sum: 274.0, count: 14 }, SummaryPayload { name: "dns_proxy.doh_avg_ms", labels: {}, sum: 7591.0, count: 7192 }, SummaryPayload { name: "message_bus.bus_driver_request_duration_sec_nano_sec", labels: {}, sum: 17.371525290999845, count: 434350 }], gauges: [GaugesPayload { name: "happy_eyeballs.conn_attempts_current", labels: {}, value: 1.0 }, GaugesPayload { name: "ntp-clock.min_since_ntp_sync", labels: {}, value: 60.006592213 }] } context="timer"}: actor_statistics::handler: Starting to upload stats
2025-08-05T01:17:02.733Z  WARN upload_stats{stats=StatsPayload { counters: [CounterPayload { name: "main-loop-compat.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "device-state.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "dns_proxy.doh_err_other", labels: {}, count: 3 }, CounterPayload { name: "message_bus.bus_driver_updates_sent", labels: {}, count: 1 }, CounterPayload { name: "message_bus.bus_driver_commands_sent", labels: {}, count: 4 }, CounterPayload { name: "main-loop-compat.commands_recv", labels: {}, count: 2 }, CounterPayload { name: "alternate-network.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "statistics.commands_recv", labels: {}, count: 1 }, CounterPayload { name: "registration-verifier.commands_recv", labels: {}, count: 1 }, CounterPayload { name: "watchdog.requests_recv", labels: {}, count: 434350 }], summaries: [SummaryPayload { name: "happy_eyeballs.conn_attempts_all", labels: {}, sum: 1.0, count: 1 }, SummaryPayload { name: "firewall.fw_avg_ms", labels: {}, sum: 274.0, count: 14 }, SummaryPayload { name: "dns_proxy.doh_avg_ms", labels: {}, sum: 7591.0, count: 7192 }, SummaryPayload { name: "message_bus.bus_driver_request_duration_sec_nano_sec", labels: {}, sum: 17.371525290999845, count: 434350 }], gauges: [GaugesPayload { name: "happy_eyeballs.conn_attempts_current", labels: {}, value: 1.0 }, GaugesPayload { name: "ntp-clock.min_since_ntp_sync", labels: {}, value: 60.006592213 }] } context="timer"}: actor_statistics::handler: Skipped uploading aggregate stats because registration was none!
2025-08-05T01:17:09.414Z DEBUG slog: got a response for stream_id=11588, headers=[":status: 200", "cf-team: 291966d7a500000482d5e71400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T01:17:09.487Z DEBUG tunnel_loop{protocol="masque" con_id="6d3539c6af1e789ae39aef05871da34927b6218a"}: warp_edge::tunnel_loop: Tunnel loop statistics stats=TunnelLoopStats { tun_packet_too_large: 0, edge_packet_too_large: 0, tun_packet_too_small: 0, edge_packet_too_small: 0, tun_packet_martian: 0, tun_packet_unhandled: 21, dropped_edge_packet: 0, dropped_tun_packet: 0, dropped_dns_packet: 0, successfully_retried_tun_packet: 0 }
2025-08-05T01:17:24.414Z DEBUG slog: got a response for stream_id=11592, headers=[":status: 200", "cf-team: 291967123c00000482d5ea2400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T01:17:39.414Z DEBUG slog: got a response for stream_id=11596, headers=[":status: 200", "cf-team: 2919674cd400000482d5ed8400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T01:17:41.367Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
2025-08-05T01:17:47.251Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
NewNeighbour; Destination: **********; 
2025-08-05T01:17:54.413Z DEBUG slog: got a response for stream_id=11600, headers=[":status: 200", "cf-team: 291967876c00000482d5f20400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T01:18:09.414Z DEBUG slog: got a response for stream_id=11604, headers=[":status: 200", "cf-team: 291967c20400000482d5f57400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T01:18:24.414Z DEBUG slog: got a response for stream_id=11608, headers=[":status: 200", "cf-team: 291967fc9c00000482d5f8c400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T01:18:26.423Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
2025-08-05T01:18:32.563Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
NewNeighbour; Destination: **********; 
2025-08-05T01:18:39.413Z DEBUG slog: got a response for stream_id=11612, headers=[":status: 200", "cf-team: 291968373400000482d5fae400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T01:18:54.413Z DEBUG slog: got a response for stream_id=11616, headers=[":status: 200", "cf-team: 29196871cc00000482d5ff7400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T01:19:02.736Z DEBUG upload_stats{stats=StatsPayload { counters: [CounterPayload { name: "main-loop-compat.commands_recv", labels: {}, count: 2 }, CounterPayload { name: "watchdog.requests_recv", labels: {}, count: 435550 }, CounterPayload { name: "dns_proxy.doh_err_other", labels: {}, count: 3 }, CounterPayload { name: "main-loop-compat.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "statistics.commands_recv", labels: {}, count: 1 }, CounterPayload { name: "message_bus.bus_driver_updates_sent", labels: {}, count: 1 }, CounterPayload { name: "registration-verifier.commands_recv", labels: {}, count: 1 }, CounterPayload { name: "message_bus.bus_driver_commands_sent", labels: {}, count: 4 }, CounterPayload { name: "device-state.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "alternate-network.updates_recv", labels: {}, count: 1 }], summaries: [SummaryPayload { name: "dns_proxy.doh_avg_ms", labels: {}, sum: 7613.0, count: 7212 }, SummaryPayload { name: "message_bus.bus_driver_request_duration_sec_nano_sec", labels: {}, sum: 17.42030853499993, count: 435550 }, SummaryPayload { name: "happy_eyeballs.conn_attempts_all", labels: {}, sum: 1.0, count: 1 }, SummaryPayload { name: "firewall.fw_avg_ms", labels: {}, sum: 274.0, count: 14 }], gauges: [GaugesPayload { name: "happy_eyeballs.conn_attempts_current", labels: {}, value: 1.0 }, GaugesPayload { name: "ntp-clock.min_since_ntp_sync", labels: {}, value: 59.992536937 }] } context="timer"}: actor_statistics::handler: Starting to upload stats
2025-08-05T01:19:02.736Z  WARN upload_stats{stats=StatsPayload { counters: [CounterPayload { name: "main-loop-compat.commands_recv", labels: {}, count: 2 }, CounterPayload { name: "watchdog.requests_recv", labels: {}, count: 435550 }, CounterPayload { name: "dns_proxy.doh_err_other", labels: {}, count: 3 }, CounterPayload { name: "main-loop-compat.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "statistics.commands_recv", labels: {}, count: 1 }, CounterPayload { name: "message_bus.bus_driver_updates_sent", labels: {}, count: 1 }, CounterPayload { name: "registration-verifier.commands_recv", labels: {}, count: 1 }, CounterPayload { name: "message_bus.bus_driver_commands_sent", labels: {}, count: 4 }, CounterPayload { name: "device-state.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "alternate-network.updates_recv", labels: {}, count: 1 }], summaries: [SummaryPayload { name: "dns_proxy.doh_avg_ms", labels: {}, sum: 7613.0, count: 7212 }, SummaryPayload { name: "message_bus.bus_driver_request_duration_sec_nano_sec", labels: {}, sum: 17.42030853499993, count: 435550 }, SummaryPayload { name: "happy_eyeballs.conn_attempts_all", labels: {}, sum: 1.0, count: 1 }, SummaryPayload { name: "firewall.fw_avg_ms", labels: {}, sum: 274.0, count: 14 }], gauges: [GaugesPayload { name: "happy_eyeballs.conn_attempts_current", labels: {}, value: 1.0 }, GaugesPayload { name: "ntp-clock.min_since_ntp_sync", labels: {}, value: 59.992536937 }] } context="timer"}: actor_statistics::handler: Skipped uploading aggregate stats because registration was none!
2025-08-05T01:19:09.415Z DEBUG slog: got a response for stream_id=11620, headers=[":status: 200", "cf-team: 291968ac6500000482d6033400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T01:19:09.487Z DEBUG tunnel_loop{protocol="masque" con_id="6d3539c6af1e789ae39aef05871da34927b6218a"}: warp_edge::tunnel_loop: Tunnel loop statistics stats=TunnelLoopStats { tun_packet_too_large: 0, edge_packet_too_large: 0, tun_packet_too_small: 0, edge_packet_too_small: 0, tun_packet_martian: 0, tun_packet_unhandled: 21, dropped_edge_packet: 0, dropped_tun_packet: 0, dropped_dns_packet: 0, successfully_retried_tun_packet: 0 }
2025-08-05T01:19:11.475Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
2025-08-05T01:19:16.595Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
NewNeighbour; Destination: **********; 
2025-08-05T01:19:24.413Z DEBUG slog: got a response for stream_id=11624, headers=[":status: 200", "cf-team: 291968e6fc00000482d6054400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T01:19:39.414Z DEBUG slog: got a response for stream_id=11628, headers=[":status: 200", "cf-team: 291969219400000482d606d400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T01:19:54.414Z DEBUG slog: got a response for stream_id=11632, headers=[":status: 200", "cf-team: 2919695c2c00000482d609a400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T01:19:54.483Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
2025-08-05T01:20:00.627Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
NewNeighbour; Destination: **********; 
2025-08-05T01:20:09.414Z DEBUG slog: got a response for stream_id=11636, headers=[":status: 200", "cf-team: 29196996c400000482d60c2400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T01:20:24.414Z DEBUG slog: got a response for stream_id=11640, headers=[":status: 200", "cf-team: 291969d15c00000482d60d9400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T01:20:39.414Z DEBUG slog: got a response for stream_id=11644, headers=[":status: 200", "cf-team: 29196a0bf400000482d60fd400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T01:20:39.539Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
2025-08-05T01:20:48.755Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
NewNeighbour; Destination: **********; 
2025-08-05T01:20:54.413Z DEBUG slog: got a response for stream_id=11648, headers=[":status: 200", "cf-team: 29196a468c00000482d612e400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T01:21:02.733Z DEBUG upload_stats{stats=StatsPayload { counters: [CounterPayload { name: "dns_proxy.doh_err_other", labels: {}, count: 3 }, CounterPayload { name: "watchdog.requests_recv", labels: {}, count: 436750 }, CounterPayload { name: "main-loop-compat.commands_recv", labels: {}, count: 2 }, CounterPayload { name: "main-loop-compat.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "alternate-network.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "device-state.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "message_bus.bus_driver_commands_sent", labels: {}, count: 4 }, CounterPayload { name: "statistics.commands_recv", labels: {}, count: 1 }, CounterPayload { name: "message_bus.bus_driver_updates_sent", labels: {}, count: 1 }, CounterPayload { name: "registration-verifier.commands_recv", labels: {}, count: 1 }], summaries: [SummaryPayload { name: "firewall.fw_avg_ms", labels: {}, sum: 274.0, count: 14 }, SummaryPayload { name: "dns_proxy.doh_avg_ms", labels: {}, sum: 7621.0, count: 7232 }, SummaryPayload { name: "message_bus.bus_driver_request_duration_sec_nano_sec", labels: {}, sum: 17.469224895999936, count: 436750 }, SummaryPayload { name: "happy_eyeballs.conn_attempts_all", labels: {}, sum: 1.0, count: 1 }], gauges: [GaugesPayload { name: "ntp-clock.min_since_ntp_sync", labels: {}, value: 59.999127818 }, GaugesPayload { name: "happy_eyeballs.conn_attempts_current", labels: {}, value: 1.0 }] } context="timer"}: actor_statistics::handler: Starting to upload stats
2025-08-05T01:21:02.733Z  WARN upload_stats{stats=StatsPayload { counters: [CounterPayload { name: "dns_proxy.doh_err_other", labels: {}, count: 3 }, CounterPayload { name: "watchdog.requests_recv", labels: {}, count: 436750 }, CounterPayload { name: "main-loop-compat.commands_recv", labels: {}, count: 2 }, CounterPayload { name: "main-loop-compat.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "alternate-network.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "device-state.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "message_bus.bus_driver_commands_sent", labels: {}, count: 4 }, CounterPayload { name: "statistics.commands_recv", labels: {}, count: 1 }, CounterPayload { name: "message_bus.bus_driver_updates_sent", labels: {}, count: 1 }, CounterPayload { name: "registration-verifier.commands_recv", labels: {}, count: 1 }], summaries: [SummaryPayload { name: "firewall.fw_avg_ms", labels: {}, sum: 274.0, count: 14 }, SummaryPayload { name: "dns_proxy.doh_avg_ms", labels: {}, sum: 7621.0, count: 7232 }, SummaryPayload { name: "message_bus.bus_driver_request_duration_sec_nano_sec", labels: {}, sum: 17.469224895999936, count: 436750 }, SummaryPayload { name: "happy_eyeballs.conn_attempts_all", labels: {}, sum: 1.0, count: 1 }], gauges: [GaugesPayload { name: "ntp-clock.min_since_ntp_sync", labels: {}, value: 59.999127818 }, GaugesPayload { name: "happy_eyeballs.conn_attempts_current", labels: {}, value: 1.0 }] } context="timer"}: actor_statistics::handler: Skipped uploading aggregate stats because registration was none!
2025-08-05T01:21:09.414Z DEBUG slog: got a response for stream_id=11652, headers=[":status: 200", "cf-team: 29196a812400000482d614d400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T01:21:09.487Z DEBUG tunnel_loop{protocol="masque" con_id="6d3539c6af1e789ae39aef05871da34927b6218a"}: warp_edge::tunnel_loop: Tunnel loop statistics stats=TunnelLoopStats { tun_packet_too_large: 0, edge_packet_too_large: 0, tun_packet_too_small: 0, edge_packet_too_small: 0, tun_packet_martian: 0, tun_packet_unhandled: 21, dropped_edge_packet: 0, dropped_tun_packet: 0, dropped_dns_packet: 0, successfully_retried_tun_packet: 0 }
2025-08-05T01:21:24.414Z DEBUG slog: got a response for stream_id=11656, headers=[":status: 200", "cf-team: 29196abbbc00000482d617a400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T01:21:26.647Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
2025-08-05T01:21:32.532Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
NewNeighbour; Destination: **********; 
2025-08-05T01:21:39.414Z DEBUG slog: got a response for stream_id=11660, headers=[":status: 200", "cf-team: 29196af65400000482d6191400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T01:21:54.414Z DEBUG slog: got a response for stream_id=11664, headers=[":status: 200", "cf-team: 29196b30ec00000482d61c3400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T01:22:09.414Z DEBUG slog: got a response for stream_id=11668, headers=[":status: 200", "cf-team: 29196b6b8400000482d61de400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T01:22:11.699Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
2025-08-05T01:22:20.403Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
NewNeighbour; Destination: **********; 
2025-08-05T01:22:24.414Z DEBUG slog: got a response for stream_id=11672, headers=[":status: 200", "cf-team: 29196ba61c00000482d620a400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T01:22:39.414Z DEBUG slog: got a response for stream_id=11676, headers=[":status: 200", "cf-team: 29196be0b400000482d622b400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T01:22:54.414Z DEBUG slog: got a response for stream_id=11680, headers=[":status: 200", "cf-team: 29196c1b4c00000482d625c400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T01:22:59.955Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
2025-08-05T01:23:02.736Z DEBUG upload_stats{stats=StatsPayload { counters: [CounterPayload { name: "main-loop-compat.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "registration-verifier.commands_recv", labels: {}, count: 1 }, CounterPayload { name: "main-loop-compat.commands_recv", labels: {}, count: 2 }, CounterPayload { name: "message_bus.bus_driver_commands_sent", labels: {}, count: 4 }, CounterPayload { name: "device-state.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "dns_proxy.doh_err_other", labels: {}, count: 3 }, CounterPayload { name: "message_bus.bus_driver_updates_sent", labels: {}, count: 1 }, CounterPayload { name: "alternate-network.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "watchdog.requests_recv", labels: {}, count: 437950 }, CounterPayload { name: "statistics.commands_recv", labels: {}, count: 1 }], summaries: [SummaryPayload { name: "happy_eyeballs.conn_attempts_all", labels: {}, sum: 1.0, count: 1 }, SummaryPayload { name: "firewall.fw_avg_ms", labels: {}, sum: 274.0, count: 14 }, SummaryPayload { name: "message_bus.bus_driver_request_duration_sec_nano_sec", labels: {}, sum: 17.515609869999963, count: 437950 }, SummaryPayload { name: "dns_proxy.doh_avg_ms", labels: {}, sum: 7649.0, count: 7252 }], gauges: [GaugesPayload { name: "happy_eyeballs.conn_attempts_current", labels: {}, value: 1.0 }, GaugesPayload { name: "ntp-clock.min_since_ntp_sync", labels: {}, value: 59.999820674 }] } context="timer"}: actor_statistics::handler: Starting to upload stats
2025-08-05T01:23:02.736Z  WARN upload_stats{stats=StatsPayload { counters: [CounterPayload { name: "main-loop-compat.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "registration-verifier.commands_recv", labels: {}, count: 1 }, CounterPayload { name: "main-loop-compat.commands_recv", labels: {}, count: 2 }, CounterPayload { name: "message_bus.bus_driver_commands_sent", labels: {}, count: 4 }, CounterPayload { name: "device-state.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "dns_proxy.doh_err_other", labels: {}, count: 3 }, CounterPayload { name: "message_bus.bus_driver_updates_sent", labels: {}, count: 1 }, CounterPayload { name: "alternate-network.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "watchdog.requests_recv", labels: {}, count: 437950 }, CounterPayload { name: "statistics.commands_recv", labels: {}, count: 1 }], summaries: [SummaryPayload { name: "happy_eyeballs.conn_attempts_all", labels: {}, sum: 1.0, count: 1 }, SummaryPayload { name: "firewall.fw_avg_ms", labels: {}, sum: 274.0, count: 14 }, SummaryPayload { name: "message_bus.bus_driver_request_duration_sec_nano_sec", labels: {}, sum: 17.515609869999963, count: 437950 }, SummaryPayload { name: "dns_proxy.doh_avg_ms", labels: {}, sum: 7649.0, count: 7252 }], gauges: [GaugesPayload { name: "happy_eyeballs.conn_attempts_current", labels: {}, value: 1.0 }, GaugesPayload { name: "ntp-clock.min_since_ntp_sync", labels: {}, value: 59.999820674 }] } context="timer"}: actor_statistics::handler: Skipped uploading aggregate stats because registration was none!
2025-08-05T01:23:05.975Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
NewNeighbour; Destination: **********; 
2025-08-05T01:23:09.413Z DEBUG slog: got a response for stream_id=11684, headers=[":status: 200", "cf-team: 29196c55e300000482d6279400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T01:23:09.487Z DEBUG tunnel_loop{protocol="masque" con_id="6d3539c6af1e789ae39aef05871da34927b6218a"}: warp_edge::tunnel_loop: Tunnel loop statistics stats=TunnelLoopStats { tun_packet_too_large: 0, edge_packet_too_large: 0, tun_packet_too_small: 0, edge_packet_too_small: 0, tun_packet_martian: 0, tun_packet_unhandled: 21, dropped_edge_packet: 0, dropped_tun_packet: 0, dropped_dns_packet: 0, successfully_retried_tun_packet: 0 }
2025-08-05T01:23:24.414Z DEBUG slog: got a response for stream_id=11688, headers=[":status: 200", "cf-team: 29196c907c00000482d62b0400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T01:23:39.414Z DEBUG slog: got a response for stream_id=11692, headers=[":status: 200", "cf-team: 29196ccb1400000482d62c1400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T01:23:45.907Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
2025-08-05T01:23:51.539Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
2025-08-05T01:23:51.540Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
2025-08-05T01:23:54.414Z DEBUG slog: got a response for stream_id=11696, headers=[":status: 200", "cf-team: 29196d05ac00000482d62df400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T01:24:09.414Z DEBUG slog: got a response for stream_id=11700, headers=[":status: 200", "cf-team: 29196d404400000482d62ed400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T01:24:24.414Z DEBUG slog: got a response for stream_id=11704, headers=[":status: 200", "cf-team: 29196d7adc00000482d6315400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T01:24:33.011Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
2025-08-05T01:24:39.413Z DEBUG slog: got a response for stream_id=11708, headers=[":status: 200", "cf-team: 29196db57300000482d6335400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T01:24:40.691Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
NewNeighbour; Destination: **********; 
2025-08-05T01:24:54.414Z DEBUG slog: got a response for stream_id=11712, headers=[":status: 200", "cf-team: 29196df00c00000482d6350400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T01:25:02.735Z DEBUG upload_stats{stats=StatsPayload { counters: [CounterPayload { name: "watchdog.requests_recv", labels: {}, count: 439150 }, CounterPayload { name: "message_bus.bus_driver_updates_sent", labels: {}, count: 1 }, CounterPayload { name: "main-loop-compat.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "main-loop-compat.commands_recv", labels: {}, count: 2 }, CounterPayload { name: "statistics.commands_recv", labels: {}, count: 1 }, CounterPayload { name: "registration-verifier.commands_recv", labels: {}, count: 1 }, CounterPayload { name: "dns_proxy.doh_err_other", labels: {}, count: 3 }, CounterPayload { name: "device-state.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "message_bus.bus_driver_commands_sent", labels: {}, count: 4 }, CounterPayload { name: "alternate-network.updates_recv", labels: {}, count: 1 }], summaries: [SummaryPayload { name: "dns_proxy.doh_avg_ms", labels: {}, sum: 7656.0, count: 7272 }, SummaryPayload { name: "happy_eyeballs.conn_attempts_all", labels: {}, sum: 1.0, count: 1 }, SummaryPayload { name: "firewall.fw_avg_ms", labels: {}, sum: 274.0, count: 14 }, SummaryPayload { name: "message_bus.bus_driver_request_duration_sec_nano_sec", labels: {}, sum: 17.562442695999945, count: 439150 }], gauges: [GaugesPayload { name: "happy_eyeballs.conn_attempts_current", labels: {}, value: 1.0 }, GaugesPayload { name: "ntp-clock.min_since_ntp_sync", labels: {}, value: 60.000587724 }] } context="timer"}: actor_statistics::handler: Starting to upload stats
2025-08-05T01:25:02.735Z  WARN upload_stats{stats=StatsPayload { counters: [CounterPayload { name: "watchdog.requests_recv", labels: {}, count: 439150 }, CounterPayload { name: "message_bus.bus_driver_updates_sent", labels: {}, count: 1 }, CounterPayload { name: "main-loop-compat.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "main-loop-compat.commands_recv", labels: {}, count: 2 }, CounterPayload { name: "statistics.commands_recv", labels: {}, count: 1 }, CounterPayload { name: "registration-verifier.commands_recv", labels: {}, count: 1 }, CounterPayload { name: "dns_proxy.doh_err_other", labels: {}, count: 3 }, CounterPayload { name: "device-state.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "message_bus.bus_driver_commands_sent", labels: {}, count: 4 }, CounterPayload { name: "alternate-network.updates_recv", labels: {}, count: 1 }], summaries: [SummaryPayload { name: "dns_proxy.doh_avg_ms", labels: {}, sum: 7656.0, count: 7272 }, SummaryPayload { name: "happy_eyeballs.conn_attempts_all", labels: {}, sum: 1.0, count: 1 }, SummaryPayload { name: "firewall.fw_avg_ms", labels: {}, sum: 274.0, count: 14 }, SummaryPayload { name: "message_bus.bus_driver_request_duration_sec_nano_sec", labels: {}, sum: 17.562442695999945, count: 439150 }], gauges: [GaugesPayload { name: "happy_eyeballs.conn_attempts_current", labels: {}, value: 1.0 }, GaugesPayload { name: "ntp-clock.min_since_ntp_sync", labels: {}, value: 60.000587724 }] } context="timer"}: actor_statistics::handler: Skipped uploading aggregate stats because registration was none!
2025-08-05T01:25:09.414Z DEBUG slog: got a response for stream_id=11716, headers=[":status: 200", "cf-team: 29196e2aa400000482d636c400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T01:25:09.486Z DEBUG tunnel_loop{protocol="masque" con_id="6d3539c6af1e789ae39aef05871da34927b6218a"}: warp_edge::tunnel_loop: Tunnel loop statistics stats=TunnelLoopStats { tun_packet_too_large: 0, edge_packet_too_large: 0, tun_packet_too_small: 0, edge_packet_too_small: 0, tun_packet_martian: 0, tun_packet_unhandled: 21, dropped_edge_packet: 0, dropped_tun_packet: 0, dropped_dns_packet: 0, successfully_retried_tun_packet: 0 }
2025-08-05T01:25:22.163Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
2025-08-05T01:25:24.414Z DEBUG slog: got a response for stream_id=11720, headers=[":status: 200", "cf-team: 29196e653c00000482d6385400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T01:25:28.563Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
NewNeighbour; Destination: **********; 
2025-08-05T01:25:39.414Z DEBUG slog: got a response for stream_id=11724, headers=[":status: 200", "cf-team: 29196e9fd400000482d63a7400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T01:25:54.413Z DEBUG slog: got a response for stream_id=11728, headers=[":status: 200", "cf-team: 29196eda6b00000482d63ef400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T01:26:09.267Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
2025-08-05T01:26:09.414Z DEBUG slog: got a response for stream_id=11732, headers=[":status: 200", "cf-team: 29196f150400000482d640e400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T01:26:14.643Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
NewNeighbour; Destination: **********; 
2025-08-05T01:26:24.413Z DEBUG slog: got a response for stream_id=11736, headers=[":status: 200", "cf-team: 29196f4f9b00000482d6446400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T01:26:39.414Z DEBUG slog: got a response for stream_id=11740, headers=[":status: 200", "cf-team: 29196f8a3400000482d646e400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T01:26:54.323Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
2025-08-05T01:26:54.414Z DEBUG slog: got a response for stream_id=11744, headers=[":status: 200", "cf-team: 29196fc4cc00000482d64b1400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T01:26:59.443Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
NewNeighbour; Destination: **********; 
2025-08-05T01:27:02.735Z DEBUG upload_stats{stats=StatsPayload { counters: [CounterPayload { name: "dns_proxy.doh_err_other", labels: {}, count: 3 }, CounterPayload { name: "device-state.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "message_bus.bus_driver_updates_sent", labels: {}, count: 1 }, CounterPayload { name: "message_bus.bus_driver_commands_sent", labels: {}, count: 4 }, CounterPayload { name: "watchdog.requests_recv", labels: {}, count: 440350 }, CounterPayload { name: "statistics.commands_recv", labels: {}, count: 1 }, CounterPayload { name: "alternate-network.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "main-loop-compat.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "registration-verifier.commands_recv", labels: {}, count: 1 }, CounterPayload { name: "main-loop-compat.commands_recv", labels: {}, count: 2 }], summaries: [SummaryPayload { name: "firewall.fw_avg_ms", labels: {}, sum: 274.0, count: 14 }, SummaryPayload { name: "happy_eyeballs.conn_attempts_all", labels: {}, sum: 1.0, count: 1 }, SummaryPayload { name: "message_bus.bus_driver_request_duration_sec_nano_sec", labels: {}, sum: 17.610257662999953, count: 440350 }, SummaryPayload { name: "dns_proxy.doh_avg_ms", labels: {}, sum: 7656.0, count: 7290 }], gauges: [GaugesPayload { name: "happy_eyeballs.conn_attempts_current", labels: {}, value: 1.0 }, GaugesPayload { name: "ntp-clock.min_since_ntp_sync", labels: {}, value: 59.999938702 }] } context="timer"}: actor_statistics::handler: Starting to upload stats
2025-08-05T01:27:02.735Z  WARN upload_stats{stats=StatsPayload { counters: [CounterPayload { name: "dns_proxy.doh_err_other", labels: {}, count: 3 }, CounterPayload { name: "device-state.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "message_bus.bus_driver_updates_sent", labels: {}, count: 1 }, CounterPayload { name: "message_bus.bus_driver_commands_sent", labels: {}, count: 4 }, CounterPayload { name: "watchdog.requests_recv", labels: {}, count: 440350 }, CounterPayload { name: "statistics.commands_recv", labels: {}, count: 1 }, CounterPayload { name: "alternate-network.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "main-loop-compat.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "registration-verifier.commands_recv", labels: {}, count: 1 }, CounterPayload { name: "main-loop-compat.commands_recv", labels: {}, count: 2 }], summaries: [SummaryPayload { name: "firewall.fw_avg_ms", labels: {}, sum: 274.0, count: 14 }, SummaryPayload { name: "happy_eyeballs.conn_attempts_all", labels: {}, sum: 1.0, count: 1 }, SummaryPayload { name: "message_bus.bus_driver_request_duration_sec_nano_sec", labels: {}, sum: 17.610257662999953, count: 440350 }, SummaryPayload { name: "dns_proxy.doh_avg_ms", labels: {}, sum: 7656.0, count: 7290 }], gauges: [GaugesPayload { name: "happy_eyeballs.conn_attempts_current", labels: {}, value: 1.0 }, GaugesPayload { name: "ntp-clock.min_since_ntp_sync", labels: {}, value: 59.999938702 }] } context="timer"}: actor_statistics::handler: Skipped uploading aggregate stats because registration was none!
2025-08-05T01:27:09.414Z DEBUG slog: got a response for stream_id=11748, headers=[":status: 200", "cf-team: 29196fff6400000482d64c5400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T01:27:09.487Z DEBUG tunnel_loop{protocol="masque" con_id="6d3539c6af1e789ae39aef05871da34927b6218a"}: warp_edge::tunnel_loop: Tunnel loop statistics stats=TunnelLoopStats { tun_packet_too_large: 0, edge_packet_too_large: 0, tun_packet_too_small: 0, edge_packet_too_small: 0, tun_packet_martian: 0, tun_packet_unhandled: 21, dropped_edge_packet: 0, dropped_tun_packet: 0, dropped_dns_packet: 0, successfully_retried_tun_packet: 0 }
2025-08-05T01:27:24.413Z DEBUG slog: got a response for stream_id=11752, headers=[":status: 200", "cf-team: 29197039fb00000482d64f1400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T01:27:39.379Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
2025-08-05T01:27:39.413Z DEBUG slog: got a response for stream_id=11756, headers=[":status: 200", "cf-team: 291970749300000482d650c400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T01:27:44.499Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
NewNeighbour; Destination: **********; 
2025-08-05T01:27:54.413Z DEBUG slog: got a response for stream_id=11760, headers=[":status: 200", "cf-team: 291970af2b00000482d6526400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T01:28:06.003Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
2025-08-05T01:28:09.414Z DEBUG slog: got a response for stream_id=11764, headers=[":status: 200", "cf-team: 291970e9c400000482d654d400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T01:28:12.663Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
NewNeighbour; Destination: **********; 
2025-08-05T01:28:24.413Z DEBUG slog: got a response for stream_id=11768, headers=[":status: 200", "cf-team: 291971245b00000482d6568400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T01:28:34.675Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
2025-08-05T01:28:39.414Z DEBUG slog: got a response for stream_id=11772, headers=[":status: 200", "cf-team: 2919715ef400000482d657a400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T01:28:40.051Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
NewNeighbour; Destination: **********; 
2025-08-05T01:28:54.414Z DEBUG slog: got a response for stream_id=11776, headers=[":status: 200", "cf-team: 291971998b00000482d658d400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T01:29:01.299Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
2025-08-05T01:29:02.734Z DEBUG upload_stats{stats=StatsPayload { counters: [CounterPayload { name: "dns_proxy.doh_err_other", labels: {}, count: 3 }, CounterPayload { name: "main-loop-compat.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "main-loop-compat.commands_recv", labels: {}, count: 2 }, CounterPayload { name: "message_bus.bus_driver_updates_sent", labels: {}, count: 1 }, CounterPayload { name: "message_bus.bus_driver_commands_sent", labels: {}, count: 4 }, CounterPayload { name: "watchdog.requests_recv", labels: {}, count: 441550 }, CounterPayload { name: "device-state.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "alternate-network.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "statistics.commands_recv", labels: {}, count: 1 }, CounterPayload { name: "registration-verifier.commands_recv", labels: {}, count: 1 }], summaries: [SummaryPayload { name: "happy_eyeballs.conn_attempts_all", labels: {}, sum: 1.0, count: 1 }, SummaryPayload { name: "dns_proxy.doh_avg_ms", labels: {}, sum: 7677.0, count: 7310 }, SummaryPayload { name: "firewall.fw_avg_ms", labels: {}, sum: 274.0, count: 14 }, SummaryPayload { name: "message_bus.bus_driver_request_duration_sec_nano_sec", labels: {}, sum: 17.6568158189999, count: 441550 }], gauges: [GaugesPayload { name: "happy_eyeballs.conn_attempts_current", labels: {}, value: 1.0 }, GaugesPayload { name: "ntp-clock.min_since_ntp_sync", labels: {}, value: 59.992960271 }] } context="timer"}: actor_statistics::handler: Starting to upload stats
2025-08-05T01:29:02.734Z  WARN upload_stats{stats=StatsPayload { counters: [CounterPayload { name: "dns_proxy.doh_err_other", labels: {}, count: 3 }, CounterPayload { name: "main-loop-compat.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "main-loop-compat.commands_recv", labels: {}, count: 2 }, CounterPayload { name: "message_bus.bus_driver_updates_sent", labels: {}, count: 1 }, CounterPayload { name: "message_bus.bus_driver_commands_sent", labels: {}, count: 4 }, CounterPayload { name: "watchdog.requests_recv", labels: {}, count: 441550 }, CounterPayload { name: "device-state.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "alternate-network.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "statistics.commands_recv", labels: {}, count: 1 }, CounterPayload { name: "registration-verifier.commands_recv", labels: {}, count: 1 }], summaries: [SummaryPayload { name: "happy_eyeballs.conn_attempts_all", labels: {}, sum: 1.0, count: 1 }, SummaryPayload { name: "dns_proxy.doh_avg_ms", labels: {}, sum: 7677.0, count: 7310 }, SummaryPayload { name: "firewall.fw_avg_ms", labels: {}, sum: 274.0, count: 14 }, SummaryPayload { name: "message_bus.bus_driver_request_duration_sec_nano_sec", labels: {}, sum: 17.6568158189999, count: 441550 }], gauges: [GaugesPayload { name: "happy_eyeballs.conn_attempts_current", labels: {}, value: 1.0 }, GaugesPayload { name: "ntp-clock.min_since_ntp_sync", labels: {}, value: 59.992960271 }] } context="timer"}: actor_statistics::handler: Skipped uploading aggregate stats because registration was none!
2025-08-05T01:29:07.699Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
NewNeighbour; Destination: **********; 
2025-08-05T01:29:09.414Z DEBUG slog: got a response for stream_id=11780, headers=[":status: 200", "cf-team: 291971d42400000482d65a2400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T01:29:09.486Z DEBUG tunnel_loop{protocol="masque" con_id="6d3539c6af1e789ae39aef05871da34927b6218a"}: warp_edge::tunnel_loop: Tunnel loop statistics stats=TunnelLoopStats { tun_packet_too_large: 0, edge_packet_too_large: 0, tun_packet_too_small: 0, edge_packet_too_small: 0, tun_packet_martian: 0, tun_packet_unhandled: 21, dropped_edge_packet: 0, dropped_tun_packet: 0, dropped_dns_packet: 0, successfully_retried_tun_packet: 0 }
2025-08-05T01:29:24.414Z DEBUG slog: got a response for stream_id=11784, headers=[":status: 200", "cf-team: 2919720ebc00000482d65af400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T01:29:29.971Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
2025-08-05T01:29:36.627Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
NewNeighbour; Destination: **********; 
2025-08-05T01:29:39.414Z DEBUG slog: got a response for stream_id=11788, headers=[":status: 200", "cf-team: 291972495400000482d65c1400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T01:29:54.414Z DEBUG slog: got a response for stream_id=11792, headers=[":status: 200", "cf-team: 29197283ec00000482d65d0400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T01:29:58.643Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
2025-08-05T01:30:04.531Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
NewNeighbour; Destination: **********; 
2025-08-05T01:30:09.414Z DEBUG slog: got a response for stream_id=11796, headers=[":status: 200", "cf-team: 291972be8300000482d65e6400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T01:30:24.414Z DEBUG slog: got a response for stream_id=11800, headers=[":status: 200", "cf-team: 291972f91b00000482d65f5400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T01:30:25.271Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
2025-08-05T01:30:32.691Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
NewNeighbour; Destination: **********; 
2025-08-05T01:30:39.414Z DEBUG slog: got a response for stream_id=11804, headers=[":status: 200", "cf-team: 29197333b400000482d6606400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T01:30:53.939Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
2025-08-05T01:30:54.414Z DEBUG slog: got a response for stream_id=11808, headers=[":status: 200", "cf-team: 2919736e4b00000482d6642400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T01:30:59.571Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
NewNeighbour; Destination: **********; 
2025-08-05T01:31:02.736Z DEBUG upload_stats{stats=StatsPayload { counters: [CounterPayload { name: "device-state.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "watchdog.requests_recv", labels: {}, count: 442750 }, CounterPayload { name: "message_bus.bus_driver_updates_sent", labels: {}, count: 1 }, CounterPayload { name: "main-loop-compat.commands_recv", labels: {}, count: 2 }, CounterPayload { name: "statistics.commands_recv", labels: {}, count: 1 }, CounterPayload { name: "registration-verifier.commands_recv", labels: {}, count: 1 }, CounterPayload { name: "message_bus.bus_driver_commands_sent", labels: {}, count: 4 }, CounterPayload { name: "main-loop-compat.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "dns_proxy.doh_err_other", labels: {}, count: 3 }, CounterPayload { name: "alternate-network.updates_recv", labels: {}, count: 1 }], summaries: [SummaryPayload { name: "happy_eyeballs.conn_attempts_all", labels: {}, sum: 1.0, count: 1 }, SummaryPayload { name: "dns_proxy.doh_avg_ms", labels: {}, sum: 7691.0, count: 7330 }, SummaryPayload { name: "firewall.fw_avg_ms", labels: {}, sum: 274.0, count: 14 }, SummaryPayload { name: "message_bus.bus_driver_request_duration_sec_nano_sec", labels: {}, sum: 17.706085325999883, count: 442750 }], gauges: [GaugesPayload { name: "happy_eyeballs.conn_attempts_current", labels: {}, value: 1.0 }, GaugesPayload { name: "ntp-clock.min_since_ntp_sync", labels: {}, value: 59.997339865 }] } context="timer"}: actor_statistics::handler: Starting to upload stats
2025-08-05T01:31:02.736Z  WARN upload_stats{stats=StatsPayload { counters: [CounterPayload { name: "device-state.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "watchdog.requests_recv", labels: {}, count: 442750 }, CounterPayload { name: "message_bus.bus_driver_updates_sent", labels: {}, count: 1 }, CounterPayload { name: "main-loop-compat.commands_recv", labels: {}, count: 2 }, CounterPayload { name: "statistics.commands_recv", labels: {}, count: 1 }, CounterPayload { name: "registration-verifier.commands_recv", labels: {}, count: 1 }, CounterPayload { name: "message_bus.bus_driver_commands_sent", labels: {}, count: 4 }, CounterPayload { name: "main-loop-compat.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "dns_proxy.doh_err_other", labels: {}, count: 3 }, CounterPayload { name: "alternate-network.updates_recv", labels: {}, count: 1 }], summaries: [SummaryPayload { name: "happy_eyeballs.conn_attempts_all", labels: {}, sum: 1.0, count: 1 }, SummaryPayload { name: "dns_proxy.doh_avg_ms", labels: {}, sum: 7691.0, count: 7330 }, SummaryPayload { name: "firewall.fw_avg_ms", labels: {}, sum: 274.0, count: 14 }, SummaryPayload { name: "message_bus.bus_driver_request_duration_sec_nano_sec", labels: {}, sum: 17.706085325999883, count: 442750 }], gauges: [GaugesPayload { name: "happy_eyeballs.conn_attempts_current", labels: {}, value: 1.0 }, GaugesPayload { name: "ntp-clock.min_since_ntp_sync", labels: {}, value: 59.997339865 }] } context="timer"}: actor_statistics::handler: Skipped uploading aggregate stats because registration was none!
2025-08-05T01:31:09.414Z DEBUG slog: got a response for stream_id=11812, headers=[":status: 200", "cf-team: 291973a8e400000482d6666400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T01:31:09.486Z DEBUG tunnel_loop{protocol="masque" con_id="6d3539c6af1e789ae39aef05871da34927b6218a"}: warp_edge::tunnel_loop: Tunnel loop statistics stats=TunnelLoopStats { tun_packet_too_large: 0, edge_packet_too_large: 0, tun_packet_too_small: 0, edge_packet_too_small: 0, tun_packet_martian: 0, tun_packet_unhandled: 21, dropped_edge_packet: 0, dropped_tun_packet: 0, dropped_dns_packet: 0, successfully_retried_tun_packet: 0 }
2025-08-05T01:31:20.567Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
2025-08-05T01:31:24.414Z DEBUG slog: got a response for stream_id=11816, headers=[":status: 200", "cf-team: 291973e37c00000482d667f400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T01:31:26.963Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
NewNeighbour; Destination: **********; 
2025-08-05T01:31:39.414Z DEBUG slog: got a response for stream_id=11820, headers=[":status: 200", "cf-team: 2919741e1300000482d66a6400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T01:31:49.235Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
2025-08-05T01:31:54.414Z DEBUG slog: got a response for stream_id=11824, headers=[":status: 200", "cf-team: 29197458ab00000482d66c3400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T01:31:56.659Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
NewNeighbour; Destination: **********; 
2025-08-05T01:32:09.414Z DEBUG slog: got a response for stream_id=11828, headers=[":status: 200", "cf-team: 291974934400000482d66d5400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T01:32:17.911Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
2025-08-05T01:32:24.414Z DEBUG slog: got a response for stream_id=11832, headers=[":status: 200", "cf-team: 291974cddb00000482d66e2400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T01:32:24.563Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
NewNeighbour; Destination: **********; 
2025-08-05T01:32:39.414Z DEBUG slog: got a response for stream_id=11836, headers=[":status: 200", "cf-team: 291975087400000482d671a400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T01:32:54.414Z DEBUG slog: got a response for stream_id=11840, headers=[":status: 200", "cf-team: 291975430b00000482d6748400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T01:32:59.635Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
2025-08-05T01:33:02.733Z DEBUG upload_stats{stats=StatsPayload { counters: [CounterPayload { name: "main-loop-compat.commands_recv", labels: {}, count: 2 }, CounterPayload { name: "registration-verifier.commands_recv", labels: {}, count: 1 }, CounterPayload { name: "message_bus.bus_driver_commands_sent", labels: {}, count: 4 }, CounterPayload { name: "main-loop-compat.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "alternate-network.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "device-state.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "message_bus.bus_driver_updates_sent", labels: {}, count: 1 }, CounterPayload { name: "statistics.commands_recv", labels: {}, count: 1 }, CounterPayload { name: "dns_proxy.doh_err_other", labels: {}, count: 3 }, CounterPayload { name: "watchdog.requests_recv", labels: {}, count: 443950 }], summaries: [SummaryPayload { name: "dns_proxy.doh_avg_ms", labels: {}, sum: 7713.0, count: 7350 }, SummaryPayload { name: "firewall.fw_avg_ms", labels: {}, sum: 274.0, count: 14 }, SummaryPayload { name: "happy_eyeballs.conn_attempts_all", labels: {}, sum: 1.0, count: 1 }, SummaryPayload { name: "message_bus.bus_driver_request_duration_sec_nano_sec", labels: {}, sum: 17.75335487399994, count: 443950 }], gauges: [GaugesPayload { name: "ntp-clock.min_since_ntp_sync", labels: {}, value: 60.002434227 }, GaugesPayload { name: "happy_eyeballs.conn_attempts_current", labels: {}, value: 1.0 }] } context="timer"}: actor_statistics::handler: Starting to upload stats
2025-08-05T01:33:02.733Z  WARN upload_stats{stats=StatsPayload { counters: [CounterPayload { name: "main-loop-compat.commands_recv", labels: {}, count: 2 }, CounterPayload { name: "registration-verifier.commands_recv", labels: {}, count: 1 }, CounterPayload { name: "message_bus.bus_driver_commands_sent", labels: {}, count: 4 }, CounterPayload { name: "main-loop-compat.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "alternate-network.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "device-state.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "message_bus.bus_driver_updates_sent", labels: {}, count: 1 }, CounterPayload { name: "statistics.commands_recv", labels: {}, count: 1 }, CounterPayload { name: "dns_proxy.doh_err_other", labels: {}, count: 3 }, CounterPayload { name: "watchdog.requests_recv", labels: {}, count: 443950 }], summaries: [SummaryPayload { name: "dns_proxy.doh_avg_ms", labels: {}, sum: 7713.0, count: 7350 }, SummaryPayload { name: "firewall.fw_avg_ms", labels: {}, sum: 274.0, count: 14 }, SummaryPayload { name: "happy_eyeballs.conn_attempts_all", labels: {}, sum: 1.0, count: 1 }, SummaryPayload { name: "message_bus.bus_driver_request_duration_sec_nano_sec", labels: {}, sum: 17.75335487399994, count: 443950 }], gauges: [GaugesPayload { name: "ntp-clock.min_since_ntp_sync", labels: {}, value: 60.002434227 }, GaugesPayload { name: "happy_eyeballs.conn_attempts_current", labels: {}, value: 1.0 }] } context="timer"}: actor_statistics::handler: Skipped uploading aggregate stats because registration was none!
2025-08-05T01:33:07.827Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
NewNeighbour; Destination: **********; 
2025-08-05T01:33:09.414Z DEBUG slog: got a response for stream_id=11844, headers=[":status: 200", "cf-team: 2919757da400000482d678c400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T01:33:09.487Z DEBUG tunnel_loop{protocol="masque" con_id="6d3539c6af1e789ae39aef05871da34927b6218a"}: warp_edge::tunnel_loop: Tunnel loop statistics stats=TunnelLoopStats { tun_packet_too_large: 0, edge_packet_too_large: 0, tun_packet_too_small: 0, edge_packet_too_small: 0, tun_packet_martian: 0, tun_packet_unhandled: 21, dropped_edge_packet: 0, dropped_tun_packet: 0, dropped_dns_packet: 0, successfully_retried_tun_packet: 0 }
2025-08-05T01:33:24.414Z DEBUG slog: got a response for stream_id=11848, headers=[":status: 200", "cf-team: 291975b83b00000482d67d6400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T01:33:39.414Z DEBUG slog: got a response for stream_id=11852, headers=[":status: 200", "cf-team: 291975f2d400000482d681a400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T01:33:43.923Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
2025-08-05T01:33:52.631Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
NewNeighbour; Destination: **********; 
2025-08-05T01:33:54.414Z DEBUG slog: got a response for stream_id=11856, headers=[":status: 200", "cf-team: 2919762d6c00000482d685b400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T01:34:09.414Z DEBUG slog: got a response for stream_id=11860, headers=[":status: 200", "cf-team: 291976680300000482d6869400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T01:34:24.414Z DEBUG slog: got a response for stream_id=11864, headers=[":status: 200", "cf-team: 291976a29b00000482d688b400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T01:34:28.979Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
2025-08-05T01:34:36.659Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
NewNeighbour; Destination: **********; 
2025-08-05T01:34:39.414Z DEBUG slog: got a response for stream_id=11868, headers=[":status: 200", "cf-team: 291976dd3400000482d68ac400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T01:34:54.414Z DEBUG slog: got a response for stream_id=11872, headers=[":status: 200", "cf-team: 29197717cb00000482d68df400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T01:35:02.738Z DEBUG upload_stats{stats=StatsPayload { counters: [CounterPayload { name: "dns_proxy.doh_err_other", labels: {}, count: 3 }, CounterPayload { name: "message_bus.bus_driver_updates_sent", labels: {}, count: 1 }, CounterPayload { name: "watchdog.requests_recv", labels: {}, count: 445150 }, CounterPayload { name: "main-loop-compat.commands_recv", labels: {}, count: 2 }, CounterPayload { name: "device-state.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "alternate-network.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "registration-verifier.commands_recv", labels: {}, count: 1 }, CounterPayload { name: "message_bus.bus_driver_commands_sent", labels: {}, count: 4 }, CounterPayload { name: "main-loop-compat.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "statistics.commands_recv", labels: {}, count: 1 }], summaries: [SummaryPayload { name: "happy_eyeballs.conn_attempts_all", labels: {}, sum: 1.0, count: 1 }, SummaryPayload { name: "dns_proxy.doh_avg_ms", labels: {}, sum: 7713.0, count: 7370 }, SummaryPayload { name: "message_bus.bus_driver_request_duration_sec_nano_sec", labels: {}, sum: 17.801057658999962, count: 445150 }, SummaryPayload { name: "firewall.fw_avg_ms", labels: {}, sum: 274.0, count: 14 }], gauges: [GaugesPayload { name: "ntp-clock.min_since_ntp_sync", labels: {}, value: 59.999314502 }, GaugesPayload { name: "happy_eyeballs.conn_attempts_current", labels: {}, value: 1.0 }] } context="timer"}: actor_statistics::handler: Starting to upload stats
2025-08-05T01:35:02.738Z  WARN upload_stats{stats=StatsPayload { counters: [CounterPayload { name: "dns_proxy.doh_err_other", labels: {}, count: 3 }, CounterPayload { name: "message_bus.bus_driver_updates_sent", labels: {}, count: 1 }, CounterPayload { name: "watchdog.requests_recv", labels: {}, count: 445150 }, CounterPayload { name: "main-loop-compat.commands_recv", labels: {}, count: 2 }, CounterPayload { name: "device-state.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "alternate-network.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "registration-verifier.commands_recv", labels: {}, count: 1 }, CounterPayload { name: "message_bus.bus_driver_commands_sent", labels: {}, count: 4 }, CounterPayload { name: "main-loop-compat.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "statistics.commands_recv", labels: {}, count: 1 }], summaries: [SummaryPayload { name: "happy_eyeballs.conn_attempts_all", labels: {}, sum: 1.0, count: 1 }, SummaryPayload { name: "dns_proxy.doh_avg_ms", labels: {}, sum: 7713.0, count: 7370 }, SummaryPayload { name: "message_bus.bus_driver_request_duration_sec_nano_sec", labels: {}, sum: 17.801057658999962, count: 445150 }, SummaryPayload { name: "firewall.fw_avg_ms", labels: {}, sum: 274.0, count: 14 }], gauges: [GaugesPayload { name: "ntp-clock.min_since_ntp_sync", labels: {}, value: 59.999314502 }, GaugesPayload { name: "happy_eyeballs.conn_attempts_current", labels: {}, value: 1.0 }] } context="timer"}: actor_statistics::handler: Skipped uploading aggregate stats because registration was none!
2025-08-05T01:35:09.414Z DEBUG slog: got a response for stream_id=11876, headers=[":status: 200", "cf-team: 291977526300000482d690f400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T01:35:09.487Z DEBUG tunnel_loop{protocol="masque" con_id="6d3539c6af1e789ae39aef05871da34927b6218a"}: warp_edge::tunnel_loop: Tunnel loop statistics stats=TunnelLoopStats { tun_packet_too_large: 0, edge_packet_too_large: 0, tun_packet_too_small: 0, edge_packet_too_small: 0, tun_packet_martian: 0, tun_packet_unhandled: 21, dropped_edge_packet: 0, dropped_tun_packet: 0, dropped_dns_packet: 0, successfully_retried_tun_packet: 0 }
2025-08-05T01:35:11.987Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
2025-08-05T01:35:20.691Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
NewNeighbour; Destination: **********; 
2025-08-05T01:35:24.414Z DEBUG slog: got a response for stream_id=11880, headers=[":status: 200", "cf-team: 2919778cfc00000482d692d400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T01:35:39.413Z DEBUG slog: got a response for stream_id=11884, headers=[":status: 200", "cf-team: 291977c79300000482d696b400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T01:35:54.414Z DEBUG slog: got a response for stream_id=11888, headers=[":status: 200", "cf-team: 291978022c00000482d6988400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T01:35:57.043Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
2025-08-05T01:36:04.723Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
NewNeighbour; Destination: **********; 
2025-08-05T01:36:09.414Z DEBUG slog: got a response for stream_id=11892, headers=[":status: 200", "cf-team: 2919783cc300000482d69aa400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T01:36:24.414Z DEBUG slog: got a response for stream_id=11896, headers=[":status: 200", "cf-team: 291978775c00000482d69f9400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T01:36:39.414Z DEBUG slog: got a response for stream_id=11900, headers=[":status: 200", "cf-team: 291978b1f400000482d6a10400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T01:36:40.051Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
2025-08-05T01:36:45.939Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
NewNeighbour; Destination: **********; 
2025-08-05T01:36:54.414Z DEBUG slog: got a response for stream_id=11904, headers=[":status: 200", "cf-team: 291978ec8b00000482d6a60400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T01:37:02.733Z DEBUG upload_stats{stats=StatsPayload { counters: [CounterPayload { name: "dns_proxy.doh_err_other", labels: {}, count: 3 }, CounterPayload { name: "main-loop-compat.commands_recv", labels: {}, count: 2 }, CounterPayload { name: "alternate-network.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "main-loop-compat.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "device-state.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "message_bus.bus_driver_updates_sent", labels: {}, count: 1 }, CounterPayload { name: "watchdog.requests_recv", labels: {}, count: 446350 }, CounterPayload { name: "statistics.commands_recv", labels: {}, count: 1 }, CounterPayload { name: "registration-verifier.commands_recv", labels: {}, count: 1 }, CounterPayload { name: "message_bus.bus_driver_commands_sent", labels: {}, count: 4 }], summaries: [SummaryPayload { name: "firewall.fw_avg_ms", labels: {}, sum: 274.0, count: 14 }, SummaryPayload { name: "dns_proxy.doh_avg_ms", labels: {}, sum: 7748.0, count: 7390 }, SummaryPayload { name: "happy_eyeballs.conn_attempts_all", labels: {}, sum: 1.0, count: 1 }, SummaryPayload { name: "message_bus.bus_driver_request_duration_sec_nano_sec", labels: {}, sum: 17.85353179699996, count: 446350 }], gauges: [GaugesPayload { name: "ntp-clock.min_since_ntp_sync", labels: {}, value: 60.000355986 }, GaugesPayload { name: "happy_eyeballs.conn_attempts_current", labels: {}, value: 1.0 }] } context="timer"}: actor_statistics::handler: Starting to upload stats
2025-08-05T01:37:02.733Z  WARN upload_stats{stats=StatsPayload { counters: [CounterPayload { name: "dns_proxy.doh_err_other", labels: {}, count: 3 }, CounterPayload { name: "main-loop-compat.commands_recv", labels: {}, count: 2 }, CounterPayload { name: "alternate-network.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "main-loop-compat.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "device-state.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "message_bus.bus_driver_updates_sent", labels: {}, count: 1 }, CounterPayload { name: "watchdog.requests_recv", labels: {}, count: 446350 }, CounterPayload { name: "statistics.commands_recv", labels: {}, count: 1 }, CounterPayload { name: "registration-verifier.commands_recv", labels: {}, count: 1 }, CounterPayload { name: "message_bus.bus_driver_commands_sent", labels: {}, count: 4 }], summaries: [SummaryPayload { name: "firewall.fw_avg_ms", labels: {}, sum: 274.0, count: 14 }, SummaryPayload { name: "dns_proxy.doh_avg_ms", labels: {}, sum: 7748.0, count: 7390 }, SummaryPayload { name: "happy_eyeballs.conn_attempts_all", labels: {}, sum: 1.0, count: 1 }, SummaryPayload { name: "message_bus.bus_driver_request_duration_sec_nano_sec", labels: {}, sum: 17.85353179699996, count: 446350 }], gauges: [GaugesPayload { name: "ntp-clock.min_since_ntp_sync", labels: {}, value: 60.000355986 }, GaugesPayload { name: "happy_eyeballs.conn_attempts_current", labels: {}, value: 1.0 }] } context="timer"}: actor_statistics::handler: Skipped uploading aggregate stats because registration was none!
2025-08-05T01:37:09.415Z DEBUG slog: got a response for stream_id=11908, headers=[":status: 200", "cf-team: 291979272400000482d6a8a400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T01:37:09.487Z DEBUG tunnel_loop{protocol="masque" con_id="6d3539c6af1e789ae39aef05871da34927b6218a"}: warp_edge::tunnel_loop: Tunnel loop statistics stats=TunnelLoopStats { tun_packet_too_large: 0, edge_packet_too_large: 0, tun_packet_too_small: 0, edge_packet_too_small: 0, tun_packet_martian: 0, tun_packet_unhandled: 21, dropped_edge_packet: 0, dropped_tun_packet: 0, dropped_dns_packet: 0, successfully_retried_tun_packet: 0 }
2025-08-05T01:37:21.011Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
2025-08-05T01:37:24.414Z DEBUG slog: got a response for stream_id=11912, headers=[":status: 200", "cf-team: 29197961bb00000482d6a98400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T01:37:28.691Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
NewNeighbour; Destination: **********; 
2025-08-05T01:37:39.414Z DEBUG slog: got a response for stream_id=11916, headers=[":status: 200", "cf-team: 2919799c5300000482d6aba400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T01:37:54.414Z DEBUG slog: got a response for stream_id=11920, headers=[":status: 200", "cf-team: 291979d6ec00000482d6acb400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T01:38:04.019Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
2025-08-05T01:38:09.414Z DEBUG slog: got a response for stream_id=11924, headers=[":status: 200", "cf-team: 29197a118400000482d6ae0400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T01:38:12.723Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
NewNeighbour; Destination: **********; 
2025-08-05T01:38:24.414Z DEBUG slog: got a response for stream_id=11928, headers=[":status: 200", "cf-team: 29197a4c1b00000482d6b05400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T01:38:36.787Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
2025-08-05T01:38:39.413Z DEBUG slog: got a response for stream_id=11932, headers=[":status: 200", "cf-team: 29197a86b300000482d6bbf400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T01:38:44.471Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
NewNeighbour; Destination: **********; 
2025-08-05T01:38:54.414Z DEBUG slog: got a response for stream_id=11936, headers=[":status: 200", "cf-team: 29197ac14b00000482d6c02400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T01:39:02.735Z DEBUG upload_stats{stats=StatsPayload { counters: [CounterPayload { name: "main-loop-compat.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "main-loop-compat.commands_recv", labels: {}, count: 2 }, CounterPayload { name: "dns_proxy.doh_err_other", labels: {}, count: 3 }, CounterPayload { name: "message_bus.bus_driver_commands_sent", labels: {}, count: 4 }, CounterPayload { name: "message_bus.bus_driver_updates_sent", labels: {}, count: 1 }, CounterPayload { name: "alternate-network.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "registration-verifier.commands_recv", labels: {}, count: 1 }, CounterPayload { name: "device-state.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "statistics.commands_recv", labels: {}, count: 1 }, CounterPayload { name: "watchdog.requests_recv", labels: {}, count: 447550 }], summaries: [SummaryPayload { name: "message_bus.bus_driver_request_duration_sec_nano_sec", labels: {}, sum: 17.90748010499999, count: 447550 }, SummaryPayload { name: "dns_proxy.doh_avg_ms", labels: {}, sum: 7756.0, count: 7410 }, SummaryPayload { name: "firewall.fw_avg_ms", labels: {}, sum: 274.0, count: 14 }, SummaryPayload { name: "happy_eyeballs.conn_attempts_all", labels: {}, sum: 1.0, count: 1 }], gauges: [GaugesPayload { name: "ntp-clock.min_since_ntp_sync", labels: {}, value: 59.991993307 }, GaugesPayload { name: "happy_eyeballs.conn_attempts_current", labels: {}, value: 1.0 }] } context="timer"}: actor_statistics::handler: Starting to upload stats
2025-08-05T01:39:02.736Z  WARN upload_stats{stats=StatsPayload { counters: [CounterPayload { name: "main-loop-compat.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "main-loop-compat.commands_recv", labels: {}, count: 2 }, CounterPayload { name: "dns_proxy.doh_err_other", labels: {}, count: 3 }, CounterPayload { name: "message_bus.bus_driver_commands_sent", labels: {}, count: 4 }, CounterPayload { name: "message_bus.bus_driver_updates_sent", labels: {}, count: 1 }, CounterPayload { name: "alternate-network.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "registration-verifier.commands_recv", labels: {}, count: 1 }, CounterPayload { name: "device-state.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "statistics.commands_recv", labels: {}, count: 1 }, CounterPayload { name: "watchdog.requests_recv", labels: {}, count: 447550 }], summaries: [SummaryPayload { name: "message_bus.bus_driver_request_duration_sec_nano_sec", labels: {}, sum: 17.90748010499999, count: 447550 }, SummaryPayload { name: "dns_proxy.doh_avg_ms", labels: {}, sum: 7756.0, count: 7410 }, SummaryPayload { name: "firewall.fw_avg_ms", labels: {}, sum: 274.0, count: 14 }, SummaryPayload { name: "happy_eyeballs.conn_attempts_all", labels: {}, sum: 1.0, count: 1 }], gauges: [GaugesPayload { name: "ntp-clock.min_since_ntp_sync", labels: {}, value: 59.991993307 }, GaugesPayload { name: "happy_eyeballs.conn_attempts_current", labels: {}, value: 1.0 }] } context="timer"}: actor_statistics::handler: Skipped uploading aggregate stats because registration was none!
2025-08-05T01:39:09.415Z DEBUG slog: got a response for stream_id=11940, headers=[":status: 200", "cf-team: 29197afbe400000482d6c31400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T01:39:09.487Z DEBUG tunnel_loop{protocol="masque" con_id="6d3539c6af1e789ae39aef05871da34927b6218a"}: warp_edge::tunnel_loop: Tunnel loop statistics stats=TunnelLoopStats { tun_packet_too_large: 0, edge_packet_too_large: 0, tun_packet_too_small: 0, edge_packet_too_small: 0, tun_packet_martian: 0, tun_packet_unhandled: 21, dropped_edge_packet: 0, dropped_tun_packet: 0, dropped_dns_packet: 0, successfully_retried_tun_packet: 0 }
2025-08-05T01:39:09.555Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
2025-08-05T01:39:16.723Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
NewNeighbour; Destination: **********; 
2025-08-05T01:39:24.415Z DEBUG slog: got a response for stream_id=11944, headers=[":status: 200", "cf-team: 29197b367c00000482d6c53400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T01:39:39.414Z DEBUG slog: got a response for stream_id=11948, headers=[":status: 200", "cf-team: 29197b711300000482d6cb6400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T01:39:40.275Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
2025-08-05T01:39:48.467Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
NewNeighbour; Destination: **********; 
2025-08-05T01:39:54.414Z DEBUG slog: got a response for stream_id=11952, headers=[":status: 200", "cf-team: 29197babac00000482d6d38400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T01:40:09.414Z DEBUG slog: got a response for stream_id=11956, headers=[":status: 200", "cf-team: 29197be64300000482d6da2400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T01:40:13.043Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
2025-08-05T01:40:18.931Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
NewNeighbour; Destination: **********; 
2025-08-05T01:40:24.414Z DEBUG slog: got a response for stream_id=11960, headers=[":status: 200", "cf-team: 29197c20db00000482d6de4400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T01:40:39.413Z DEBUG slog: got a response for stream_id=11964, headers=[":status: 200", "cf-team: 29197c5b7300000482d6e1c400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T01:40:43.767Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
2025-08-05T01:40:49.395Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
NewNeighbour; Destination: **********; 
2025-08-05T01:40:54.414Z DEBUG slog: got a response for stream_id=11968, headers=[":status: 200", "cf-team: 29197c960b00000482d6e59400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T01:41:02.733Z DEBUG upload_stats{stats=StatsPayload { counters: [CounterPayload { name: "dns_proxy.doh_err_other", labels: {}, count: 3 }, CounterPayload { name: "registration-verifier.commands_recv", labels: {}, count: 1 }, CounterPayload { name: "main-loop-compat.commands_recv", labels: {}, count: 2 }, CounterPayload { name: "message_bus.bus_driver_commands_sent", labels: {}, count: 4 }, CounterPayload { name: "alternate-network.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "watchdog.requests_recv", labels: {}, count: 448750 }, CounterPayload { name: "statistics.commands_recv", labels: {}, count: 1 }, CounterPayload { name: "device-state.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "main-loop-compat.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "message_bus.bus_driver_updates_sent", labels: {}, count: 1 }], summaries: [SummaryPayload { name: "message_bus.bus_driver_request_duration_sec_nano_sec", labels: {}, sum: 17.963765131999846, count: 448750 }, SummaryPayload { name: "dns_proxy.doh_avg_ms", labels: {}, sum: 7771.0, count: 7430 }, SummaryPayload { name: "happy_eyeballs.conn_attempts_all", labels: {}, sum: 1.0, count: 1 }, SummaryPayload { name: "firewall.fw_avg_ms", labels: {}, sum: 274.0, count: 14 }], gauges: [GaugesPayload { name: "ntp-clock.min_since_ntp_sync", labels: {}, value: 59.997588416 }, GaugesPayload { name: "happy_eyeballs.conn_attempts_current", labels: {}, value: 1.0 }] } context="timer"}: actor_statistics::handler: Starting to upload stats
2025-08-05T01:41:02.733Z  WARN upload_stats{stats=StatsPayload { counters: [CounterPayload { name: "dns_proxy.doh_err_other", labels: {}, count: 3 }, CounterPayload { name: "registration-verifier.commands_recv", labels: {}, count: 1 }, CounterPayload { name: "main-loop-compat.commands_recv", labels: {}, count: 2 }, CounterPayload { name: "message_bus.bus_driver_commands_sent", labels: {}, count: 4 }, CounterPayload { name: "alternate-network.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "watchdog.requests_recv", labels: {}, count: 448750 }, CounterPayload { name: "statistics.commands_recv", labels: {}, count: 1 }, CounterPayload { name: "device-state.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "main-loop-compat.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "message_bus.bus_driver_updates_sent", labels: {}, count: 1 }], summaries: [SummaryPayload { name: "message_bus.bus_driver_request_duration_sec_nano_sec", labels: {}, sum: 17.963765131999846, count: 448750 }, SummaryPayload { name: "dns_proxy.doh_avg_ms", labels: {}, sum: 7771.0, count: 7430 }, SummaryPayload { name: "happy_eyeballs.conn_attempts_all", labels: {}, sum: 1.0, count: 1 }, SummaryPayload { name: "firewall.fw_avg_ms", labels: {}, sum: 274.0, count: 14 }], gauges: [GaugesPayload { name: "ntp-clock.min_since_ntp_sync", labels: {}, value: 59.997588416 }, GaugesPayload { name: "happy_eyeballs.conn_attempts_current", labels: {}, value: 1.0 }] } context="timer"}: actor_statistics::handler: Skipped uploading aggregate stats because registration was none!
2025-08-05T01:41:09.414Z DEBUG slog: got a response for stream_id=11972, headers=[":status: 200", "cf-team: 29197cd0a300000482d6e93400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T01:41:09.487Z DEBUG tunnel_loop{protocol="masque" con_id="6d3539c6af1e789ae39aef05871da34927b6218a"}: warp_edge::tunnel_loop: Tunnel loop statistics stats=TunnelLoopStats { tun_packet_too_large: 0, edge_packet_too_large: 0, tun_packet_too_small: 0, edge_packet_too_small: 0, tun_packet_martian: 0, tun_packet_unhandled: 21, dropped_edge_packet: 0, dropped_tun_packet: 0, dropped_dns_packet: 0, successfully_retried_tun_packet: 0 }
2025-08-05T01:41:14.483Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
2025-08-05T01:41:19.859Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
NewNeighbour; Destination: **********; 
2025-08-05T01:41:24.414Z DEBUG slog: got a response for stream_id=11976, headers=[":status: 200", "cf-team: 29197d0b3c00000482d6ea6400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T01:41:39.414Z DEBUG slog: got a response for stream_id=11980, headers=[":status: 200", "cf-team: 29197d45d300000482d6eb6400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T01:41:45.203Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
2025-08-05T01:41:52.627Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
NewNeighbour; Destination: **********; 
2025-08-05T01:41:54.414Z DEBUG slog: got a response for stream_id=11984, headers=[":status: 200", "cf-team: 29197d806b00000482d6ed0400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T01:42:09.414Z DEBUG slog: got a response for stream_id=11988, headers=[":status: 200", "cf-team: 29197dbb0300000482d6ee2400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T01:42:17.975Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
2025-08-05T01:42:24.414Z DEBUG slog: got a response for stream_id=11992, headers=[":status: 200", "cf-team: 29197df59b00000482d6eff400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T01:42:24.627Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
NewNeighbour; Destination: **********; 
2025-08-05T01:42:39.414Z DEBUG slog: got a response for stream_id=11996, headers=[":status: 200", "cf-team: 29197e303400000482d6f4a400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T01:42:54.414Z DEBUG slog: got a response for stream_id=12000, headers=[":status: 200", "cf-team: 29197e6acc00000482d6f80400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T01:42:56.887Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
2025-08-05T01:43:02.734Z DEBUG upload_stats{stats=StatsPayload { counters: [CounterPayload { name: "device-state.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "message_bus.bus_driver_updates_sent", labels: {}, count: 1 }, CounterPayload { name: "alternate-network.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "statistics.commands_recv", labels: {}, count: 1 }, CounterPayload { name: "registration-verifier.commands_recv", labels: {}, count: 1 }, CounterPayload { name: "main-loop-compat.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "dns_proxy.doh_err_other", labels: {}, count: 3 }, CounterPayload { name: "message_bus.bus_driver_commands_sent", labels: {}, count: 4 }, CounterPayload { name: "main-loop-compat.commands_recv", labels: {}, count: 2 }, CounterPayload { name: "watchdog.requests_recv", labels: {}, count: 449950 }], summaries: [SummaryPayload { name: "happy_eyeballs.conn_attempts_all", labels: {}, sum: 1.0, count: 1 }, SummaryPayload { name: "dns_proxy.doh_avg_ms", labels: {}, sum: 7800.0, count: 7450 }, SummaryPayload { name: "message_bus.bus_driver_request_duration_sec_nano_sec", labels: {}, sum: 18.014311469999786, count: 449950 }, SummaryPayload { name: "firewall.fw_avg_ms", labels: {}, sum: 274.0, count: 14 }], gauges: [GaugesPayload { name: "ntp-clock.min_since_ntp_sync", labels: {}, value: 60.001272473 }, GaugesPayload { name: "happy_eyeballs.conn_attempts_current", labels: {}, value: 1.0 }] } context="timer"}: actor_statistics::handler: Starting to upload stats
2025-08-05T01:43:02.734Z  WARN upload_stats{stats=StatsPayload { counters: [CounterPayload { name: "device-state.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "message_bus.bus_driver_updates_sent", labels: {}, count: 1 }, CounterPayload { name: "alternate-network.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "statistics.commands_recv", labels: {}, count: 1 }, CounterPayload { name: "registration-verifier.commands_recv", labels: {}, count: 1 }, CounterPayload { name: "main-loop-compat.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "dns_proxy.doh_err_other", labels: {}, count: 3 }, CounterPayload { name: "message_bus.bus_driver_commands_sent", labels: {}, count: 4 }, CounterPayload { name: "main-loop-compat.commands_recv", labels: {}, count: 2 }, CounterPayload { name: "watchdog.requests_recv", labels: {}, count: 449950 }], summaries: [SummaryPayload { name: "happy_eyeballs.conn_attempts_all", labels: {}, sum: 1.0, count: 1 }, SummaryPayload { name: "dns_proxy.doh_avg_ms", labels: {}, sum: 7800.0, count: 7450 }, SummaryPayload { name: "message_bus.bus_driver_request_duration_sec_nano_sec", labels: {}, sum: 18.014311469999786, count: 449950 }, SummaryPayload { name: "firewall.fw_avg_ms", labels: {}, sum: 274.0, count: 14 }], gauges: [GaugesPayload { name: "ntp-clock.min_since_ntp_sync", labels: {}, value: 60.001272473 }, GaugesPayload { name: "happy_eyeballs.conn_attempts_current", labels: {}, value: 1.0 }] } context="timer"}: actor_statistics::handler: Skipped uploading aggregate stats because registration was none!
2025-08-05T01:43:04.563Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
NewNeighbour; Destination: **********; 
2025-08-05T01:43:09.414Z DEBUG slog: got a response for stream_id=12004, headers=[":status: 200", "cf-team: 29197ea56400000482d6fac400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T01:43:09.486Z DEBUG tunnel_loop{protocol="masque" con_id="6d3539c6af1e789ae39aef05871da34927b6218a"}: warp_edge::tunnel_loop: Tunnel loop statistics stats=TunnelLoopStats { tun_packet_too_large: 0, edge_packet_too_large: 0, tun_packet_too_small: 0, edge_packet_too_small: 0, tun_packet_martian: 0, tun_packet_unhandled: 21, dropped_edge_packet: 0, dropped_tun_packet: 0, dropped_dns_packet: 0, successfully_retried_tun_packet: 0 }
2025-08-05T01:43:24.414Z DEBUG slog: got a response for stream_id=12008, headers=[":status: 200", "cf-team: 29197edffc00000482d6fcf400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T01:43:37.843Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
2025-08-05T01:43:39.414Z DEBUG slog: got a response for stream_id=12012, headers=[":status: 200", "cf-team: 29197f1a9400000482d6fe7400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T01:43:44.499Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
NewNeighbour; Destination: **********; 
2025-08-05T01:43:54.414Z DEBUG slog: got a response for stream_id=12016, headers=[":status: 200", "cf-team: 29197f552c00000482d6ffe400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T01:44:09.415Z DEBUG slog: got a response for stream_id=12020, headers=[":status: 200", "cf-team: 29197f8fc400000482d7024400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T01:44:16.755Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
2025-08-05T01:44:21.875Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
NewNeighbour; Destination: **********; 
2025-08-05T01:44:24.414Z DEBUG slog: got a response for stream_id=12024, headers=[":status: 200", "cf-team: 29197fca5c00000482d703c400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T01:44:39.414Z DEBUG slog: got a response for stream_id=12028, headers=[":status: 200", "cf-team: 29198004f400000482d7068400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T01:44:54.414Z DEBUG slog: got a response for stream_id=12032, headers=[":status: 200", "cf-team: 2919803f8c00000482d707b400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T01:44:55.667Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
2025-08-05T01:45:02.734Z DEBUG upload_stats{stats=StatsPayload { counters: [CounterPayload { name: "device-state.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "message_bus.bus_driver_updates_sent", labels: {}, count: 1 }, CounterPayload { name: "alternate-network.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "registration-verifier.commands_recv", labels: {}, count: 1 }, CounterPayload { name: "watchdog.requests_recv", labels: {}, count: 451150 }, CounterPayload { name: "main-loop-compat.commands_recv", labels: {}, count: 2 }, CounterPayload { name: "message_bus.bus_driver_commands_sent", labels: {}, count: 4 }, CounterPayload { name: "dns_proxy.doh_err_other", labels: {}, count: 3 }, CounterPayload { name: "main-loop-compat.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "statistics.commands_recv", labels: {}, count: 1 }], summaries: [SummaryPayload { name: "firewall.fw_avg_ms", labels: {}, sum: 274.0, count: 14 }, SummaryPayload { name: "happy_eyeballs.conn_attempts_all", labels: {}, sum: 1.0, count: 1 }, SummaryPayload { name: "message_bus.bus_driver_request_duration_sec_nano_sec", labels: {}, sum: 18.066329458999846, count: 451150 }, SummaryPayload { name: "dns_proxy.doh_avg_ms", labels: {}, sum: 7800.0, count: 7470 }], gauges: [GaugesPayload { name: "happy_eyeballs.conn_attempts_current", labels: {}, value: 1.0 }, GaugesPayload { name: "ntp-clock.min_since_ntp_sync", labels: {}, value: 60.00039963 }] } context="timer"}: actor_statistics::handler: Starting to upload stats
2025-08-05T01:45:02.734Z  WARN upload_stats{stats=StatsPayload { counters: [CounterPayload { name: "device-state.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "message_bus.bus_driver_updates_sent", labels: {}, count: 1 }, CounterPayload { name: "alternate-network.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "registration-verifier.commands_recv", labels: {}, count: 1 }, CounterPayload { name: "watchdog.requests_recv", labels: {}, count: 451150 }, CounterPayload { name: "main-loop-compat.commands_recv", labels: {}, count: 2 }, CounterPayload { name: "message_bus.bus_driver_commands_sent", labels: {}, count: 4 }, CounterPayload { name: "dns_proxy.doh_err_other", labels: {}, count: 3 }, CounterPayload { name: "main-loop-compat.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "statistics.commands_recv", labels: {}, count: 1 }], summaries: [SummaryPayload { name: "firewall.fw_avg_ms", labels: {}, sum: 274.0, count: 14 }, SummaryPayload { name: "happy_eyeballs.conn_attempts_all", labels: {}, sum: 1.0, count: 1 }, SummaryPayload { name: "message_bus.bus_driver_request_duration_sec_nano_sec", labels: {}, sum: 18.066329458999846, count: 451150 }, SummaryPayload { name: "dns_proxy.doh_avg_ms", labels: {}, sum: 7800.0, count: 7470 }], gauges: [GaugesPayload { name: "happy_eyeballs.conn_attempts_current", labels: {}, value: 1.0 }, GaugesPayload { name: "ntp-clock.min_since_ntp_sync", labels: {}, value: 60.00039963 }] } context="timer"}: actor_statistics::handler: Skipped uploading aggregate stats because registration was none!
2025-08-05T01:45:04.627Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
NewNeighbour; Destination: **********; 
2025-08-05T01:45:09.414Z DEBUG slog: got a response for stream_id=12036, headers=[":status: 200", "cf-team: 2919807a2500000482d70bd400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T01:45:09.486Z DEBUG tunnel_loop{protocol="masque" con_id="6d3539c6af1e789ae39aef05871da34927b6218a"}: warp_edge::tunnel_loop: Tunnel loop statistics stats=TunnelLoopStats { tun_packet_too_large: 0, edge_packet_too_large: 0, tun_packet_too_small: 0, edge_packet_too_small: 0, tun_packet_martian: 0, tun_packet_unhandled: 21, dropped_edge_packet: 0, dropped_tun_packet: 0, dropped_dns_packet: 0, successfully_retried_tun_packet: 0 }
2025-08-05T01:45:24.414Z DEBUG slog: got a response for stream_id=12040, headers=[":status: 200", "cf-team: 291980b4bc00000482d70de400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T01:45:38.675Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
2025-08-05T01:45:39.414Z DEBUG slog: got a response for stream_id=12044, headers=[":status: 200", "cf-team: 291980ef5400000482d7103400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T01:45:44.563Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
NewNeighbour; Destination: **********; 
2025-08-05T01:45:54.414Z DEBUG slog: got a response for stream_id=12048, headers=[":status: 200", "cf-team: 29198129ec00000482d7127400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T01:46:09.414Z DEBUG slog: got a response for stream_id=12052, headers=[":status: 200", "cf-team: 291981648400000482d7149400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T01:46:17.587Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
2025-08-05T01:46:23.475Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
NewNeighbour; Destination: **********; 
2025-08-05T01:46:24.414Z DEBUG slog: got a response for stream_id=12056, headers=[":status: 200", "cf-team: 2919819f1d00000482d7165400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T01:46:39.414Z DEBUG slog: got a response for stream_id=12060, headers=[":status: 200", "cf-team: 291981d9b400000482d7176400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T01:46:54.414Z DEBUG slog: got a response for stream_id=12064, headers=[":status: 200", "cf-team: 291982144c00000482d7184400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T01:46:56.503Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
2025-08-05T01:47:02.735Z DEBUG upload_stats{stats=StatsPayload { counters: [CounterPayload { name: "message_bus.bus_driver_updates_sent", labels: {}, count: 1 }, CounterPayload { name: "registration-verifier.commands_recv", labels: {}, count: 1 }, CounterPayload { name: "statistics.commands_recv", labels: {}, count: 1 }, CounterPayload { name: "dns_proxy.doh_err_other", labels: {}, count: 3 }, CounterPayload { name: "device-state.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "watchdog.requests_recv", labels: {}, count: 452350 }, CounterPayload { name: "main-loop-compat.commands_recv", labels: {}, count: 2 }, CounterPayload { name: "message_bus.bus_driver_commands_sent", labels: {}, count: 4 }, CounterPayload { name: "main-loop-compat.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "alternate-network.updates_recv", labels: {}, count: 1 }], summaries: [SummaryPayload { name: "message_bus.bus_driver_request_duration_sec_nano_sec", labels: {}, sum: 18.118126730999894, count: 452350 }, SummaryPayload { name: "firewall.fw_avg_ms", labels: {}, sum: 274.0, count: 14 }, SummaryPayload { name: "dns_proxy.doh_avg_ms", labels: {}, sum: 7830.0, count: 7488 }, SummaryPayload { name: "happy_eyeballs.conn_attempts_all", labels: {}, sum: 1.0, count: 1 }], gauges: [GaugesPayload { name: "happy_eyeballs.conn_attempts_current", labels: {}, value: 1.0 }, GaugesPayload { name: "ntp-clock.min_since_ntp_sync", labels: {}, value: 59.999268578 }] } context="timer"}: actor_statistics::handler: Starting to upload stats
2025-08-05T01:47:02.735Z  WARN upload_stats{stats=StatsPayload { counters: [CounterPayload { name: "message_bus.bus_driver_updates_sent", labels: {}, count: 1 }, CounterPayload { name: "registration-verifier.commands_recv", labels: {}, count: 1 }, CounterPayload { name: "statistics.commands_recv", labels: {}, count: 1 }, CounterPayload { name: "dns_proxy.doh_err_other", labels: {}, count: 3 }, CounterPayload { name: "device-state.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "watchdog.requests_recv", labels: {}, count: 452350 }, CounterPayload { name: "main-loop-compat.commands_recv", labels: {}, count: 2 }, CounterPayload { name: "message_bus.bus_driver_commands_sent", labels: {}, count: 4 }, CounterPayload { name: "main-loop-compat.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "alternate-network.updates_recv", labels: {}, count: 1 }], summaries: [SummaryPayload { name: "message_bus.bus_driver_request_duration_sec_nano_sec", labels: {}, sum: 18.118126730999894, count: 452350 }, SummaryPayload { name: "firewall.fw_avg_ms", labels: {}, sum: 274.0, count: 14 }, SummaryPayload { name: "dns_proxy.doh_avg_ms", labels: {}, sum: 7830.0, count: 7488 }, SummaryPayload { name: "happy_eyeballs.conn_attempts_all", labels: {}, sum: 1.0, count: 1 }], gauges: [GaugesPayload { name: "happy_eyeballs.conn_attempts_current", labels: {}, value: 1.0 }, GaugesPayload { name: "ntp-clock.min_since_ntp_sync", labels: {}, value: 59.999268578 }] } context="timer"}: actor_statistics::handler: Skipped uploading aggregate stats because registration was none!
2025-08-05T01:47:04.691Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
NewNeighbour; Destination: **********; 
2025-08-05T01:47:09.414Z DEBUG slog: got a response for stream_id=12068, headers=[":status: 200", "cf-team: 2919824ee500000482d7199400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T01:47:09.487Z DEBUG tunnel_loop{protocol="masque" con_id="6d3539c6af1e789ae39aef05871da34927b6218a"}: warp_edge::tunnel_loop: Tunnel loop statistics stats=TunnelLoopStats { tun_packet_too_large: 0, edge_packet_too_large: 0, tun_packet_too_small: 0, edge_packet_too_small: 0, tun_packet_martian: 0, tun_packet_unhandled: 21, dropped_edge_packet: 0, dropped_tun_packet: 0, dropped_dns_packet: 0, successfully_retried_tun_packet: 0 }
2025-08-05T01:47:24.414Z DEBUG slog: got a response for stream_id=12072, headers=[":status: 200", "cf-team: 291982897d00000482d71b2400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T01:47:37.459Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
2025-08-05T01:47:39.414Z DEBUG slog: got a response for stream_id=12076, headers=[":status: 200", "cf-team: 291982c41500000482d71d6400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T01:47:44.627Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
NewNeighbour; Destination: **********; 
2025-08-05T01:47:54.414Z DEBUG slog: got a response for stream_id=12080, headers=[":status: 200", "cf-team: 291982feac00000482d720a400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T01:48:09.415Z DEBUG slog: got a response for stream_id=12084, headers=[":status: 200", "cf-team: 291983394500000482d7231400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T01:48:18.419Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
2025-08-05T01:48:24.414Z DEBUG slog: got a response for stream_id=12088, headers=[":status: 200", "cf-team: 29198373dd00000482d726c400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T01:48:24.563Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
NewNeighbour; Destination: **********; 
2025-08-05T01:48:39.414Z DEBUG slog: got a response for stream_id=12092, headers=[":status: 200", "cf-team: 291983ae7500000482d729f400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T01:48:54.414Z DEBUG slog: got a response for stream_id=12096, headers=[":status: 200", "cf-team: 291983e90d00000482d72c9400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T01:48:57.331Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
2025-08-05T01:49:02.735Z DEBUG upload_stats{stats=StatsPayload { counters: [CounterPayload { name: "dns_proxy.doh_err_other", labels: {}, count: 3 }, CounterPayload { name: "alternate-network.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "registration-verifier.commands_recv", labels: {}, count: 1 }, CounterPayload { name: "watchdog.requests_recv", labels: {}, count: 453550 }, CounterPayload { name: "main-loop-compat.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "message_bus.bus_driver_commands_sent", labels: {}, count: 4 }, CounterPayload { name: "main-loop-compat.commands_recv", labels: {}, count: 2 }, CounterPayload { name: "statistics.commands_recv", labels: {}, count: 1 }, CounterPayload { name: "message_bus.bus_driver_updates_sent", labels: {}, count: 1 }, CounterPayload { name: "device-state.updates_recv", labels: {}, count: 1 }], summaries: [SummaryPayload { name: "firewall.fw_avg_ms", labels: {}, sum: 274.0, count: 14 }, SummaryPayload { name: "message_bus.bus_driver_request_duration_sec_nano_sec", labels: {}, sum: 18.167536513999874, count: 453550 }, SummaryPayload { name: "happy_eyeballs.conn_attempts_all", labels: {}, sum: 1.0, count: 1 }, SummaryPayload { name: "dns_proxy.doh_avg_ms", labels: {}, sum: 7847.0, count: 7508 }], gauges: [GaugesPayload { name: "happy_eyeballs.conn_attempts_current", labels: {}, value: 1.0 }, GaugesPayload { name: "ntp-clock.min_since_ntp_sync", labels: {}, value: 59.990231097 }] } context="timer"}: actor_statistics::handler: Starting to upload stats
2025-08-05T01:49:02.735Z  WARN upload_stats{stats=StatsPayload { counters: [CounterPayload { name: "dns_proxy.doh_err_other", labels: {}, count: 3 }, CounterPayload { name: "alternate-network.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "registration-verifier.commands_recv", labels: {}, count: 1 }, CounterPayload { name: "watchdog.requests_recv", labels: {}, count: 453550 }, CounterPayload { name: "main-loop-compat.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "message_bus.bus_driver_commands_sent", labels: {}, count: 4 }, CounterPayload { name: "main-loop-compat.commands_recv", labels: {}, count: 2 }, CounterPayload { name: "statistics.commands_recv", labels: {}, count: 1 }, CounterPayload { name: "message_bus.bus_driver_updates_sent", labels: {}, count: 1 }, CounterPayload { name: "device-state.updates_recv", labels: {}, count: 1 }], summaries: [SummaryPayload { name: "firewall.fw_avg_ms", labels: {}, sum: 274.0, count: 14 }, SummaryPayload { name: "message_bus.bus_driver_request_duration_sec_nano_sec", labels: {}, sum: 18.167536513999874, count: 453550 }, SummaryPayload { name: "happy_eyeballs.conn_attempts_all", labels: {}, sum: 1.0, count: 1 }, SummaryPayload { name: "dns_proxy.doh_avg_ms", labels: {}, sum: 7847.0, count: 7508 }], gauges: [GaugesPayload { name: "happy_eyeballs.conn_attempts_current", labels: {}, value: 1.0 }, GaugesPayload { name: "ntp-clock.min_since_ntp_sync", labels: {}, value: 59.990231097 }] } context="timer"}: actor_statistics::handler: Skipped uploading aggregate stats because registration was none!
2025-08-05T01:49:04.755Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
NewNeighbour; Destination: **********; 
2025-08-05T01:49:09.414Z DEBUG slog: got a response for stream_id=12100, headers=[":status: 200", "cf-team: 29198423a500000482d72e6400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T01:49:09.487Z DEBUG tunnel_loop{protocol="masque" con_id="6d3539c6af1e789ae39aef05871da34927b6218a"}: warp_edge::tunnel_loop: Tunnel loop statistics stats=TunnelLoopStats { tun_packet_too_large: 0, edge_packet_too_large: 0, tun_packet_too_small: 0, edge_packet_too_small: 0, tun_packet_martian: 0, tun_packet_unhandled: 21, dropped_edge_packet: 0, dropped_tun_packet: 0, dropped_dns_packet: 0, successfully_retried_tun_packet: 0 }
2025-08-05T01:49:24.414Z DEBUG slog: got a response for stream_id=12104, headers=[":status: 200", "cf-team: 2919845e3d00000482d72f8400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T01:49:36.247Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
2025-08-05T01:49:39.414Z DEBUG slog: got a response for stream_id=12108, headers=[":status: 200", "cf-team: 29198498d500000482d7325400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T01:49:44.435Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
NewNeighbour; Destination: **********; 
2025-08-05T01:49:54.414Z DEBUG slog: got a response for stream_id=12112, headers=[":status: 200", "cf-team: 291984d36d00000482d7353400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T01:50:09.414Z DEBUG slog: got a response for stream_id=12116, headers=[":status: 200", "cf-team: 2919850e0500000482d7377400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T01:50:17.203Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
2025-08-05T01:50:24.414Z DEBUG slog: got a response for stream_id=12120, headers=[":status: 200", "cf-team: 291985489d00000482d73cd400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T01:50:24.627Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
NewNeighbour; Destination: **********; 
2025-08-05T01:50:39.414Z DEBUG slog: got a response for stream_id=12124, headers=[":status: 200", "cf-team: 291985833500000482d73f0400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T01:50:54.414Z DEBUG slog: got a response for stream_id=12128, headers=[":status: 200", "cf-team: 291985bdcd00000482d742d400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T01:50:56.115Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
2025-08-05T01:51:02.734Z DEBUG upload_stats{stats=StatsPayload { counters: [CounterPayload { name: "alternate-network.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "device-state.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "statistics.commands_recv", labels: {}, count: 1 }, CounterPayload { name: "message_bus.bus_driver_updates_sent", labels: {}, count: 1 }, CounterPayload { name: "dns_proxy.doh_err_other", labels: {}, count: 3 }, CounterPayload { name: "main-loop-compat.commands_recv", labels: {}, count: 2 }, CounterPayload { name: "watchdog.requests_recv", labels: {}, count: 454750 }, CounterPayload { name: "main-loop-compat.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "message_bus.bus_driver_commands_sent", labels: {}, count: 4 }, CounterPayload { name: "registration-verifier.commands_recv", labels: {}, count: 1 }], summaries: [SummaryPayload { name: "happy_eyeballs.conn_attempts_all", labels: {}, sum: 1.0, count: 1 }, SummaryPayload { name: "message_bus.bus_driver_request_duration_sec_nano_sec", labels: {}, sum: 18.216930271999885, count: 454750 }, SummaryPayload { name: "firewall.fw_avg_ms", labels: {}, sum: 274.0, count: 14 }, SummaryPayload { name: "dns_proxy.doh_avg_ms", labels: {}, sum: 7855.0, count: 7528 }], gauges: [GaugesPayload { name: "happy_eyeballs.conn_attempts_current", labels: {}, value: 1.0 }, GaugesPayload { name: "ntp-clock.min_since_ntp_sync", labels: {}, value: 60.000549206 }] } context="timer"}: actor_statistics::handler: Starting to upload stats
2025-08-05T01:51:02.734Z  WARN upload_stats{stats=StatsPayload { counters: [CounterPayload { name: "alternate-network.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "device-state.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "statistics.commands_recv", labels: {}, count: 1 }, CounterPayload { name: "message_bus.bus_driver_updates_sent", labels: {}, count: 1 }, CounterPayload { name: "dns_proxy.doh_err_other", labels: {}, count: 3 }, CounterPayload { name: "main-loop-compat.commands_recv", labels: {}, count: 2 }, CounterPayload { name: "watchdog.requests_recv", labels: {}, count: 454750 }, CounterPayload { name: "main-loop-compat.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "message_bus.bus_driver_commands_sent", labels: {}, count: 4 }, CounterPayload { name: "registration-verifier.commands_recv", labels: {}, count: 1 }], summaries: [SummaryPayload { name: "happy_eyeballs.conn_attempts_all", labels: {}, sum: 1.0, count: 1 }, SummaryPayload { name: "message_bus.bus_driver_request_duration_sec_nano_sec", labels: {}, sum: 18.216930271999885, count: 454750 }, SummaryPayload { name: "firewall.fw_avg_ms", labels: {}, sum: 274.0, count: 14 }, SummaryPayload { name: "dns_proxy.doh_avg_ms", labels: {}, sum: 7855.0, count: 7528 }], gauges: [GaugesPayload { name: "happy_eyeballs.conn_attempts_current", labels: {}, value: 1.0 }, GaugesPayload { name: "ntp-clock.min_since_ntp_sync", labels: {}, value: 60.000549206 }] } context="timer"}: actor_statistics::handler: Skipped uploading aggregate stats because registration was none!
2025-08-05T01:51:04.563Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
NewNeighbour; Destination: **********; 
2025-08-05T01:51:09.414Z DEBUG slog: got a response for stream_id=12132, headers=[":status: 200", "cf-team: 291985f86500000482d7450400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T01:51:09.486Z DEBUG tunnel_loop{protocol="masque" con_id="6d3539c6af1e789ae39aef05871da34927b6218a"}: warp_edge::tunnel_loop: Tunnel loop statistics stats=TunnelLoopStats { tun_packet_too_large: 0, edge_packet_too_large: 0, tun_packet_too_small: 0, edge_packet_too_small: 0, tun_packet_martian: 0, tun_packet_unhandled: 21, dropped_edge_packet: 0, dropped_tun_packet: 0, dropped_dns_packet: 0, successfully_retried_tun_packet: 0 }
2025-08-05T01:51:24.414Z DEBUG slog: got a response for stream_id=12136, headers=[":status: 200", "cf-team: 29198632fd00000482d746c400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T01:51:37.075Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
2025-08-05T01:51:39.414Z DEBUG slog: got a response for stream_id=12140, headers=[":status: 200", "cf-team: 2919866d9500000482d7484400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T01:51:42.451Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
NewNeighbour; Destination: **********; 
2025-08-05T01:51:54.414Z DEBUG slog: got a response for stream_id=12144, headers=[":status: 200", "cf-team: 291986a82d00000482d74a5400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T01:52:09.414Z DEBUG slog: got a response for stream_id=12148, headers=[":status: 200", "cf-team: 291986e2c500000482d74cd400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T01:52:13.939Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
2025-08-05T01:52:20.595Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
NewNeighbour; Destination: **********; 
2025-08-05T01:52:24.414Z DEBUG slog: got a response for stream_id=12152, headers=[":status: 200", "cf-team: 2919871d5d00000482d74ec400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T01:52:39.414Z DEBUG slog: got a response for stream_id=12156, headers=[":status: 200", "cf-team: 29198757f500000482d750f400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T01:52:54.414Z DEBUG slog: got a response for stream_id=12160, headers=[":status: 200", "cf-team: 291987928e00000482d751b400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T01:53:02.733Z DEBUG upload_stats{stats=StatsPayload { counters: [CounterPayload { name: "message_bus.bus_driver_updates_sent", labels: {}, count: 1 }, CounterPayload { name: "statistics.commands_recv", labels: {}, count: 1 }, CounterPayload { name: "alternate-network.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "message_bus.bus_driver_commands_sent", labels: {}, count: 4 }, CounterPayload { name: "registration-verifier.commands_recv", labels: {}, count: 1 }, CounterPayload { name: "main-loop-compat.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "device-state.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "main-loop-compat.commands_recv", labels: {}, count: 2 }, CounterPayload { name: "dns_proxy.doh_err_other", labels: {}, count: 3 }, CounterPayload { name: "watchdog.requests_recv", labels: {}, count: 455950 }], summaries: [SummaryPayload { name: "message_bus.bus_driver_request_duration_sec_nano_sec", labels: {}, sum: 18.265661732999902, count: 455950 }, SummaryPayload { name: "dns_proxy.doh_avg_ms", labels: {}, sum: 7869.0, count: 7548 }, SummaryPayload { name: "firewall.fw_avg_ms", labels: {}, sum: 274.0, count: 14 }, SummaryPayload { name: "happy_eyeballs.conn_attempts_all", labels: {}, sum: 1.0, count: 1 }], gauges: [GaugesPayload { name: "happy_eyeballs.conn_attempts_current", labels: {}, value: 1.0 }, GaugesPayload { name: "ntp-clock.min_since_ntp_sync", labels: {}, value: 59.993898332 }] } context="timer"}: actor_statistics::handler: Starting to upload stats
2025-08-05T01:53:02.733Z  WARN upload_stats{stats=StatsPayload { counters: [CounterPayload { name: "message_bus.bus_driver_updates_sent", labels: {}, count: 1 }, CounterPayload { name: "statistics.commands_recv", labels: {}, count: 1 }, CounterPayload { name: "alternate-network.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "message_bus.bus_driver_commands_sent", labels: {}, count: 4 }, CounterPayload { name: "registration-verifier.commands_recv", labels: {}, count: 1 }, CounterPayload { name: "main-loop-compat.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "device-state.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "main-loop-compat.commands_recv", labels: {}, count: 2 }, CounterPayload { name: "dns_proxy.doh_err_other", labels: {}, count: 3 }, CounterPayload { name: "watchdog.requests_recv", labels: {}, count: 455950 }], summaries: [SummaryPayload { name: "message_bus.bus_driver_request_duration_sec_nano_sec", labels: {}, sum: 18.265661732999902, count: 455950 }, SummaryPayload { name: "dns_proxy.doh_avg_ms", labels: {}, sum: 7869.0, count: 7548 }, SummaryPayload { name: "firewall.fw_avg_ms", labels: {}, sum: 274.0, count: 14 }, SummaryPayload { name: "happy_eyeballs.conn_attempts_all", labels: {}, sum: 1.0, count: 1 }], gauges: [GaugesPayload { name: "happy_eyeballs.conn_attempts_current", labels: {}, value: 1.0 }, GaugesPayload { name: "ntp-clock.min_since_ntp_sync", labels: {}, value: 59.993898332 }] } context="timer"}: actor_statistics::handler: Skipped uploading aggregate stats because registration was none!
2025-08-05T01:53:05.651Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
2025-08-05T01:53:09.414Z DEBUG slog: got a response for stream_id=12164, headers=[":status: 200", "cf-team: 291987cd2500000482d752f400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T01:53:09.487Z DEBUG tunnel_loop{protocol="masque" con_id="6d3539c6af1e789ae39aef05871da34927b6218a"}: warp_edge::tunnel_loop: Tunnel loop statistics stats=TunnelLoopStats { tun_packet_too_large: 0, edge_packet_too_large: 0, tun_packet_too_small: 0, edge_packet_too_small: 0, tun_packet_martian: 0, tun_packet_unhandled: 21, dropped_edge_packet: 0, dropped_tun_packet: 0, dropped_dns_packet: 0, successfully_retried_tun_packet: 0 }
2025-08-05T01:53:12.563Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
NewNeighbour; Destination: **********; 
2025-08-05T01:53:24.415Z DEBUG slog: got a response for stream_id=12168, headers=[":status: 200", "cf-team: 29198807be00000482d755a400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T01:53:39.414Z DEBUG slog: got a response for stream_id=12172, headers=[":status: 200", "cf-team: 291988425600000482d756d400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T01:53:54.414Z DEBUG slog: got a response for stream_id=12176, headers=[":status: 200", "cf-team: 2919887ced00000482d758b400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T01:53:58.387Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
2025-08-05T01:54:04.531Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
NewNeighbour; Destination: **********; 
2025-08-05T01:54:09.414Z DEBUG slog: got a response for stream_id=12180, headers=[":status: 200", "cf-team: 291988b78500000482d75e5400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T01:54:24.414Z DEBUG slog: got a response for stream_id=12184, headers=[":status: 200", "cf-team: 291988f21e00000482d7609400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T01:54:39.414Z DEBUG slog: got a response for stream_id=12188, headers=[":status: 200", "cf-team: 2919892cb500000482d7625400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T01:54:49.587Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
2025-08-05T01:54:54.414Z DEBUG slog: got a response for stream_id=12192, headers=[":status: 200", "cf-team: 291989674e00000482d762f400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T01:54:56.755Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
NewNeighbour; Destination: **********; 
2025-08-05T01:55:02.736Z DEBUG upload_stats{stats=StatsPayload { counters: [CounterPayload { name: "main-loop-compat.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "message_bus.bus_driver_commands_sent", labels: {}, count: 4 }, CounterPayload { name: "alternate-network.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "main-loop-compat.commands_recv", labels: {}, count: 2 }, CounterPayload { name: "watchdog.requests_recv", labels: {}, count: 457149 }, CounterPayload { name: "registration-verifier.commands_recv", labels: {}, count: 1 }, CounterPayload { name: "device-state.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "message_bus.bus_driver_updates_sent", labels: {}, count: 1 }, CounterPayload { name: "dns_proxy.doh_err_other", labels: {}, count: 3 }, CounterPayload { name: "statistics.commands_recv", labels: {}, count: 1 }], summaries: [SummaryPayload { name: "firewall.fw_avg_ms", labels: {}, sum: 274.0, count: 14 }, SummaryPayload { name: "dns_proxy.doh_avg_ms", labels: {}, sum: 7892.0, count: 7568 }, SummaryPayload { name: "happy_eyeballs.conn_attempts_all", labels: {}, sum: 1.0, count: 1 }, SummaryPayload { name: "message_bus.bus_driver_request_duration_sec_nano_sec", labels: {}, sum: 18.314294866999955, count: 457149 }], gauges: [GaugesPayload { name: "ntp-clock.min_since_ntp_sync", labels: {}, value: 59.993164769 }, GaugesPayload { name: "happy_eyeballs.conn_attempts_current", labels: {}, value: 1.0 }] } context="timer"}: actor_statistics::handler: Starting to upload stats
2025-08-05T01:55:02.736Z  WARN upload_stats{stats=StatsPayload { counters: [CounterPayload { name: "main-loop-compat.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "message_bus.bus_driver_commands_sent", labels: {}, count: 4 }, CounterPayload { name: "alternate-network.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "main-loop-compat.commands_recv", labels: {}, count: 2 }, CounterPayload { name: "watchdog.requests_recv", labels: {}, count: 457149 }, CounterPayload { name: "registration-verifier.commands_recv", labels: {}, count: 1 }, CounterPayload { name: "device-state.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "message_bus.bus_driver_updates_sent", labels: {}, count: 1 }, CounterPayload { name: "dns_proxy.doh_err_other", labels: {}, count: 3 }, CounterPayload { name: "statistics.commands_recv", labels: {}, count: 1 }], summaries: [SummaryPayload { name: "firewall.fw_avg_ms", labels: {}, sum: 274.0, count: 14 }, SummaryPayload { name: "dns_proxy.doh_avg_ms", labels: {}, sum: 7892.0, count: 7568 }, SummaryPayload { name: "happy_eyeballs.conn_attempts_all", labels: {}, sum: 1.0, count: 1 }, SummaryPayload { name: "message_bus.bus_driver_request_duration_sec_nano_sec", labels: {}, sum: 18.314294866999955, count: 457149 }], gauges: [GaugesPayload { name: "ntp-clock.min_since_ntp_sync", labels: {}, value: 59.993164769 }, GaugesPayload { name: "happy_eyeballs.conn_attempts_current", labels: {}, value: 1.0 }] } context="timer"}: actor_statistics::handler: Skipped uploading aggregate stats because registration was none!
2025-08-05T01:55:09.414Z DEBUG slog: got a response for stream_id=12196, headers=[":status: 200", "cf-team: 291989a1e600000482d7642400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T01:55:09.486Z DEBUG tunnel_loop{protocol="masque" con_id="6d3539c6af1e789ae39aef05871da34927b6218a"}: warp_edge::tunnel_loop: Tunnel loop statistics stats=TunnelLoopStats { tun_packet_too_large: 0, edge_packet_too_large: 0, tun_packet_too_small: 0, edge_packet_too_small: 0, tun_packet_martian: 0, tun_packet_unhandled: 21, dropped_edge_packet: 0, dropped_tun_packet: 0, dropped_dns_packet: 0, successfully_retried_tun_packet: 0 }
2025-08-05T01:55:24.414Z DEBUG slog: got a response for stream_id=12200, headers=[":status: 200", "cf-team: 291989dc7e00000482d7694400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T01:55:39.414Z DEBUG slog: got a response for stream_id=12204, headers=[":status: 200", "cf-team: 29198a171500000482d76a5400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-08-05T01:55:42.835Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
2025-08-05T01:55:48.723Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
NewNeighbour; Destination: **********; 
2025-08-05T01:55:54.414Z DEBUG slog: got a response for stream_id=12208, headers=[":status: 200", "cf-team: 29198a51ad00000482d76be400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
