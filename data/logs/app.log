time="2025-07-31 09:42:33" level=info msg="Redis DSN not configured, falling back to in-memory store."
time="2025-07-31 09:42:33" level=info msg="Starting as Master Node."
time="2025-07-31 09:42:33" level=info msg="Database auto-migration completed."
time="2025-07-31 09:42:33" level=info msg="Initialized system setting: app_url = http://localhost:3001"
time="2025-07-31 09:42:33" level=info msg="Initialized system setting: request_log_retention_days = 7"
time="2025-07-31 09:42:33" level=info msg="Initialized system setting: request_log_write_interval_minutes = 1"
time="2025-07-31 09:42:33" level=info msg="Initialized system setting: proxy_keys = sk-123456"
time="2025-07-31 09:42:33" level=info msg="Initialized system setting: request_timeout = 600"
time="2025-07-31 09:42:33" level=info msg="Initialized system setting: connect_timeout = 15"
time="2025-07-31 09:42:33" level=info msg="Initialized system setting: idle_conn_timeout = 120"
time="2025-07-31 09:42:33" level=info msg="Initialized system setting: response_header_timeout = 600"
time="2025-07-31 09:42:33" level=info msg="Initialized system setting: max_idle_conns = 100"
time="2025-07-31 09:42:33" level=info msg="Initialized system setting: max_idle_conns_per_host = 50"
time="2025-07-31 09:42:33" level=info msg="Initialized system setting: max_retries = 3"
time="2025-07-31 09:42:33" level=info msg="Initialized system setting: blacklist_threshold = 3"
time="2025-07-31 09:42:33" level=info msg="Initialized system setting: key_validation_interval_minutes = 60"
time="2025-07-31 09:42:33" level=info msg="Initialized system setting: key_validation_concurrency = 10"
time="2025-07-31 09:42:33" level=info msg="Initialized system setting: key_validation_timeout_seconds = 20"
time="2025-07-31 09:42:33" level=info msg="System settings initialized in DB."
time="2025-07-31 09:42:33" level=info
time="2025-07-31 09:42:33" level=info msg="========= System Settings ========="
time="2025-07-31 09:42:33" level=info msg="  --- Basic Settings ---"
time="2025-07-31 09:42:33" level=info msg="    App URL: http://localhost:3001"
time="2025-07-31 09:42:33" level=info msg="    Request Log Retention: 7 days"
time="2025-07-31 09:42:33" level=info msg="    Request Log Write Interval: 1 minutes"
time="2025-07-31 09:42:33" level=info msg="  --- Request Behavior ---"
time="2025-07-31 09:42:33" level=info msg="    Request Timeout: 600 seconds"
time="2025-07-31 09:42:33" level=info msg="    Connect Timeout: 15 seconds"
time="2025-07-31 09:42:33" level=info msg="    Response Header Timeout: 600 seconds"
time="2025-07-31 09:42:33" level=info msg="    Idle Connection Timeout: 120 seconds"
time="2025-07-31 09:42:33" level=info msg="    Max Idle Connections: 100"
time="2025-07-31 09:42:33" level=info msg="    Max Idle Connections Per Host: 50"
time="2025-07-31 09:42:33" level=info msg="  --- Key & Group Behavior ---"
time="2025-07-31 09:42:33" level=info msg="    Max Retries: 3"
time="2025-07-31 09:42:33" level=info msg="    Blacklist Threshold: 3"
time="2025-07-31 09:42:33" level=info msg="    Key Validation Interval: 60 minutes"
time="2025-07-31 09:42:33" level=info msg="===================================="
time="2025-07-31 09:42:33" level=info
time="2025-07-31 09:42:33" level=info msg="cache reloaded successfully" syncer=system_settings
time="2025-07-31 09:42:33" level=info msg="Updating active key lists for all groups..."
time="2025-07-31 09:42:33" level=info
time="2025-07-31 09:42:33" level=info msg="======= Server Configuration ======="
time="2025-07-31 09:42:33" level=info msg="  --- Server ---"
time="2025-07-31 09:42:33" level=info msg="    Listen Address: 0.0.0.0:3001"
time="2025-07-31 09:42:33" level=info msg="    Graceful Shutdown Timeout: 10 seconds"
time="2025-07-31 09:42:33" level=info msg="    Read Timeout: 60 seconds"
time="2025-07-31 09:42:33" level=info msg="    Write Timeout: 600 seconds"
time="2025-07-31 09:42:33" level=info msg="    Idle Timeout: 120 seconds"
time="2025-07-31 09:42:33" level=info msg="  --- Performance ---"
time="2025-07-31 09:42:33" level=info msg="    Max Concurrent Requests: 100"
time="2025-07-31 09:42:33" level=info msg="  --- Security ---"
time="2025-07-31 09:42:33" level=info msg="    Authentication: enabled (key loaded)"
time="2025-07-31 09:42:33" level=info msg="    CORS: enabled (Origins: *)"
time="2025-07-31 09:42:33" level=info msg="  --- Logging ---"
time="2025-07-31 09:42:33" level=info msg="    Log Level: info"
time="2025-07-31 09:42:33" level=info msg="    Log Format: text"
time="2025-07-31 09:42:33" level=info msg="    File Logging: true"
time="2025-07-31 09:42:33" level=info msg="    Log File Path: ./data/logs/app.log"
time="2025-07-31 09:42:33" level=info msg="  --- Dependencies ---"
time="2025-07-31 09:42:33" level=info msg="    Database: configured"
time="2025-07-31 09:42:33" level=info msg="    Redis: not configured"
time="2025-07-31 09:42:33" level=info msg="===================================="
time="2025-07-31 09:42:33" level=info
time="2025-07-31 09:42:33" level=info msg="cache reloaded successfully" syncer=groups
time="2025-07-31 09:42:33" level=info msg="GPT-Load proxy server started successfully on Version: v1.0.17"
time="2025-07-31 09:42:33" level=info msg="Server address: http://0.0.0.0:3001"
time="2025-07-31 09:42:33" level=info
time="2025-07-31 09:44:23" level=info msg="Shutting down server..."
time="2025-07-31 09:44:23" level=info msg="HTTP server has been shut down."
time="2025-07-31 09:44:23" level=info msg="cache syncer stopped." syncer=groups
time="2025-07-31 09:44:23" level=info msg="RequestLogService stopped gracefully."
time="2025-07-31 09:44:23" level=info msg="LogCleanupService stopped gracefully."
time="2025-07-31 09:44:23" level=info msg="CronChecker stopped gracefully."
time="2025-07-31 09:44:23" level=info msg="cache syncer stopped." syncer=system_settings
time="2025-07-31 09:44:23" level=info msg="All background services stopped."
time="2025-07-31 09:44:23" level=info msg="Server exited gracefully"
time="2025-07-31 09:44:24" level=info msg="Redis DSN not configured, falling back to in-memory store."
time="2025-07-31 09:44:24" level=info msg="Starting as Master Node."
time="2025-07-31 09:44:24" level=info msg="Database auto-migration completed."
time="2025-07-31 09:44:24" level=info msg="System settings initialized in DB."
time="2025-07-31 09:44:24" level=info
time="2025-07-31 09:44:24" level=info msg="========= System Settings ========="
time="2025-07-31 09:44:24" level=info msg="  --- Basic Settings ---"
time="2025-07-31 09:44:24" level=info msg="    App URL: http://localhost:3001"
time="2025-07-31 09:44:24" level=info msg="    Request Log Retention: 7 days"
time="2025-07-31 09:44:24" level=info msg="    Request Log Write Interval: 1 minutes"
time="2025-07-31 09:44:24" level=info msg="  --- Request Behavior ---"
time="2025-07-31 09:44:24" level=info msg="    Request Timeout: 600 seconds"
time="2025-07-31 09:44:24" level=info msg="    Connect Timeout: 15 seconds"
time="2025-07-31 09:44:24" level=info msg="    Response Header Timeout: 600 seconds"
time="2025-07-31 09:44:24" level=info msg="    Idle Connection Timeout: 120 seconds"
time="2025-07-31 09:44:24" level=info msg="    Max Idle Connections: 100"
time="2025-07-31 09:44:24" level=info msg="    Max Idle Connections Per Host: 50"
time="2025-07-31 09:44:24" level=info msg="  --- Key & Group Behavior ---"
time="2025-07-31 09:44:24" level=info msg="    Max Retries: 3"
time="2025-07-31 09:44:24" level=info msg="    Blacklist Threshold: 3"
time="2025-07-31 09:44:24" level=info msg="    Key Validation Interval: 60 minutes"
time="2025-07-31 09:44:24" level=info msg="===================================="
time="2025-07-31 09:44:24" level=info
time="2025-07-31 09:44:24" level=info msg="cache reloaded successfully" syncer=system_settings
time="2025-07-31 09:44:24" level=info msg="Updating active key lists for all groups..."
time="2025-07-31 09:44:24" level=info
time="2025-07-31 09:44:24" level=info msg="======= Server Configuration ======="
time="2025-07-31 09:44:24" level=info msg="  --- Server ---"
time="2025-07-31 09:44:24" level=info msg="    Listen Address: 0.0.0.0:3001"
time="2025-07-31 09:44:24" level=info msg="    Graceful Shutdown Timeout: 10 seconds"
time="2025-07-31 09:44:24" level=info msg="    Read Timeout: 60 seconds"
time="2025-07-31 09:44:24" level=info msg="    Write Timeout: 600 seconds"
time="2025-07-31 09:44:24" level=info msg="    Idle Timeout: 120 seconds"
time="2025-07-31 09:44:24" level=info msg="  --- Performance ---"
time="2025-07-31 09:44:24" level=info msg="    Max Concurrent Requests: 100"
time="2025-07-31 09:44:24" level=info msg="  --- Security ---"
time="2025-07-31 09:44:24" level=info msg="    Authentication: enabled (key loaded)"
time="2025-07-31 09:44:24" level=info msg="    CORS: enabled (Origins: *)"
time="2025-07-31 09:44:24" level=info msg="  --- Logging ---"
time="2025-07-31 09:44:24" level=info msg="    Log Level: info"
time="2025-07-31 09:44:24" level=info msg="    Log Format: text"
time="2025-07-31 09:44:24" level=info msg="    File Logging: true"
time="2025-07-31 09:44:24" level=info msg="    Log File Path: ./data/logs/app.log"
time="2025-07-31 09:44:24" level=info msg="  --- Dependencies ---"
time="2025-07-31 09:44:24" level=info msg="    Database: configured"
time="2025-07-31 09:44:24" level=info msg="    Redis: not configured"
time="2025-07-31 09:44:24" level=info msg="===================================="
time="2025-07-31 09:44:24" level=info
time="2025-07-31 09:44:24" level=info msg="cache reloaded successfully" syncer=groups
time="2025-07-31 09:44:24" level=info msg="GPT-Load proxy server started successfully on Version: v1.0.17"
time="2025-07-31 09:44:24" level=info msg="Server address: http://0.0.0.0:3001"
time="2025-07-31 09:44:24" level=info
time="2025-07-31 09:44:49" level=info msg="GET /wordpress/ - 200 - 306.733µs"
time="2025-07-31 09:45:18" level=info msg="GET / - 200 - 502.305µs"
time="2025-07-31 09:45:19" level=info msg="GET /assets/index-Gswog8ov.css - 200 - 3.017732ms"
time="2025-07-31 09:45:19" level=info msg="GET /assets/index-SPLiOoPZ.js - 200 - 69.48716ms"
time="2025-07-31 09:45:21" level=info msg="GET /assets/Login-DzpmRpZS.css - 200 - 388.849µs"
time="2025-07-31 09:45:21" level=info msg="GET /assets/Login-C7BGdm6I.js - 200 - 475.053µs"
time="2025-07-31 09:45:21" level=info msg="GET /assets/logo-BV-_qOqO.png - 200 - 1.47681ms"
time="2025-07-31 09:45:23" level=warning msg="POST /api/auth/login - 401 - 428.515µs"
time="2025-07-31 09:45:56" level=info msg="Shutting down server..."
time="2025-07-31 09:45:56" level=info msg="HTTP server has been shut down."
time="2025-07-31 09:45:56" level=info msg="cache syncer stopped." syncer=groups
time="2025-07-31 09:45:56" level=info msg="cache syncer stopped." syncer=system_settings
time="2025-07-31 09:45:56" level=info msg="RequestLogService stopped gracefully."
time="2025-07-31 09:45:56" level=info msg="LogCleanupService stopped gracefully."
time="2025-07-31 09:45:56" level=info msg="CronChecker stopped gracefully."
time="2025-07-31 09:45:56" level=info msg="All background services stopped."
time="2025-07-31 09:45:56" level=info msg="Server exited gracefully"
time="2025-07-31 09:45:57" level=info msg="Redis DSN not configured, falling back to in-memory store."
time="2025-07-31 09:45:57" level=info msg="Starting as Master Node."
time="2025-07-31 09:45:57" level=info msg="Database auto-migration completed."
time="2025-07-31 09:45:57" level=info msg="System settings initialized in DB."
time="2025-07-31 09:45:57" level=info
time="2025-07-31 09:45:57" level=info msg="========= System Settings ========="
time="2025-07-31 09:45:57" level=info msg="  --- Basic Settings ---"
time="2025-07-31 09:45:57" level=info msg="    App URL: http://localhost:3001"
time="2025-07-31 09:45:57" level=info msg="    Request Log Retention: 7 days"
time="2025-07-31 09:45:57" level=info msg="    Request Log Write Interval: 1 minutes"
time="2025-07-31 09:45:57" level=info msg="  --- Request Behavior ---"
time="2025-07-31 09:45:57" level=info msg="    Request Timeout: 600 seconds"
time="2025-07-31 09:45:57" level=info msg="    Connect Timeout: 15 seconds"
time="2025-07-31 09:45:57" level=info msg="    Response Header Timeout: 600 seconds"
time="2025-07-31 09:45:57" level=info msg="    Idle Connection Timeout: 120 seconds"
time="2025-07-31 09:45:57" level=info msg="    Max Idle Connections: 100"
time="2025-07-31 09:45:57" level=info msg="    Max Idle Connections Per Host: 50"
time="2025-07-31 09:45:57" level=info msg="  --- Key & Group Behavior ---"
time="2025-07-31 09:45:57" level=info msg="    Max Retries: 3"
time="2025-07-31 09:45:57" level=info msg="    Blacklist Threshold: 3"
time="2025-07-31 09:45:57" level=info msg="    Key Validation Interval: 60 minutes"
time="2025-07-31 09:45:57" level=info msg="===================================="
time="2025-07-31 09:45:57" level=info
time="2025-07-31 09:45:57" level=info msg="cache reloaded successfully" syncer=system_settings
time="2025-07-31 09:45:57" level=info msg="Updating active key lists for all groups..."
time="2025-07-31 09:45:57" level=info
time="2025-07-31 09:45:57" level=info msg="======= Server Configuration ======="
time="2025-07-31 09:45:57" level=info msg="  --- Server ---"
time="2025-07-31 09:45:57" level=info msg="    Listen Address: 0.0.0.0:3001"
time="2025-07-31 09:45:57" level=info msg="    Graceful Shutdown Timeout: 10 seconds"
time="2025-07-31 09:45:57" level=info msg="    Read Timeout: 60 seconds"
time="2025-07-31 09:45:57" level=info msg="    Write Timeout: 600 seconds"
time="2025-07-31 09:45:57" level=info msg="    Idle Timeout: 120 seconds"
time="2025-07-31 09:45:57" level=info msg="  --- Performance ---"
time="2025-07-31 09:45:57" level=info msg="    Max Concurrent Requests: 100"
time="2025-07-31 09:45:57" level=info msg="  --- Security ---"
time="2025-07-31 09:45:57" level=info msg="    Authentication: enabled (key loaded)"
time="2025-07-31 09:45:57" level=info msg="    CORS: enabled (Origins: *)"
time="2025-07-31 09:45:57" level=info msg="  --- Logging ---"
time="2025-07-31 09:45:57" level=info msg="    Log Level: info"
time="2025-07-31 09:45:57" level=info msg="    Log Format: text"
time="2025-07-31 09:45:57" level=info msg="    File Logging: true"
time="2025-07-31 09:45:57" level=info msg="    Log File Path: ./data/logs/app.log"
time="2025-07-31 09:45:57" level=info msg="  --- Dependencies ---"
time="2025-07-31 09:45:57" level=info msg="    Database: configured"
time="2025-07-31 09:45:57" level=info msg="    Redis: not configured"
time="2025-07-31 09:45:57" level=info msg="===================================="
time="2025-07-31 09:45:57" level=info
time="2025-07-31 09:45:57" level=info msg="cache reloaded successfully" syncer=groups
time="2025-07-31 09:45:57" level=info msg="GPT-Load proxy server started successfully on Version: v1.0.17"
time="2025-07-31 09:45:57" level=info msg="Server address: http://0.0.0.0:3001"
time="2025-07-31 09:45:57" level=info
time="2025-07-31 09:46:01" level=info msg="GET /login - 200 - 546.169µs"
time="2025-07-31 09:46:04" level=warning msg="POST /api/auth/login - 401 - 210.16µs"
time="2025-07-31 09:46:16" level=warning msg="POST /api/auth/login - 401 - 98.797µs"
time="2025-07-31 09:47:17" level=info msg="GET / - 200 - 374.623µs"
time="2025-07-31 09:47:17" level=info msg="GET /assets/index-Gswog8ov.css - 200 - 2.169628ms"
time="2025-07-31 09:47:18" level=info msg="GET /assets/index-SPLiOoPZ.js - 200 - 62.411015ms"
time="2025-07-31 09:47:18" level=info msg="GET /assets/Login-DzpmRpZS.css - 200 - 233.544µs"
time="2025-07-31 09:47:18" level=info msg="GET /assets/logo-BV-_qOqO.png - 200 - 902.878µs"
time="2025-07-31 09:47:18" level=info msg="GET /assets/Login-C7BGdm6I.js - 200 - 388.83µs"
time="2025-07-31 09:47:19" level=info msg="GET /assets/logo-BV-_qOqO.png - 200 - 1.342604ms"
time="2025-07-31 09:47:20" level=info msg="Shutting down server..."
time="2025-07-31 09:47:20" level=info msg="HTTP server has been shut down."
time="2025-07-31 09:47:20" level=info msg="cache syncer stopped." syncer=groups
time="2025-07-31 09:47:20" level=info msg="cache syncer stopped." syncer=system_settings
time="2025-07-31 09:47:20" level=info msg="CronChecker stopped gracefully."
time="2025-07-31 09:47:20" level=info msg="LogCleanupService stopped gracefully."
time="2025-07-31 09:47:20" level=info msg="RequestLogService stopped gracefully."
time="2025-07-31 09:47:20" level=info msg="All background services stopped."
time="2025-07-31 09:47:20" level=info msg="Server exited gracefully"
time="2025-07-31 09:47:20" level=info msg="Redis DSN not configured, falling back to in-memory store."
time="2025-07-31 09:47:20" level=info msg="Starting as Master Node."
time="2025-07-31 09:47:20" level=info msg="Database auto-migration completed."
time="2025-07-31 09:47:20" level=info msg="System settings initialized in DB."
time="2025-07-31 09:47:20" level=info
time="2025-07-31 09:47:20" level=info msg="========= System Settings ========="
time="2025-07-31 09:47:20" level=info msg="  --- Basic Settings ---"
time="2025-07-31 09:47:20" level=info msg="    App URL: http://localhost:3001"
time="2025-07-31 09:47:20" level=info msg="    Request Log Retention: 7 days"
time="2025-07-31 09:47:20" level=info msg="    Request Log Write Interval: 1 minutes"
time="2025-07-31 09:47:20" level=info msg="  --- Request Behavior ---"
time="2025-07-31 09:47:20" level=info msg="    Request Timeout: 600 seconds"
time="2025-07-31 09:47:20" level=info msg="    Connect Timeout: 15 seconds"
time="2025-07-31 09:47:20" level=info msg="    Response Header Timeout: 600 seconds"
time="2025-07-31 09:47:20" level=info msg="    Idle Connection Timeout: 120 seconds"
time="2025-07-31 09:47:20" level=info msg="    Max Idle Connections: 100"
time="2025-07-31 09:47:20" level=info msg="    Max Idle Connections Per Host: 50"
time="2025-07-31 09:47:20" level=info msg="  --- Key & Group Behavior ---"
time="2025-07-31 09:47:20" level=info msg="    Max Retries: 3"
time="2025-07-31 09:47:20" level=info msg="    Blacklist Threshold: 3"
time="2025-07-31 09:47:20" level=info msg="    Key Validation Interval: 60 minutes"
time="2025-07-31 09:47:20" level=info msg="===================================="
time="2025-07-31 09:47:20" level=info
time="2025-07-31 09:47:20" level=info msg="cache reloaded successfully" syncer=system_settings
time="2025-07-31 09:47:20" level=info msg="Updating active key lists for all groups..."
time="2025-07-31 09:47:20" level=info
time="2025-07-31 09:47:20" level=info msg="======= Server Configuration ======="
time="2025-07-31 09:47:20" level=info msg="  --- Server ---"
time="2025-07-31 09:47:20" level=info msg="    Listen Address: 0.0.0.0:3001"
time="2025-07-31 09:47:20" level=info msg="    Graceful Shutdown Timeout: 10 seconds"
time="2025-07-31 09:47:20" level=info msg="    Read Timeout: 60 seconds"
time="2025-07-31 09:47:20" level=info msg="    Write Timeout: 600 seconds"
time="2025-07-31 09:47:20" level=info msg="    Idle Timeout: 120 seconds"
time="2025-07-31 09:47:20" level=info msg="  --- Performance ---"
time="2025-07-31 09:47:20" level=info msg="    Max Concurrent Requests: 100"
time="2025-07-31 09:47:20" level=info msg="  --- Security ---"
time="2025-07-31 09:47:20" level=info msg="    Authentication: enabled (key loaded)"
time="2025-07-31 09:47:20" level=info msg="    CORS: enabled (Origins: *)"
time="2025-07-31 09:47:20" level=info msg="  --- Logging ---"
time="2025-07-31 09:47:20" level=info msg="    Log Level: info"
time="2025-07-31 09:47:20" level=info msg="    Log Format: text"
time="2025-07-31 09:47:20" level=info msg="    File Logging: true"
time="2025-07-31 09:47:20" level=info msg="    Log File Path: ./data/logs/app.log"
time="2025-07-31 09:47:20" level=info msg="  --- Dependencies ---"
time="2025-07-31 09:47:20" level=info msg="    Database: configured"
time="2025-07-31 09:47:20" level=info msg="    Redis: not configured"
time="2025-07-31 09:47:20" level=info msg="===================================="
time="2025-07-31 09:47:20" level=info
time="2025-07-31 09:47:20" level=info msg="cache reloaded successfully" syncer=groups
time="2025-07-31 09:47:20" level=info msg="GPT-Load proxy server started successfully on Version: v1.0.17"
time="2025-07-31 09:47:20" level=info msg="Server address: http://0.0.0.0:3001"
time="2025-07-31 09:47:20" level=info
time="2025-07-31 09:47:22" level=info msg="GET / - 200 - 635.39µs"
time="2025-07-31 09:47:23" level=info msg="GET /assets/index-Gswog8ov.css - 200 - 1.146722ms"
time="2025-07-31 09:47:23" level=info msg="GET /login - 200 - 852.863µs"
time="2025-07-31 09:47:23" level=info msg="GET /assets/index-SPLiOoPZ.js - 200 - 67.13886ms"
time="2025-07-31 09:47:24" level=info msg="GET /assets/Login-DzpmRpZS.css - 200 - 246.268µs"
time="2025-07-31 09:47:24" level=info msg="GET /assets/Login-C7BGdm6I.js - 200 - 261.619µs"
time="2025-07-31 09:47:24" level=info msg="GET /assets/logo-BV-_qOqO.png - 200 - 853.634µs"
time="2025-07-31 09:47:25" level=info msg="GET /assets/logo-BV-_qOqO.png - 200 - 1.053905ms"
time="2025-07-31 09:47:25" level=warning msg="POST /api/auth/login - 401 - 439.316µs"
time="2025-07-31 09:47:40" level=info msg="Shutting down server..."
time="2025-07-31 09:47:40" level=info msg="HTTP server has been shut down."
time="2025-07-31 09:47:40" level=info msg="LogCleanupService stopped gracefully."
time="2025-07-31 09:47:40" level=info msg="cache syncer stopped." syncer=system_settings
time="2025-07-31 09:47:40" level=info msg="RequestLogService stopped gracefully."
time="2025-07-31 09:47:40" level=info msg="CronChecker stopped gracefully."
time="2025-07-31 09:47:40" level=info msg="cache syncer stopped." syncer=groups
time="2025-07-31 09:47:40" level=info msg="All background services stopped."
time="2025-07-31 09:47:40" level=info msg="Server exited gracefully"
time="2025-07-31 09:47:41" level=info msg="Redis DSN not configured, falling back to in-memory store."
time="2025-07-31 09:47:41" level=info msg="Starting as Master Node."
time="2025-07-31 09:47:41" level=info msg="Database auto-migration completed."
time="2025-07-31 09:47:41" level=info msg="System settings initialized in DB."
time="2025-07-31 09:47:41" level=info
time="2025-07-31 09:47:41" level=info msg="========= System Settings ========="
time="2025-07-31 09:47:41" level=info msg="  --- Basic Settings ---"
time="2025-07-31 09:47:41" level=info msg="    App URL: http://localhost:3001"
time="2025-07-31 09:47:41" level=info msg="    Request Log Retention: 7 days"
time="2025-07-31 09:47:41" level=info msg="    Request Log Write Interval: 1 minutes"
time="2025-07-31 09:47:41" level=info msg="  --- Request Behavior ---"
time="2025-07-31 09:47:41" level=info msg="    Request Timeout: 600 seconds"
time="2025-07-31 09:47:41" level=info msg="    Connect Timeout: 15 seconds"
time="2025-07-31 09:47:41" level=info msg="    Response Header Timeout: 600 seconds"
time="2025-07-31 09:47:41" level=info msg="    Idle Connection Timeout: 120 seconds"
time="2025-07-31 09:47:41" level=info msg="    Max Idle Connections: 100"
time="2025-07-31 09:47:41" level=info msg="    Max Idle Connections Per Host: 50"
time="2025-07-31 09:47:41" level=info msg="  --- Key & Group Behavior ---"
time="2025-07-31 09:47:41" level=info msg="    Max Retries: 3"
time="2025-07-31 09:47:41" level=info msg="    Blacklist Threshold: 3"
time="2025-07-31 09:47:41" level=info msg="    Key Validation Interval: 60 minutes"
time="2025-07-31 09:47:41" level=info msg="===================================="
time="2025-07-31 09:47:41" level=info
time="2025-07-31 09:47:41" level=info msg="cache reloaded successfully" syncer=system_settings
time="2025-07-31 09:47:41" level=info msg="Updating active key lists for all groups..."
time="2025-07-31 09:47:41" level=info
time="2025-07-31 09:47:41" level=info msg="======= Server Configuration ======="
time="2025-07-31 09:47:41" level=info msg="  --- Server ---"
time="2025-07-31 09:47:41" level=info msg="    Listen Address: 0.0.0.0:3001"
time="2025-07-31 09:47:41" level=info msg="    Graceful Shutdown Timeout: 10 seconds"
time="2025-07-31 09:47:41" level=info msg="    Read Timeout: 60 seconds"
time="2025-07-31 09:47:41" level=info msg="    Write Timeout: 600 seconds"
time="2025-07-31 09:47:41" level=info msg="    Idle Timeout: 120 seconds"
time="2025-07-31 09:47:41" level=info msg="  --- Performance ---"
time="2025-07-31 09:47:41" level=info msg="    Max Concurrent Requests: 100"
time="2025-07-31 09:47:41" level=info msg="  --- Security ---"
time="2025-07-31 09:47:41" level=info msg="    Authentication: enabled (key loaded)"
time="2025-07-31 09:47:41" level=info msg="    CORS: enabled (Origins: *)"
time="2025-07-31 09:47:41" level=info msg="  --- Logging ---"
time="2025-07-31 09:47:41" level=info msg="    Log Level: info"
time="2025-07-31 09:47:41" level=info msg="    Log Format: text"
time="2025-07-31 09:47:41" level=info msg="    File Logging: true"
time="2025-07-31 09:47:41" level=info msg="    Log File Path: ./data/logs/app.log"
time="2025-07-31 09:47:41" level=info msg="  --- Dependencies ---"
time="2025-07-31 09:47:41" level=info msg="    Database: configured"
time="2025-07-31 09:47:41" level=info msg="    Redis: not configured"
time="2025-07-31 09:47:41" level=info msg="===================================="
time="2025-07-31 09:47:41" level=info
time="2025-07-31 09:47:41" level=info msg="cache reloaded successfully" syncer=groups
time="2025-07-31 09:47:41" level=info msg="GPT-Load proxy server started successfully on Version: v1.0.17"
time="2025-07-31 09:47:41" level=info msg="Server address: http://0.0.0.0:3001"
time="2025-07-31 09:47:41" level=info
time="2025-07-31 09:48:56" level=info msg="Redis DSN not configured, falling back to in-memory store."
time="2025-07-31 09:48:56" level=info msg="Starting as Master Node."
time="2025-07-31 09:48:56" level=info msg="Database auto-migration completed."
time="2025-07-31 09:48:56" level=info msg="System settings initialized in DB."
time="2025-07-31 09:48:56" level=info
time="2025-07-31 09:48:56" level=info msg="========= System Settings ========="
time="2025-07-31 09:48:56" level=info msg="  --- Basic Settings ---"
time="2025-07-31 09:48:56" level=info msg="    App URL: http://localhost:3001"
time="2025-07-31 09:48:56" level=info msg="    Request Log Retention: 7 days"
time="2025-07-31 09:48:56" level=info msg="    Request Log Write Interval: 1 minutes"
time="2025-07-31 09:48:56" level=info msg="  --- Request Behavior ---"
time="2025-07-31 09:48:56" level=info msg="    Request Timeout: 600 seconds"
time="2025-07-31 09:48:56" level=info msg="    Connect Timeout: 15 seconds"
time="2025-07-31 09:48:56" level=info msg="    Response Header Timeout: 600 seconds"
time="2025-07-31 09:48:56" level=info msg="    Idle Connection Timeout: 120 seconds"
time="2025-07-31 09:48:56" level=info msg="    Max Idle Connections: 100"
time="2025-07-31 09:48:56" level=info msg="    Max Idle Connections Per Host: 50"
time="2025-07-31 09:48:56" level=info msg="  --- Key & Group Behavior ---"
time="2025-07-31 09:48:56" level=info msg="    Max Retries: 3"
time="2025-07-31 09:48:56" level=info msg="    Blacklist Threshold: 3"
time="2025-07-31 09:48:56" level=info msg="    Key Validation Interval: 60 minutes"
time="2025-07-31 09:48:56" level=info msg="===================================="
time="2025-07-31 09:48:56" level=info
time="2025-07-31 09:48:56" level=info msg="cache reloaded successfully" syncer=system_settings
time="2025-07-31 09:48:56" level=info msg="Updating active key lists for all groups..."
time="2025-07-31 09:48:56" level=info
time="2025-07-31 09:48:56" level=info msg="======= Server Configuration ======="
time="2025-07-31 09:48:56" level=info msg="  --- Server ---"
time="2025-07-31 09:48:56" level=info msg="    Listen Address: 0.0.0.0:3001"
time="2025-07-31 09:48:56" level=info msg="    Graceful Shutdown Timeout: 10 seconds"
time="2025-07-31 09:48:56" level=info msg="    Read Timeout: 60 seconds"
time="2025-07-31 09:48:56" level=info msg="    Write Timeout: 600 seconds"
time="2025-07-31 09:48:56" level=info msg="    Idle Timeout: 120 seconds"
time="2025-07-31 09:48:56" level=info msg="  --- Performance ---"
time="2025-07-31 09:48:56" level=info msg="    Max Concurrent Requests: 100"
time="2025-07-31 09:48:56" level=info msg="  --- Security ---"
time="2025-07-31 09:48:56" level=info msg="    Authentication: enabled (key loaded)"
time="2025-07-31 09:48:56" level=info msg="    CORS: enabled (Origins: *)"
time="2025-07-31 09:48:56" level=info msg="  --- Logging ---"
time="2025-07-31 09:48:56" level=info msg="    Log Level: info"
time="2025-07-31 09:48:56" level=info msg="    Log Format: text"
time="2025-07-31 09:48:56" level=info msg="    File Logging: true"
time="2025-07-31 09:48:56" level=info msg="    Log File Path: ./data/logs/app.log"
time="2025-07-31 09:48:56" level=info msg="  --- Dependencies ---"
time="2025-07-31 09:48:56" level=info msg="    Database: configured"
time="2025-07-31 09:48:56" level=info msg="    Redis: not configured"
time="2025-07-31 09:48:56" level=info msg="===================================="
time="2025-07-31 09:48:56" level=info
time="2025-07-31 09:48:56" level=info msg="cache reloaded successfully" syncer=groups
time="2025-07-31 09:48:56" level=info msg="GPT-Load proxy server started successfully on Version: v1.0.17"
time="2025-07-31 09:48:56" level=info msg="Server address: http://0.0.0.0:3001"
time="2025-07-31 09:48:56" level=info
time="2025-07-31 09:49:03" level=info msg="GET /login - 200 - 288.208µs"
time="2025-07-31 09:49:05" level=info msg="POST /api/auth/login - 200 - 276.847µs"
time="2025-07-31 09:49:06" level=info msg="GET /assets/display-DuIJKIJ5.js - 200 - 832.754µs"
time="2025-07-31 09:49:06" level=info msg="GET /assets/Dashboard-ZqIXRWdq.js - 200 - 890.845µs"
time="2025-07-31 09:49:06" level=info msg="GET /assets/Dashboard-DHOcOEH2.css - 200 - 461.578µs"
time="2025-07-31 09:49:06" level=info msg="GET /api/tasks/status - 200 - 74.101µs"
time="2025-07-31 09:49:06" level=info msg="GET /api/groups/list - 200 - 953.393µs"
time="2025-07-31 09:49:06" level=info msg="GET /api/dashboard/chart - 200 - 1.693673ms"
time="2025-07-31 09:49:06" level=info msg="GET /api/dashboard/stats - 200 - 2.400066ms"
time="2025-07-31 09:49:08" level=info msg="GET /assets/ProxyKeysInput-qloCXNPS.js - 200 - 425.058µs"
time="2025-07-31 09:49:08" level=info msg="GET /assets/ProxyKeysInput-CoLmi6p2.css - 200 - 299.961µs"
time="2025-07-31 09:49:08" level=info msg="GET /assets/Settings-bVufNCH9.js - 200 - 572.47µs"
time="2025-07-31 09:49:09" level=info msg="GET /api/settings - 200 - 830.641µs"
time="2025-07-31 09:50:12" level=info msg="GET /assets/Keys-CCps4UaT.css - 200 - 784.824µs"
time="2025-07-31 09:50:12" level=info msg="GET /assets/Search-oHPKo0PA.js - 200 - 304.128µs"
time="2025-07-31 09:50:12" level=info msg="GET /assets/Keys-Cvs7q-Qm.js - 200 - 2.176111ms"
time="2025-07-31 09:50:13" level=info msg="GET /api/groups/config-options - 200 - 275.384µs"
time="2025-07-31 09:50:13" level=info msg="GET /api/groups - 200 - 520.87µs"
time="2025-07-31 09:51:43" level=info msg="GET /api/channel-types - 200 - 101.933µs"
time="2025-07-31 09:51:43" level=info msg="GET /api/groups/config-options - 200 - 150.507µs"
time="2025-07-31 09:52:41" level=info msg="POST /api/groups - 200 - 5.306466ms"
time="2025-07-31 09:52:41" level=info msg="cache reloaded successfully" syncer=groups
time="2025-07-31 09:52:42" level=info msg="GET /api/groups - 200 - 750.428µs"
time="2025-07-31 09:52:42" level=info msg="GET /api/keys?group_id=1&page=1&page_size=12 - 200 - 1.573754ms"
time="2025-07-31 09:52:42" level=info msg="GET /api/groups/1/stats - 200 - 2.934843ms"
time="2025-07-31 09:53:13" level=info msg="POST /api/keys/add-async - 200 - 936.963µs"
time="2025-07-31 09:53:13" level=info msg="GET /api/tasks/status - 200 - 128.825µs"
time="2025-07-31 09:53:14" level=info msg="GET /api/keys?group_id=1&page=1&page_size=12 - 200 - 939.708µs"
time="2025-07-31 09:53:14" level=info msg="GET /api/groups/1/stats - 200 - 1.153005ms"
time="2025-07-31 09:53:18" level=info msg="POST /api/keys/test-multiple - 200 - 532.801465ms"
time="2025-07-31 09:53:18" level=info msg="GET /api/keys?group_id=1&page=1&page_size=12 - 200 - 1.398702ms"
time="2025-07-31 09:53:18" level=info msg="GET /api/groups/1/stats - 200 - 1.026302ms"
time="2025-07-31 09:53:56" level=info msg="CronChecker: Group 'kimi-k2' has no invalid keys to check."
time="2025-07-31 09:54:47" level=info msg="GET /api/groups/config-options - 200 - 1.206205ms"
time="2025-07-31 09:54:47" level=info msg="GET /api/channel-types - 200 - 76.736µs"
time="2025-07-31 09:55:09" level=info msg="PUT /api/groups/1 - 200 - 7.82547ms"
time="2025-07-31 09:55:09" level=info msg="cache reloaded successfully" syncer=groups
time="2025-07-31 09:55:10" level=info msg="GET /api/groups - 200 - 576.753µs"
time="2025-07-31 09:55:10" level=info msg="GET /api/groups/1/stats - 200 - 1.274206ms"
time="2025-07-31 09:55:10" level=info msg="GET /api/keys?group_id=1&page=1&page_size=12 - 200 - 1.068794ms"
time="2025-07-31 09:55:12" level=info msg="POST /api/keys/test-multiple - 200 - 132.334182ms"
time="2025-07-31 09:55:13" level=info msg="GET /api/keys?group_id=1&page=1&page_size=12 - 200 - 957.531µs"
time="2025-07-31 09:55:13" level=info msg="GET /api/groups/1/stats - 200 - 1.009008ms"
time="2025-07-31 09:56:01" level=info msg="PUT /api/groups/1 - 200 - 5.022538ms"
time="2025-07-31 09:56:01" level=info msg="cache reloaded successfully" syncer=groups
time="2025-07-31 09:56:01" level=info msg="GET /api/groups - 200 - 696.06µs"
time="2025-07-31 09:56:02" level=info msg="GET /api/groups/1/stats - 200 - 1.149807ms"
time="2025-07-31 09:56:02" level=info msg="GET /api/keys?group_id=1&page=1&page_size=12 - 200 - 912.042µs"
time="2025-07-31 09:56:03" level=info msg="POST /api/keys/test-multiple - 200 - 206.399107ms"
time="2025-07-31 09:56:03" level=warning msg="Key has reached blacklist threshold, disabling." keyID=1 threshold=3
time="2025-07-31 09:56:04" level=info msg="GET /api/keys?group_id=1&page=1&page_size=12 - 200 - 940.006µs"
time="2025-07-31 09:56:04" level=info msg="GET /api/groups/1/stats - 200 - 1.338218ms"
time="2025-07-31 09:56:07" level=info msg="POST /api/keys/restore-multiple - 200 - 4.078894ms"
time="2025-07-31 09:56:08" level=info msg="GET /api/keys?group_id=1&page=1&page_size=12 - 200 - 908.926µs"
time="2025-07-31 09:56:08" level=info msg="GET /api/groups/1/stats - 200 - 1.195384ms"
time="2025-07-31 09:56:10" level=info msg="POST /api/keys/test-multiple - 200 - 230.063686ms"
time="2025-07-31 09:56:11" level=info msg="GET /api/keys?group_id=1&page=1&page_size=12 - 200 - 916.211µs"
time="2025-07-31 09:56:11" level=info msg="GET /api/groups/1/stats - 200 - 957.829µs"
time="2025-07-31 09:57:00" level=info msg="GET / - 200 - 967.728µs"
time="2025-07-31 09:57:00" level=info msg="GET / - 200 - 608.281µs"
time="2025-07-31 09:57:01" level=info msg="PUT /api/groups/1 - 200 - 5.598724ms"
time="2025-07-31 09:57:01" level=info msg="cache reloaded successfully" syncer=groups
time="2025-07-31 09:57:02" level=info msg="GET /api/groups - 200 - 638.329µs"
time="2025-07-31 09:57:02" level=info msg="GET /api/keys?group_id=1&page=1&page_size=12 - 200 - 1.170045ms"
time="2025-07-31 09:57:02" level=info msg="GET /api/groups/1/stats - 200 - 1.713332ms"
time="2025-07-31 09:57:04" level=info msg="POST /api/keys/test-multiple - 200 - 249.302547ms"
time="2025-07-31 09:57:04" level=info msg="GET /api/keys?group_id=1&page=1&page_size=12 - 200 - 843.15µs"
time="2025-07-31 09:57:05" level=info msg="GET /api/groups/1/stats - 200 - 860.264µs"
time="2025-07-31 09:57:46" level=info msg="PUT /api/groups/1 - 200 - 4.243343ms"
time="2025-07-31 09:57:46" level=info msg="cache reloaded successfully" syncer=groups
time="2025-07-31 09:57:46" level=info msg="GET /api/groups - 200 - 707.803µs"
time="2025-07-31 09:57:47" level=info msg="GET /api/groups/1/stats - 200 - 995.391µs"
time="2025-07-31 09:57:47" level=info msg="GET /api/keys?group_id=1&page=1&page_size=12 - 200 - 1.129537ms"
time="2025-07-31 09:57:50" level=info msg="POST /api/keys/test-multiple - 200 - 2.001965546s"
time="2025-07-31 09:57:51" level=info msg="GET /api/keys?group_id=1&page=1&page_size=12 - 200 - 2.1678ms"
time="2025-07-31 09:57:51" level=info msg="GET /api/groups/1/stats - 200 - 1.354708ms"
time="2025-07-31 09:57:57" level=info msg="POST /api/keys/validate-group - 200 - 813.162µs"
time="2025-07-31 09:57:57" level=info msg="Starting manual validation for group kimi-k2"
time="2025-07-31 09:57:57" level=error msg="Failed to handle key failure" error="failed to update key stats in DB: database is locked (5) (SQLITE_BUSY)" keyID=3
time="2025-07-31 09:57:57" level=error msg="Failed to handle key failure" error="failed to update key stats in DB: database is locked (5) (SQLITE_BUSY)" keyID=7
time="2025-07-31 09:57:58" level=info msg="GET /api/tasks/status - 200 - 263.312µs"
time="2025-07-31 09:57:59" level=info msg="GET /api/tasks/status - 200 - 129.989µs"
time="2025-07-31 09:57:59" level=info msg="Manual validation finished for group kimi-k2: {TotalKeys:10 ValidKeys:5 InvalidKeys:5}"
time="2025-07-31 09:58:00" level=info msg="GET /api/tasks/status - 200 - 129.788µs"
time="2025-07-31 09:58:01" level=info msg="GET /api/keys?group_id=1&page=1&page_size=12 - 200 - 879.89µs"
time="2025-07-31 09:58:01" level=info msg="GET /api/groups/1/stats - 200 - 1.345459ms"
time="2025-07-31 09:58:54" level=info msg="POST /api/keys/validate-group - 200 - 847.176µs"
time="2025-07-31 09:58:54" level=info msg="Starting manual validation for group kimi-k2"
time="2025-07-31 09:58:54" level=error msg="Failed to handle key failure" error="failed to update key stats in DB: database is locked (5) (SQLITE_BUSY)" keyID=9
time="2025-07-31 09:58:54" level=info msg="GET /api/tasks/status - 200 - 93.739µs"
time="2025-07-31 09:58:55" level=info msg="Manual validation finished for group kimi-k2: {TotalKeys:10 ValidKeys:4 InvalidKeys:6}"
time="2025-07-31 09:58:56" level=info msg="GET /api/tasks/status - 200 - 199.8µs"
time="2025-07-31 09:58:56" level=info msg="GET /api/keys?group_id=1&page=1&page_size=12 - 200 - 973.498µs"
time="2025-07-31 09:58:56" level=info msg="GET /api/groups/1/stats - 200 - 1.134105ms"
time="2025-07-31 09:59:06" level=info msg="GET /api/settings - 200 - 269.635µs"
time="2025-07-31 10:00:03" level=info
time="2025-07-31 10:00:03" level=info msg="========= System Settings ========="
time="2025-07-31 10:00:03" level=info msg="  --- Basic Settings ---"
time="2025-07-31 10:00:03" level=info msg="    App URL: https://load.ainima.de"
time="2025-07-31 10:00:03" level=info msg="    Request Log Retention: 7 days"
time="2025-07-31 10:00:03" level=info msg="    Request Log Write Interval: 1 minutes"
time="2025-07-31 10:00:03" level=info msg="  --- Request Behavior ---"
time="2025-07-31 10:00:03" level=info msg="    Request Timeout: 600 seconds"
time="2025-07-31 10:00:03" level=info msg="    Connect Timeout: 15 seconds"
time="2025-07-31 10:00:03" level=info msg="    Response Header Timeout: 600 seconds"
time="2025-07-31 10:00:03" level=info msg="    Idle Connection Timeout: 120 seconds"
time="2025-07-31 10:00:03" level=info msg="    Max Idle Connections: 100"
time="2025-07-31 10:00:03" level=info msg="    Max Idle Connections Per Host: 50"
time="2025-07-31 10:00:03" level=info msg="  --- Key & Group Behavior ---"
time="2025-07-31 10:00:03" level=info msg="    Max Retries: 3"
time="2025-07-31 10:00:03" level=info msg="    Blacklist Threshold: 3"
time="2025-07-31 10:00:03" level=info msg="    Key Validation Interval: 60 minutes"
time="2025-07-31 10:00:03" level=info msg="===================================="
time="2025-07-31 10:00:03" level=info
time="2025-07-31 10:00:03" level=info msg="cache reloaded successfully" syncer=system_settings
time="2025-07-31 10:00:03" level=info msg="cache reloaded successfully" syncer=groups
time="2025-07-31 10:00:03" level=info msg="PUT /api/settings - 200 - 106.700047ms"
time="2025-07-31 10:00:03" level=info msg="GET /api/settings - 200 - 236.212µs"
time="2025-07-31 10:00:08" level=info msg="GET /api/dashboard/stats - 200 - 1.088797ms"
time="2025-07-31 10:00:08" level=info msg="GET /api/dashboard/chart - 200 - 354.447µs"
time="2025-07-31 10:00:08" level=info msg="GET /api/groups/list - 200 - 285.035µs"
time="2025-07-31 10:00:11" level=info msg="GET /api/groups/config-options - 200 - 296.304µs"
time="2025-07-31 10:00:11" level=info msg="GET /api/groups - 200 - 565.059µs"
time="2025-07-31 10:00:11" level=info msg="GET /api/groups/1/stats - 200 - 1.401424ms"
time="2025-07-31 10:00:11" level=info msg="GET /api/keys?group_id=1&page=1&page_size=12 - 200 - 558.084µs"
time="2025-07-31 10:00:15" level=info msg="POST /api/keys/test-multiple - 200 - 204.708687ms"
time="2025-07-31 10:00:15" level=info msg="GET /api/keys?group_id=1&page=1&page_size=12 - 200 - 1.009506ms"
time="2025-07-31 10:00:16" level=info msg="GET /api/groups/1/stats - 200 - 1.724671ms"
time="2025-07-31 10:00:19" level=info msg="POST /api/keys/test-multiple - 200 - 1.041100445s"
time="2025-07-31 10:00:19" level=info msg="GET /api/keys?group_id=1&page=1&page_size=12 - 200 - 910.658µs"
time="2025-07-31 10:00:20" level=info msg="GET /api/groups/1/stats - 200 - 2.02824ms"
time="2025-07-31 10:00:59" level=info msg="GET /api/channel-types - 200 - 51.067µs"
time="2025-07-31 10:00:59" level=info msg="GET /api/groups/config-options - 200 - 163.733µs"
time="2025-07-31 10:02:58" level=info msg="GET /api/settings - 200 - 221.873µs"
time="2025-07-31 10:03:02" level=info msg="GET /api/groups/config-options - 200 - 180.364µs"
time="2025-07-31 10:03:02" level=info msg="GET /api/groups - 200 - 1.006881ms"
time="2025-07-31 10:03:02" level=info msg="GET /api/keys?group_id=1&page=1&page_size=12 - 200 - 1.076173ms"
time="2025-07-31 10:03:02" level=info msg="GET /api/groups/1/stats - 200 - 1.116538ms"
time="2025-07-31 10:03:06" level=info msg="GET /api/channel-types - 200 - 120.209µs"
time="2025-07-31 10:03:06" level=info msg="GET /api/groups/config-options - 200 - 148.684µs"
time="2025-07-31 10:03:26" level=info msg="PUT /api/groups/1 - 200 - 4.928378ms"
time="2025-07-31 10:03:26" level=info msg="cache reloaded successfully" syncer=groups
time="2025-07-31 10:03:26" level=info msg="GET /api/groups - 200 - 569.516µs"
time="2025-07-31 10:03:27" level=info msg="GET /api/groups/1/stats - 200 - 1.266475ms"
time="2025-07-31 10:03:27" level=info msg="GET /api/keys?group_id=1&page=1&page_size=12 - 200 - 1.198806ms"
time="2025-07-31 10:03:34" level=info msg="POST /api/keys/restore-all-invalid - 200 - 886.461µs"
time="2025-07-31 10:03:34" level=info msg="GET /api/keys?group_id=1&page=1&page_size=12 - 200 - 834.982µs"
time="2025-07-31 10:03:34" level=info msg="GET /api/groups/1/stats - 200 - 944.781µs"
time="2025-07-31 10:03:36" level=info msg="POST /api/keys/validate-group - 200 - 743.007µs"
time="2025-07-31 10:03:36" level=info msg="Starting manual validation for group kimi-k2"
time="2025-07-31 10:03:36" level=info msg="GET /api/tasks/status - 200 - 181.107µs"
time="2025-07-31 10:03:37" level=error msg="Failed to handle key failure" error="failed to update key stats in DB: database is locked (5) (SQLITE_BUSY)" keyID=4
time="2025-07-31 10:03:37" level=warning msg="Key has reached blacklist threshold, disabling." keyID=8 threshold=3
time="2025-07-31 10:03:38" level=info msg="GET /api/tasks/status - 200 - 128.034µs"
time="2025-07-31 10:03:39" level=info msg="Manual validation finished for group kimi-k2: {TotalKeys:10 ValidKeys:3 InvalidKeys:7}"
time="2025-07-31 10:03:39" level=info msg="GET /api/tasks/status - 200 - 133.043µs"
time="2025-07-31 10:03:40" level=info msg="GET /api/groups/1/stats - 200 - 1.198855ms"
time="2025-07-31 10:03:40" level=info msg="GET /api/keys?group_id=1&page=1&page_size=12 - 200 - 907.521µs"
time="2025-07-31 10:03:48" level=info msg="POST /api/keys/validate-group - 200 - 1.309196ms"
time="2025-07-31 10:03:48" level=info msg="Starting manual validation for group kimi-k2"
time="2025-07-31 10:03:48" level=warning msg="Key has reached blacklist threshold, disabling." keyID=1 threshold=3
time="2025-07-31 10:03:48" level=warning msg="Key has reached blacklist threshold, disabling." keyID=6 threshold=3
time="2025-07-31 10:03:48" level=warning msg="Key has reached blacklist threshold, disabling." keyID=7 threshold=3
time="2025-07-31 10:03:48" level=info msg="GET /api/tasks/status - 200 - 131.161µs"
time="2025-07-31 10:03:48" level=info msg="Manual validation finished for group kimi-k2: {TotalKeys:10 ValidKeys:3 InvalidKeys:7}"
time="2025-07-31 10:03:49" level=info msg="GET /api/tasks/status - 200 - 137.543µs"
time="2025-07-31 10:03:50" level=info msg="GET /api/groups/1/stats - 200 - 1.24841ms"
time="2025-07-31 10:03:50" level=info msg="GET /api/keys?group_id=1&page=1&page_size=12 - 200 - 1.016609ms"
time="2025-07-31 10:03:53" level=info msg="POST /api/keys/validate-group - 200 - 1.034122ms"
time="2025-07-31 10:03:53" level=info msg="Starting manual validation for group kimi-k2"
time="2025-07-31 10:03:54" level=error msg="Failed to handle key failure" error="failed to update key stats in DB: database is locked (5) (SQLITE_BUSY)" keyID=2
time="2025-07-31 10:03:54" level=error msg="Failed to handle key failure" error="failed to update key stats in DB: database is locked (5) (SQLITE_BUSY)" keyID=4
time="2025-07-31 10:03:54" level=warning msg="Key has reached blacklist threshold, disabling." keyID=10 threshold=3
time="2025-07-31 10:03:54" level=error msg="Failed to handle key failure" error="failed to update key stats in DB: database is locked (5) (SQLITE_BUSY)" keyID=5
time="2025-07-31 10:03:54" level=info msg="GET /api/tasks/status - 200 - 188.379µs"
time="2025-07-31 10:03:54" level=info msg="Manual validation finished for group kimi-k2: {TotalKeys:10 ValidKeys:2 InvalidKeys:8}"
time="2025-07-31 10:03:55" level=info msg="GET /api/tasks/status - 200 - 212.336µs"
time="2025-07-31 10:03:56" level=info msg="GET /api/groups/1/stats - 200 - 1.370113ms"
time="2025-07-31 10:03:56" level=info msg="GET /api/keys?group_id=1&page=1&page_size=12 - 200 - 887.842µs"
time="2025-07-31 10:03:58" level=info msg="POST /api/keys/validate-group - 200 - 1.094787ms"
time="2025-07-31 10:03:58" level=info msg="Starting manual validation for group kimi-k2"
time="2025-07-31 10:03:58" level=error msg="Failed to handle key failure" error="failed to update key stats in DB: database is locked (5) (SQLITE_BUSY)" keyID=5
time="2025-07-31 10:03:58" level=error msg="Failed to handle key failure" error="failed to update key stats in DB: database is locked (5) (SQLITE_BUSY)" keyID=3
time="2025-07-31 10:03:58" level=error msg="Failed to handle key failure" error="failed to update key stats in DB: database is locked (5) (SQLITE_BUSY)" keyID=2
time="2025-07-31 10:03:59" level=info msg="GET /api/tasks/status - 200 - 108.998µs"
time="2025-07-31 10:03:59" level=info msg="Manual validation finished for group kimi-k2: {TotalKeys:10 ValidKeys:2 InvalidKeys:8}"
time="2025-07-31 10:04:00" level=info msg="GET /api/tasks/status - 200 - 153.152µs"
time="2025-07-31 10:04:00" level=info msg="GET /api/groups/1/stats - 200 - 1.538945ms"
time="2025-07-31 10:04:00" level=info msg="GET /api/keys?group_id=1&page=1&page_size=12 - 200 - 1.586705ms"
time="2025-07-31 10:04:03" level=info msg="POST /api/keys/validate-group - 200 - 930.714µs"
time="2025-07-31 10:04:03" level=info msg="Starting manual validation for group kimi-k2"
time="2025-07-31 10:04:04" level=info msg="GET /api/tasks/status - 200 - 108.277µs"
time="2025-07-31 10:04:04" level=info msg="Manual validation finished for group kimi-k2: {TotalKeys:10 ValidKeys:6 InvalidKeys:4}"
time="2025-07-31 10:04:05" level=info msg="GET /api/tasks/status - 200 - 164.534µs"
time="2025-07-31 10:04:05" level=info msg="GET /api/keys?group_id=1&page=1&page_size=12 - 200 - 987.082µs"
time="2025-07-31 10:04:05" level=info msg="GET /api/groups/1/stats - 200 - 1.039933ms"
time="2025-07-31 10:04:13" level=info msg="POST /api/keys/restore-all-invalid - 200 - 881.771µs"
time="2025-07-31 10:04:14" level=info msg="GET /api/keys?group_id=1&page=1&page_size=12 - 200 - 969.788µs"
time="2025-07-31 10:04:14" level=info msg="GET /api/groups/1/stats - 200 - 1.11701ms"
time="2025-07-31 10:04:37" level=info msg="GET /api/settings - 200 - 166.507µs"
time="2025-07-31 10:05:15" level=info msg="GET /api/groups/list - 200 - 370.676µs"
time="2025-07-31 10:05:15" level=info msg="GET /api/dashboard/chart - 200 - 248.593µs"
time="2025-07-31 10:05:15" level=info msg="GET /api/dashboard/stats - 200 - 1.26444ms"
time="2025-07-31 10:06:04" level=info msg="GET /api/groups/config-options - 200 - 467.051µs"
time="2025-07-31 10:06:04" level=info msg="GET /api/groups - 200 - 961.813µs"
time="2025-07-31 10:06:04" level=info msg="GET /api/groups/1/stats - 200 - 1.026576ms"
time="2025-07-31 10:06:04" level=info msg="GET /api/keys?group_id=1&page=1&page_size=12 - 200 - 861.743µs"
time="2025-07-31 10:06:38" level=info msg="GET /api/groups/config-options - 200 - 189.912µs"
time="2025-07-31 10:06:38" level=info msg="GET /api/channel-types - 200 - 93.439µs"
time="2025-07-31 10:06:54" level=info msg="PUT /api/groups/1 - 200 - 3.889743ms"
time="2025-07-31 10:06:54" level=info msg="cache reloaded successfully" syncer=groups
time="2025-07-31 10:06:54" level=info msg="GET /api/groups - 200 - 619.46µs"
time="2025-07-31 10:06:55" level=info msg="GET /api/keys?group_id=1&page=1&page_size=12 - 200 - 1.005727ms"
time="2025-07-31 10:06:55" level=info msg="GET /api/groups/1/stats - 200 - 1.281572ms"
time="2025-07-31 10:08:46" level=info msg="GET / - 200 - 945.04µs"
time="2025-07-31 10:08:46" level=info msg="GET / - 200 - 1.540225ms"
time="2025-07-31 10:08:56" level=info msg="GET / - 200 - 180.994µs"
time="2025-07-31 10:09:02" level=info msg="GET /api/settings - 200 - 161.358µs"
time="2025-07-31 10:09:10" level=info msg="GET /assets/index-SPLiOoPZ.js - 200 - 54.013661ms"
time="2025-07-31 10:11:05" level=info msg="GET /api/groups/config-options - 200 - 279.503µs"
time="2025-07-31 10:11:05" level=info msg="GET /api/groups - 200 - 516.815µs"
time="2025-07-31 10:11:05" level=info msg="GET /api/groups/1/stats - 200 - 2.28856ms"
time="2025-07-31 10:11:05" level=info msg="GET /api/keys?group_id=1&page=1&page_size=12 - 200 - 1.456535ms"
time="2025-07-31 10:11:08" level=info msg="GET /api/groups/config-options - 200 - 214.539µs"
time="2025-07-31 10:11:08" level=info msg="GET /api/channel-types - 200 - 64.182µs"
time="2025-07-31 10:11:19" level=warning msg="Key has reached blacklist threshold, disabling." keyID=3 threshold=3
time="2025-07-31 10:11:19" level=warning msg="Key has reached blacklist threshold, disabling." keyID=4 threshold=3
time="2025-07-31 10:11:19" level=warning msg="POST /proxy/kimi-k2/v1/chat/completions - 400 - 1.215400863s"
time="2025-07-31 10:11:19" level=warning msg="Key has reached blacklist threshold, disabling." keyID=5 threshold=3
time="2025-07-31 10:11:56" level=info msg="Successfully flushed 1 request logs."
time="2025-07-31 10:12:20" level=warning msg="POST /proxy/kimi-k2/v1/chat/completions - 400 - 1.808362094s"
time="2025-07-31 10:12:21" level=warning msg="POST /proxy/kimi-k2/v1/chat/completions - 400 - 833.037521ms"
time="2025-07-31 10:12:22" level=warning msg="Key has reached blacklist threshold, disabling." keyID=7 threshold=3
time="2025-07-31 10:12:22" level=warning msg="POST /proxy/kimi-k2/v1/chat/completions - 400 - 667.021377ms"
time="2025-07-31 10:12:23" level=warning msg="Key has reached blacklist threshold, disabling." keyID=2 threshold=3
time="2025-07-31 10:12:23" level=warning msg="Key has reached blacklist threshold, disabling." keyID=9 threshold=3
time="2025-07-31 10:12:23" level=warning msg="POST /proxy/kimi-k2/v1/chat/completions - 400 - 727.739314ms"
time="2025-07-31 10:12:23" level=warning msg="Key has reached blacklist threshold, disabling." keyID=8 threshold=3
time="2025-07-31 10:12:56" level=info msg="Successfully flushed 4 request logs."
time="2025-07-31 10:14:21" level=info msg="PUT /api/groups/1 - 200 - 4.006132ms"
time="2025-07-31 10:14:21" level=info msg="cache reloaded successfully" syncer=groups
time="2025-07-31 10:14:21" level=info msg="GET /api/groups - 200 - 702.017µs"
time="2025-07-31 10:14:21" level=info msg="GET /api/keys?group_id=1&page=1&page_size=12 - 200 - 729.249µs"
time="2025-07-31 10:14:21" level=info msg="GET /api/groups/1/stats - 200 - 786.569µs"
time="2025-07-31 10:14:26" level=info msg="POST /api/keys/restore-all-invalid - 200 - 5.58597ms"
time="2025-07-31 10:14:27" level=info msg="GET /api/keys?group_id=1&page=1&page_size=12 - 200 - 797.74µs"
time="2025-07-31 10:14:27" level=info msg="GET /api/groups/1/stats - 200 - 1.074377ms"
time="2025-07-31 10:14:31" level=info msg="POST /api/keys/test-multiple - 200 - 743.216001ms"
time="2025-07-31 10:14:31" level=info msg="GET /api/keys?group_id=1&page=1&page_size=12 - 200 - 1.030904ms"
time="2025-07-31 10:14:31" level=info msg="GET /api/groups/1/stats - 200 - 1.267265ms"
time="2025-07-31 10:14:37" level=info msg="POST /api/keys/test-multiple - 200 - 462.548177ms"
time="2025-07-31 10:14:37" level=info msg="GET /api/keys?group_id=1&page=1&page_size=12 - 200 - 826.454µs"
time="2025-07-31 10:14:37" level=info msg="GET /api/groups/1/stats - 200 - 1.262425ms"
time="2025-07-31 10:15:16" level=info msg="PUT /api/groups/1 - 200 - 5.343388ms"
time="2025-07-31 10:15:16" level=info msg="cache reloaded successfully" syncer=groups
time="2025-07-31 10:15:16" level=info msg="GET /api/groups - 200 - 725.732µs"
time="2025-07-31 10:15:16" level=info msg="GET /api/groups/1/stats - 200 - 1.209905ms"
time="2025-07-31 10:15:16" level=info msg="GET /api/keys?group_id=1&page=1&page_size=12 - 200 - 1.327528ms"
time="2025-07-31 10:20:39" level=info msg="GET / - 200 - 85.222µs"
time="2025-07-31 10:21:00" level=info msg="GET / - 200 - 832.415µs"
time="2025-07-31 10:21:00" level=info msg="POST /proxy/targon/v1/chat/completions - 200 - 973.361515ms"
time="2025-07-31 10:21:04" level=info msg="GET /assets/logo-BV-_qOqO.png - 200 - 819.053466ms"
time="2025-07-31 10:21:13" level=info msg="GET /favicon.ico - 200 - 476.207µs"
time="2025-07-31 10:21:14" level=info msg="PRI * - 200 - 74.522µs"
time="2025-07-31 10:21:31" level=info msg="GET /favicon.ico - 200 - 227.974µs"
time="2025-07-31 10:21:34" level=info msg="PRI * - 200 - 64.914µs"
time="2025-07-31 10:21:56" level=info msg="GET /robots.txt - 200 - 233.846µs"
time="2025-07-31 10:21:56" level=info msg="Successfully flushed 1 request logs."
time="2025-07-31 10:22:27" level=warning msg="Key has reached blacklist threshold, disabling." keyID=10 threshold=3
time="2025-07-31 10:22:27" level=warning msg="Key has reached blacklist threshold, disabling." keyID=1 threshold=3
time="2025-07-31 10:22:28" level=info msg="POST /proxy/targon/v1/chat/completions - 200 - 1.097017326s"
time="2025-07-31 10:22:56" level=info msg="Successfully flushed 1 request logs."
time="2025-07-31 10:22:59" level=info msg="GET /api/settings - 200 - 171.957µs"
time="2025-07-31 10:23:21" level=info msg="POST /proxy/targon/v1/chat/completions - 200 - 673.181535ms"
time="2025-07-31 10:23:56" level=info msg="Successfully flushed 1 request logs."
time="2025-07-31 10:24:15" level=info msg="POST /proxy/targon/v1/chat/completions - 200 - 651.967672ms"
time="2025-07-31 10:24:37" level=info msg="GET /api/dashboard/stats - 200 - 629.889µs"
time="2025-07-31 10:24:37" level=info msg="GET /api/dashboard/chart - 200 - 575.705µs"
time="2025-07-31 10:24:37" level=info msg="GET /api/groups/list - 200 - 571.94µs"
time="2025-07-31 10:24:40" level=info msg="GET /api/groups/config-options - 200 - 262.049µs"
time="2025-07-31 10:24:40" level=info msg="GET /api/groups - 200 - 599.622µs"
time="2025-07-31 10:24:40" level=info msg="GET /api/groups/1/stats - 200 - 1.401779ms"
time="2025-07-31 10:24:40" level=info msg="GET /api/keys?group_id=1&page=1&page_size=12 - 200 - 808.299µs"
time="2025-07-31 10:24:47" level=info msg="POST /api/keys/restore-all-invalid - 200 - 3.918482ms"
time="2025-07-31 10:24:47" level=info msg="GET /api/keys?group_id=1&page=1&page_size=12 - 200 - 1.014031ms"
time="2025-07-31 10:24:47" level=info msg="GET /api/groups/1/stats - 200 - 1.054258ms"
time="2025-07-31 10:24:49" level=info msg="POST /api/keys/validate-group - 200 - 759.878µs"
time="2025-07-31 10:24:49" level=info msg="Starting manual validation for group targon"
time="2025-07-31 10:24:49" level=info msg="GET /api/tasks/status - 200 - 98.417µs"
time="2025-07-31 10:24:50" level=error msg="Failed to handle key success" error="failed to update key in DB: database is locked (5) (SQLITE_BUSY)" keyID=2
time="2025-07-31 10:24:50" level=info msg="Manual validation finished for group targon: {TotalKeys:10 ValidKeys:10 InvalidKeys:0}"
time="2025-07-31 10:24:51" level=info msg="GET /api/tasks/status - 200 - 105.792µs"
time="2025-07-31 10:24:51" level=info msg="GET /api/groups/1/stats - 200 - 1.227347ms"
time="2025-07-31 10:24:51" level=info msg="GET /api/keys?group_id=1&page=1&page_size=12 - 200 - 1.154929ms"
time="2025-07-31 10:24:56" level=info msg="Successfully flushed 1 request logs."
time="2025-07-31 10:27:08" level=info msg="GET /api/settings - 200 - 215.47µs"
time="2025-07-31 10:27:11" level=info
time="2025-07-31 10:27:11" level=info msg="========= System Settings ========="
time="2025-07-31 10:27:11" level=info msg="  --- Basic Settings ---"
time="2025-07-31 10:27:11" level=info msg="    App URL: https://load.ainima.de"
time="2025-07-31 10:27:11" level=info msg="    Request Log Retention: 7 days"
time="2025-07-31 10:27:11" level=info msg="    Request Log Write Interval: 1 minutes"
time="2025-07-31 10:27:11" level=info msg="  --- Request Behavior ---"
time="2025-07-31 10:27:11" level=info msg="    Request Timeout: 600 seconds"
time="2025-07-31 10:27:11" level=info msg="    Connect Timeout: 15 seconds"
time="2025-07-31 10:27:11" level=info msg="    Response Header Timeout: 600 seconds"
time="2025-07-31 10:27:11" level=info msg="    Idle Connection Timeout: 120 seconds"
time="2025-07-31 10:27:11" level=info msg="    Max Idle Connections: 100"
time="2025-07-31 10:27:11" level=info msg="    Max Idle Connections Per Host: 50"
time="2025-07-31 10:27:11" level=info msg="  --- Key & Group Behavior ---"
time="2025-07-31 10:27:11" level=info msg="    Max Retries: 3"
time="2025-07-31 10:27:11" level=info msg="    Blacklist Threshold: 3"
time="2025-07-31 10:27:11" level=info msg="    Key Validation Interval: 60 minutes"
time="2025-07-31 10:27:11" level=info msg="===================================="
time="2025-07-31 10:27:11" level=info
time="2025-07-31 10:27:11" level=info msg="cache reloaded successfully" syncer=system_settings
time="2025-07-31 10:27:11" level=info msg="cache reloaded successfully" syncer=groups
time="2025-07-31 10:27:11" level=info msg="PUT /api/settings - 200 - 104.337805ms"
time="2025-07-31 10:27:11" level=info msg="GET /api/settings - 200 - 187.145µs"
time="2025-07-31 10:28:33" level=warning msg="POST /proxy/targon/v1/chat/completions - 429 - 1.288436718s"
time="2025-07-31 10:28:44" level=info msg="GET /api/groups - 200 - 716.614µs"
time="2025-07-31 10:28:44" level=info msg="GET /api/groups/config-options - 200 - 189.11µs"
time="2025-07-31 10:28:44" level=info msg="GET /api/keys?group_id=1&page=1&page_size=12 - 200 - 679.654µs"
time="2025-07-31 10:28:44" level=info msg="GET /api/groups/1/stats - 200 - 1.986082ms"
time="2025-07-31 10:28:56" level=info msg="Successfully flushed 1 request logs."
time="2025-07-31 10:29:26" level=info msg="POST /proxy/targon/v1/chat/completions - 200 - 806.275797ms"
time="2025-07-31 10:29:32" level=warning msg="POST /proxy/targon/v1/chat/completions - 429 - 924.186682ms"
time="2025-07-31 10:29:56" level=info msg="Successfully flushed 2 request logs."
time="2025-07-31 10:30:00" level=warning msg="POST /proxy/targon/v1/chat/completions - 429 - 1.198546397s"
time="2025-07-31 10:30:01" level=warning msg="POST /proxy/targon/v1/chat/completions - 429 - 915.477432ms"
time="2025-07-31 10:30:02" level=warning msg="POST /proxy/targon/v1/chat/completions - 429 - 639.937141ms"
time="2025-07-31 10:30:02" level=warning msg="Key has reached blacklist threshold, disabling." keyID=7 threshold=3
time="2025-07-31 10:30:03" level=warning msg="Key has reached blacklist threshold, disabling." keyID=8 threshold=3
time="2025-07-31 10:30:03" level=info msg="GET /assets/Logs-D_3m6Xv1.css - 200 - 2.406984ms"
time="2025-07-31 10:30:03" level=warning msg="Key has reached blacklist threshold, disabling." keyID=9 threshold=3
time="2025-07-31 10:30:03" level=info msg="GET /assets/Logs-BAsgvVXW.js - 200 - 1.198282ms"
time="2025-07-31 10:30:03" level=warning msg="Key has reached blacklist threshold, disabling." keyID=6 threshold=3
time="2025-07-31 10:30:03" level=warning msg="POST /proxy/targon/v1/chat/completions - 429 - 566.015515ms"
time="2025-07-31 10:30:03" level=warning msg="Key has reached blacklist threshold, disabling." keyID=2 threshold=3
time="2025-07-31 10:30:04" level=info msg="GET /api/logs?page=1&page_size=15 - 200 - 1.297911ms"
time="2025-07-31 10:30:56" level=info msg="Successfully flushed 4 request logs."
time="2025-07-31 10:36:44" level=info msg="GET /.git/config - 200 - 483.471µs"
time="2025-07-31 10:37:02" level=info msg="GET / - 200 - 200.482µs"
time="2025-07-31 10:37:03" level=info msg="GET / - 200 - 604.842µs"
time="2025-07-31 10:37:09" level=info msg="GET /.git/config - 200 - 404.391µs"
time="2025-07-31 10:37:16" level=info msg="GET / - 200 - 564.285µs"
time="2025-07-31 10:37:29" level=info msg="GET /.git/config - 200 - 220.26µs"
time="2025-07-31 10:37:33" level=info msg="GET /assets/index-SPLiOoPZ.js - 200 - 52.603751ms"
time="2025-07-31 10:41:16" level=info msg="GET /.git/config - 200 - 457.822µs"
time="2025-07-31 10:41:39" level=info msg="GET /.git/config - 200 - 177.308µs"
time="2025-07-31 10:50:10" level=info msg="GET / - 200 - 300.622µs"
time="2025-07-31 10:50:10" level=info msg="GET / - 200 - 543.895µs"
time="2025-07-31 10:50:16" level=info msg="GET / - 200 - 196.975µs"
time="2025-07-31 10:50:24" level=info msg="GET /assets/index-SPLiOoPZ.js - 200 - 69.563279ms"
time="2025-07-31 10:53:53" level=info msg="GET / - 200 - 181.115µs"
time="2025-07-31 10:58:57" level=error msg="Failed to handle key success" error="failed to update key in DB: database is locked (5) (SQLITE_BUSY)" keyID=6
time="2025-07-31 10:58:57" level=error msg="Failed to handle key success" error="failed to update key in DB: database is locked (5) (SQLITE_BUSY)" keyID=8
time="2025-07-31 10:58:57" level=error msg="Failed to handle key success" error="failed to update key in DB: database is locked (5) (SQLITE_BUSY)" keyID=9
time="2025-07-31 10:58:57" level=info msg="CronChecker: Group 'targon' validation finished. Total checked: 5, became valid: 5. Duration: 958.521993ms."
time="2025-07-31 11:02:30" level=info msg="GET / - 200 - 479.042µs"
time="2025-07-31 11:02:32" level=info msg="GET /assets/logo-BV-_qOqO.png - 200 - 1.027396ms"
time="2025-07-31 11:02:32" level=info msg="GET /js/twint_ch.js - 200 - 1.115926ms"
time="2025-07-31 11:02:32" level=info msg="GET /js/lkk_ch.js - 200 - 618.839µs"
time="2025-07-31 12:03:57" level=error msg="Failed to handle key success" error="failed to update key in DB: database is locked (5) (SQLITE_BUSY)" keyID=8
time="2025-07-31 12:03:57" level=info msg="CronChecker: Group 'targon' validation finished. Total checked: 3, became valid: 3. Duration: 846.013647ms."
time="2025-07-31 12:03:59" level=info msg="GET / - 200 - 1.504803ms"
time="2025-07-31 12:03:59" level=info msg="GET / - 200 - 198.518µs"
time="2025-07-31 12:13:13" level=info msg="GET / - 200 - 670.797µs"
time="2025-07-31 12:13:13" level=info msg="GET /assets/index-Gswog8ov.css - 200 - 437.232µs"
time="2025-07-31 12:13:13" level=info msg="GET /assets/index-SPLiOoPZ.js - 200 - 65.929427ms"
time="2025-07-31 12:13:17" level=info msg="GET / - 200 - 459.215µs"
time="2025-07-31 12:13:17" level=info msg="GET /assets/index-Gswog8ov.css - 200 - 587.147µs"
time="2025-07-31 12:13:17" level=info msg="GET /assets/index-SPLiOoPZ.js - 200 - 82.550692ms"
time="2025-07-31 12:39:27" level=info msg="GET / - 200 - 468.331µs"
time="2025-07-31 12:39:28" level=info msg="GET / - 200 - 293.788µs"
time="2025-07-31 12:39:28" level=info msg="GET /assets/index-Gswog8ov.css - 200 - 2.467245ms"
time="2025-07-31 12:39:28" level=info msg="GET /assets/index-SPLiOoPZ.js - 200 - 60.929418ms"
time="2025-07-31 12:39:28" level=info msg="GET /assets/Login-DzpmRpZS.css - 200 - 243.222µs"
time="2025-07-31 12:39:28" level=info msg="GET /assets/Login-C7BGdm6I.js - 200 - 416.392µs"
time="2025-07-31 12:39:28" level=info msg="GET /assets/logo-BV-_qOqO.png - 200 - 2.820898ms"
time="2025-07-31 12:39:29" level=info msg="GET /login - 200 - 259.223µs"
time="2025-07-31 12:39:29" level=info msg="GET /favicon.ico - 200 - 266.166µs"
time="2025-07-31 12:39:29" level=info msg="GET /favicon.ico - 200 - 321.01µs"
time="2025-07-31 12:39:29" level=info msg="GET /assets/logo-BV-_qOqO.png - 200 - 846.541µs"
time="2025-07-31 13:08:57" level=info msg="CronChecker: Group 'targon' validation finished. Total checked: 1, became valid: 1. Duration: 782.270539ms."
time="2025-07-31 13:53:05" level=info msg="GET / - 200 - 556.188µs"
time="2025-07-31 13:53:06" level=info msg="GET /api/dashboard/stats - 200 - 797.136µs"
time="2025-07-31 13:53:06" level=info msg="GET /api/dashboard/chart - 200 - 548.113µs"
time="2025-07-31 13:53:06" level=info msg="GET /api/groups/list - 200 - 841.221µs"
time="2025-07-31 13:53:06" level=info msg="GET /api/tasks/status - 200 - 61.806µs"
time="2025-07-31 13:53:10" level=info msg="GET /api/groups/config-options - 200 - 211.162µs"
time="2025-07-31 13:53:10" level=info msg="GET /api/groups - 200 - 690.213µs"
time="2025-07-31 13:53:11" level=info msg="GET /api/keys?group_id=1&page=1&page_size=12 - 200 - 2.114222ms"
time="2025-07-31 13:53:11" level=info msg="GET /api/groups/1/stats - 200 - 2.668867ms"
time="2025-07-31 13:53:16" level=info msg="GET /api/channel-types - 200 - 54.834µs"
time="2025-07-31 13:53:16" level=info msg="GET /api/groups/config-options - 200 - 271.426µs"
time="2025-07-31 13:53:40" level=info msg="POST /api/groups - 200 - 5.796627ms"
time="2025-07-31 13:53:40" level=info msg="cache reloaded successfully" syncer=groups
time="2025-07-31 13:53:40" level=info msg="GET /api/groups - 200 - 781.146µs"
time="2025-07-31 13:53:41" level=info msg="GET /api/keys?group_id=2&page=1&page_size=12 - 200 - 1.207036ms"
time="2025-07-31 13:53:41" level=info msg="GET /api/groups/2/stats - 200 - 1.199752ms"
time="2025-07-31 13:53:45" level=info msg="GET /api/settings - 200 - 212.565µs"
time="2025-07-31 13:53:56" level=info msg="CronChecker: Group 'gemini' has no invalid keys to check."
time="2025-07-31 13:54:24" level=info msg="GET /api/logs?page=1&page_size=15 - 200 - 1.17916ms"
time="2025-07-31 13:54:27" level=info msg="GET /api/groups/config-options - 200 - 161.067µs"
time="2025-07-31 13:54:27" level=info msg="GET /api/groups - 200 - 910.698µs"
time="2025-07-31 13:54:28" level=info msg="GET /api/groups/2/stats - 200 - 1.258461ms"
time="2025-07-31 13:54:28" level=info msg="GET /api/keys?group_id=2&page=1&page_size=12 - 200 - 585.207µs"
time="2025-07-31 13:54:30" level=info msg="GET /api/groups/1/stats - 200 - 1.375604ms"
time="2025-07-31 13:54:30" level=info msg="GET /api/keys?group_id=1&page=1&page_size=12 - 200 - 2.056813ms"
time="2025-07-31 13:54:30" level=info msg="GET /api/keys?group_id=2&page=1&page_size=12 - 200 - 713.451µs"
time="2025-07-31 13:54:30" level=info msg="GET /api/groups/2/stats - 200 - 1.20571ms"
time="2025-07-31 13:57:27" level=error msg="Failed to select a key for group gemini on attempt 1: No active API keys available for this group"
time="2025-07-31 13:57:27" level=error msg="GET /proxy/gemini/api/groups - 503 - 353.794µs"
time="2025-07-31 13:57:56" level=info msg="Successfully flushed 1 request logs."
time="2025-07-31 13:58:26" level=error msg="Failed to select a key for group gemini on attempt 1: No active API keys available for this group"
time="2025-07-31 13:58:26" level=error msg="GET /proxy/gemini/api/groups - 503 - 240.117µs"
time="2025-07-31 13:58:56" level=info msg="Successfully flushed 1 request logs."
time="2025-07-31 13:59:27" level=error msg="Failed to select a key for group gemini on attempt 1: No active API keys available for this group"
time="2025-07-31 13:59:27" level=error msg="GET /proxy/gemini/api/groups - 503 - 247.882µs"
time="2025-07-31 13:59:56" level=info msg="Successfully flushed 1 request logs."
time="2025-07-31 14:00:26" level=error msg="Failed to select a key for group gemini on attempt 1: No active API keys available for this group"
time="2025-07-31 14:00:26" level=error msg="GET /proxy/gemini/api/groups - 503 - 288.27µs"
time="2025-07-31 14:00:56" level=info msg="Successfully flushed 1 request logs."
time="2025-07-31 14:01:26" level=error msg="Failed to select a key for group gemini on attempt 1: No active API keys available for this group"
time="2025-07-31 14:01:26" level=error msg="GET /proxy/gemini/api/groups - 503 - 579.063µs"
time="2025-07-31 14:01:42" level=info msg="GET /keys?groupId=2 - 200 - 560.829µs"
time="2025-07-31 14:01:43" level=info msg="GET /api/groups/config-options - 200 - 180.002µs"
time="2025-07-31 14:01:43" level=info msg="GET /api/groups - 200 - 758.865µs"
time="2025-07-31 14:01:43" level=info msg="GET /api/tasks/status - 200 - 49.434µs"
time="2025-07-31 14:01:43" level=info msg="GET /api/keys?group_id=2&page=1&page_size=12 - 200 - 548.415µs"
time="2025-07-31 14:01:43" level=info msg="GET /api/groups/2/stats - 200 - 1.525717ms"
time="2025-07-31 14:01:51" level=info msg="GET /api/logs?page=1&page_size=15 - 200 - 1.015785ms"
time="2025-07-31 14:01:56" level=info msg="Successfully flushed 1 request logs."
time="2025-07-31 14:01:57" level=info msg="GET /api/groups/list - 200 - 358.914µs"
time="2025-07-31 14:01:57" level=info msg="GET /api/dashboard/stats - 200 - 1.332709ms"
time="2025-07-31 14:01:57" level=info msg="GET /api/dashboard/chart - 200 - 552.102µs"
time="2025-07-31 14:01:59" level=info msg="GET /api/settings - 200 - 434.588µs"
time="2025-07-31 14:02:26" level=error msg="Failed to select a key for group gemini on attempt 1: No active API keys available for this group"
time="2025-07-31 14:02:26" level=error msg="GET /proxy/gemini/api/groups - 503 - 294.381µs"
time="2025-07-31 14:02:51" level=info msg="GET /api/groups/config-options - 200 - 204.64µs"
time="2025-07-31 14:02:51" level=info msg="GET /api/groups - 200 - 821.966µs"
time="2025-07-31 14:02:51" level=info msg="GET /api/groups/2/stats - 200 - 1.156683ms"
time="2025-07-31 14:02:51" level=info msg="GET /api/keys?group_id=2&page=1&page_size=12 - 200 - 462.341µs"
time="2025-07-31 14:02:56" level=info msg="Successfully flushed 1 request logs."
time="2025-07-31 14:03:27" level=error msg="Failed to select a key for group gemini on attempt 1: No active API keys available for this group"
time="2025-07-31 14:03:27" level=error msg="GET /proxy/gemini/api/groups - 503 - 337.653µs"
time="2025-07-31 14:03:50" level=info msg="GET /api/channel-types - 200 - 92.265µs"
time="2025-07-31 14:03:50" level=info msg="GET /api/groups/config-options - 200 - 179.092µs"
time="2025-07-31 14:03:56" level=info msg="Successfully flushed 1 request logs."
time="2025-07-31 14:04:26" level=error msg="Failed to select a key for group gemini on attempt 1: No active API keys available for this group"
time="2025-07-31 14:04:26" level=error msg="GET /proxy/gemini/api/groups - 503 - 600.854µs"
time="2025-07-31 14:04:56" level=info msg="Successfully flushed 1 request logs."
time="2025-07-31 14:05:20" level=info msg="GET /api/groups - 200 - 1.057543ms"
time="2025-07-31 14:05:20" level=info msg="POST /api/keys/add-async - 200 - 680.436µs"
time="2025-07-31 14:06:36" level=info msg="HEAD / - 200 - 637.644µs"
time="2025-07-31 14:11:53" level=info msg="GET /keys?groupId=2 - 200 - 285.293µs"
time="2025-07-31 14:11:54" level=info msg="GET /api/groups/config-options - 200 - 201.273µs"
time="2025-07-31 14:11:54" level=info msg="GET /api/groups - 200 - 687.88µs"
time="2025-07-31 14:11:55" level=info msg="GET /api/tasks/status - 200 - 130.007µs"
time="2025-07-31 14:11:55" level=info msg="GET /api/groups/2/stats - 200 - 1.726396ms"
time="2025-07-31 14:11:55" level=info msg="GET /api/keys?group_id=2&page=1&page_size=12 - 200 - 1.015142ms"
time="2025-07-31 14:11:55" level=info msg="GET /api/keys?group_id=2&page=1&page_size=12 - 200 - 1.168153ms"
time="2025-07-31 14:11:55" level=info msg="GET /api/groups/2/stats - 200 - 2.243251ms"
time="2025-07-31 14:12:02" level=info msg="POST /api/keys/validate-group - 200 - 932.906µs"
time="2025-07-31 14:12:02" level=info msg="Starting manual validation for group gemini"
time="2025-07-31 14:12:02" level=info msg="GET /api/tasks/status - 200 - 112.894µs"
time="2025-07-31 14:12:02" level=info msg="Manual validation finished for group gemini: {TotalKeys:4 ValidKeys:4 InvalidKeys:0}"
time="2025-07-31 14:12:04" level=info msg="GET /api/tasks/status - 200 - 119.298µs"
time="2025-07-31 14:12:04" level=info msg="GET /api/keys?group_id=2&page=1&page_size=12 - 200 - 1.098893ms"
time="2025-07-31 14:12:04" level=info msg="GET /api/groups/2/stats - 200 - 1.621247ms"
time="2025-07-31 14:12:12" level=info msg="GET /api/settings - 200 - 171.377µs"
time="2025-07-31 14:12:14" level=info msg="GET /api/groups/config-options - 200 - 181.405µs"
time="2025-07-31 14:12:14" level=info msg="GET /api/groups - 200 - 682.589µs"
time="2025-07-31 14:12:15" level=info msg="GET /api/keys?group_id=2&page=1&page_size=12 - 200 - 906.104µs"
time="2025-07-31 14:12:15" level=info msg="GET /api/groups/2/stats - 200 - 1.300074ms"
time="2025-07-31 14:12:16" level=info msg="GET /api/groups/list - 200 - 364.754µs"
time="2025-07-31 14:12:16" level=info msg="GET /api/dashboard/stats - 200 - 1.12399ms"
time="2025-07-31 14:12:16" level=info msg="GET /api/dashboard/chart - 200 - 337.913µs"
time="2025-07-31 14:13:56" level=info msg="CronChecker: Group 'targon' has no invalid keys to check."
time="2025-07-31 14:16:21" level=info msg="POST /api/keys/add-async - 200 - 633.325µs"
time="2025-07-31 14:18:21" level=info msg="POST /api/keys/add-async - 200 - 797.478µs"
time="2025-07-31 14:25:21" level=info msg="GET /api/groups - 200 - 634.167µs"
time="2025-07-31 14:25:21" level=info msg="POST /api/keys/add-async - 200 - 608.309µs"
time="2025-07-31 14:36:21" level=info msg="POST /api/keys/add-async - 200 - 592.468µs"
time="2025-07-31 14:37:21" level=info msg="POST /api/keys/add-async - 200 - 747.883µs"
time="2025-07-31 14:38:21" level=info msg="POST /api/keys/add-async - 200 - 554.935µs"
time="2025-07-31 14:38:38" level=info msg="GET / - 200 - 586.345µs"
time="2025-07-31 14:38:41" level=info msg="GET /api/dashboard/stats - 200 - 954.727µs"
time="2025-07-31 14:38:41" level=info msg="GET /api/groups/list - 200 - 221.662µs"
time="2025-07-31 14:38:41" level=info msg="GET /api/dashboard/chart - 200 - 659.895µs"
time="2025-07-31 14:38:41" level=info msg="GET /api/tasks/status - 200 - 100.16µs"
time="2025-07-31 14:38:44" level=info msg="GET /api/groups/config-options - 200 - 235.568µs"
time="2025-07-31 14:38:44" level=info msg="GET /api/groups - 200 - 718.808µs"
time="2025-07-31 14:38:58" level=info msg="GET /api/keys?group_id=2&page=1&page_size=12 - 200 - 884.703µs"
time="2025-07-31 14:38:58" level=info msg="GET /api/groups/2/stats - 200 - 1.352703ms"
time="2025-07-31 14:39:18" level=info msg="GET /api/keys?group_id=2&page=2&page_size=12 - 200 - 1.058374ms"
time="2025-07-31 14:39:21" level=info msg="POST /api/keys/add-async - 200 - 688.209µs"
time="2025-07-31 14:40:20" level=info msg="POST /api/keys/add-async - 200 - 638.896µs"
time="2025-07-31 14:41:20" level=info msg="GET /api/groups - 200 - 582.548µs"
time="2025-07-31 14:41:20" level=info msg="POST /api/keys/add-async - 200 - 620.691µs"
time="2025-07-31 14:42:21" level=info msg="POST /api/keys/add-async - 200 - 549.295µs"
time="2025-07-31 14:43:21" level=info msg="POST /api/keys/add-async - 200 - 595.623µs"
time="2025-07-31 14:44:21" level=info msg="POST /api/keys/add-async - 200 - 594.742µs"
time="2025-07-31 14:45:21" level=info msg="POST /api/keys/add-async - 200 - 669.343µs"
time="2025-07-31 14:46:21" level=info msg="POST /api/keys/add-async - 200 - 592.758µs"
time="2025-07-31 14:47:21" level=info msg="POST /api/keys/add-async - 200 - 671.798µs"
time="2025-07-31 14:48:21" level=info msg="POST /api/keys/add-async - 200 - 682.82µs"
time="2025-07-31 14:49:20" level=info msg="POST /api/keys/add-async - 200 - 671.448µs"
time="2025-07-31 14:50:21" level=info msg="POST /api/keys/add-async - 200 - 631.172µs"
time="2025-07-31 14:52:21" level=info msg="POST /api/keys/add-async - 200 - 574.123µs"
time="2025-07-31 14:53:21" level=info msg="POST /api/keys/add-async - 200 - 737.072µs"
time="2025-07-31 14:54:21" level=info msg="POST /api/keys/add-async - 200 - 509.749µs"
time="2025-07-31 14:55:21" level=info msg="POST /api/keys/add-async - 200 - 533.376µs"
time="2025-07-31 14:56:21" level=info msg="GET /api/groups - 200 - 951.761µs"
time="2025-07-31 14:56:21" level=info msg="POST /api/keys/add-async - 200 - 764.215µs"
time="2025-07-31 14:58:21" level=info msg="POST /api/keys/add-async - 200 - 599.07µs"
time="2025-07-31 14:58:56" level=info msg="CronChecker: Group 'gemini' has no invalid keys to check."
time="2025-07-31 14:59:21" level=info msg="POST /api/keys/add-async - 200 - 780.956µs"
time="2025-07-31 15:00:21" level=info msg="POST /api/keys/add-async - 200 - 639.286µs"
time="2025-07-31 15:01:21" level=info msg="POST /api/keys/add-async - 200 - 668.842µs"
time="2025-07-31 15:02:21" level=info msg="POST /api/keys/add-async - 200 - 623.356µs"
time="2025-07-31 15:03:21" level=info msg="POST /api/keys/add-async - 200 - 772.651µs"
time="2025-07-31 15:04:21" level=info msg="POST /api/keys/add-async - 200 - 910.073µs"
time="2025-07-31 15:05:21" level=info msg="POST /api/keys/add-async - 200 - 656.942µs"
time="2025-07-31 15:06:21" level=info msg="POST /api/keys/add-async - 200 - 676.868µs"
time="2025-07-31 15:07:21" level=info msg="POST /api/keys/add-async - 200 - 823.448µs"
time="2025-07-31 15:08:21" level=info msg="POST /api/keys/add-async - 200 - 709.442µs"
time="2025-07-31 15:09:21" level=info msg="POST /api/keys/add-async - 200 - 514.479µs"
time="2025-07-31 15:10:03" level=info msg="GET / - 200 - 658.123µs"
time="2025-07-31 15:10:03" level=info msg="GET /assets/logo-BV-_qOqO.png - 200 - 1.342356ms"
time="2025-07-31 15:10:21" level=info msg="POST /api/keys/add-async - 200 - 693.841µs"
time="2025-07-31 15:11:21" level=info msg="POST /api/keys/add-async - 200 - 655.679µs"
time="2025-07-31 15:12:21" level=info msg="GET /api/groups - 200 - 1.508682ms"
time="2025-07-31 15:12:21" level=info msg="POST /api/keys/add-async - 200 - 719.55µs"
time="2025-07-31 15:13:21" level=info msg="POST /api/keys/add-async - 200 - 643.755µs"
time="2025-07-31 15:13:56" level=info msg="CronChecker: Group 'targon' has no invalid keys to check."
time="2025-07-31 15:14:25" level=info msg="POST /api/keys/add-async - 200 - 667.09µs"
time="2025-07-31 15:15:21" level=info msg="POST /api/keys/add-async - 200 - 626.904µs"
time="2025-07-31 15:15:28" level=info msg="GET / - 200 - 774.113µs"
time="2025-07-31 15:16:21" level=info msg="POST /api/keys/add-async - 200 - 867.882µs"
time="2025-07-31 15:21:21" level=info msg="POST /api/keys/add-async - 200 - 724.068µs"
time="2025-07-31 15:22:21" level=info msg="POST /api/keys/add-async - 200 - 602.658µs"
time="2025-07-31 15:23:21" level=info msg="POST /api/keys/add-async - 200 - 730.64µs"
time="2025-07-31 15:24:21" level=info msg="POST /api/keys/add-async - 200 - 719.289µs"
time="2025-07-31 15:26:21" level=info msg="POST /api/keys/add-async - 200 - 664.395µs"
time="2025-07-31 15:27:21" level=info msg="GET /api/groups - 200 - 854.246µs"
time="2025-07-31 15:27:21" level=info msg="POST /api/keys/add-async - 200 - 694.952µs"
time="2025-07-31 15:28:21" level=info msg="POST /api/keys/add-async - 200 - 631.142µs"
time="2025-07-31 15:29:05" level=info msg="GET / - 200 - 734.488µs"
time="2025-07-31 15:29:05" level=info msg="GET /assets/logo-BV-_qOqO.png - 200 - 953.474µs"
time="2025-07-31 15:29:21" level=info msg="POST /api/keys/add-async - 200 - 606.945µs"
time="2025-07-31 15:30:21" level=info msg="POST /api/keys/add-async - 200 - 644.817µs"
time="2025-07-31 15:31:21" level=info msg="POST /api/keys/add-async - 200 - 553.464µs"
time="2025-07-31 15:32:21" level=info msg="POST /api/keys/add-async - 200 - 639.808µs"
time="2025-07-31 15:33:21" level=info msg="POST /api/keys/add-async - 200 - 744.547µs"
time="2025-07-31 15:34:21" level=info msg="POST /api/keys/add-async - 200 - 737.825µs"
time="2025-07-31 15:35:21" level=info msg="POST /api/keys/add-async - 200 - 703.999µs"
time="2025-07-31 15:36:21" level=info msg="POST /api/keys/add-async - 200 - 766.889µs"
time="2025-07-31 15:38:20" level=info msg="POST /api/keys/add-async - 200 - 686.004µs"
time="2025-07-31 15:40:21" level=info msg="POST /api/keys/add-async - 200 - 696.244µs"
time="2025-07-31 15:41:21" level=info msg="POST /api/keys/add-async - 200 - 732.984µs"
time="2025-07-31 15:42:21" level=info msg="POST /api/keys/add-async - 200 - 643.855µs"
time="2025-07-31 15:43:21" level=info msg="GET /api/groups - 200 - 1.207727ms"
time="2025-07-31 15:43:21" level=info msg="POST /api/keys/add-async - 200 - 682.628µs"
time="2025-07-31 15:44:21" level=info msg="POST /api/keys/add-async - 200 - 573.04µs"
time="2025-07-31 15:45:21" level=info msg="POST /api/keys/add-async - 200 - 807.085µs"
time="2025-07-31 15:48:21" level=info msg="POST /api/keys/add-async - 200 - 706.695µs"
time="2025-07-31 15:52:20" level=info msg="POST /api/keys/add-async - 200 - 540.629µs"
time="2025-07-31 15:54:21" level=info msg="POST /api/keys/add-async - 200 - 659.805µs"
time="2025-07-31 15:55:21" level=info msg="POST /api/keys/add-async - 200 - 553.843µs"
time="2025-07-31 15:56:21" level=info msg="POST /api/keys/add-async - 200 - 773.962µs"
time="2025-07-31 15:59:20" level=info msg="GET /api/groups - 200 - 670.916µs"
time="2025-07-31 15:59:20" level=info msg="POST /api/keys/add-async - 200 - 611.283µs"
time="2025-07-31 16:03:56" level=info msg="CronChecker: Group 'gemini' has no invalid keys to check."
time="2025-07-31 16:05:21" level=info msg="POST /api/keys/add-async - 200 - 717.986µs"
time="2025-07-31 16:08:21" level=info msg="POST /api/keys/add-async - 200 - 755.948µs"
time="2025-07-31 16:09:21" level=info msg="POST /api/keys/add-async - 200 - 604.23µs"
time="2025-07-31 16:10:21" level=info msg="POST /api/keys/add-async - 200 - 671.909µs"
time="2025-07-31 16:13:21" level=info msg="POST /api/keys/add-async - 200 - 1.285498ms"
time="2025-07-31 16:14:21" level=info msg="GET /api/groups - 200 - 544.477µs"
time="2025-07-31 16:14:21" level=info msg="POST /api/keys/add-async - 200 - 830.613µs"
time="2025-07-31 16:18:56" level=info msg="CronChecker: Group 'targon' has no invalid keys to check."
time="2025-07-31 16:20:21" level=info msg="POST /api/keys/add-async - 200 - 729.258µs"
time="2025-07-31 16:27:20" level=info msg="POST /api/keys/add-async - 200 - 700.624µs"
time="2025-07-31 16:28:21" level=info msg="POST /api/keys/add-async - 200 - 661.109µs"
time="2025-07-31 16:29:21" level=info msg="GET /api/groups - 200 - 658.683µs"
time="2025-07-31 16:29:21" level=info msg="POST /api/keys/add-async - 200 - 695.544µs"
time="2025-07-31 16:35:21" level=info msg="POST /api/keys/add-async - 200 - 596.846µs"
time="2025-07-31 16:43:21" level=info msg="POST /api/keys/add-async - 200 - 567.449µs"
time="2025-07-31 16:46:21" level=info msg="GET /api/groups - 200 - 732.507µs"
time="2025-07-31 16:46:21" level=info msg="POST /api/keys/add-async - 200 - 615.022µs"
time="2025-07-31 16:47:21" level=info msg="POST /api/keys/add-async - 200 - 660.289µs"
time="2025-07-31 16:48:21" level=info msg="POST /api/keys/add-async - 200 - 620.242µs"
time="2025-07-31 16:49:21" level=info msg="POST /api/keys/add-async - 200 - 720.522µs"
time="2025-07-31 16:51:21" level=info msg="POST /api/keys/add-async - 200 - 889.294µs"
time="2025-07-31 16:52:20" level=info msg="POST /api/keys/add-async - 200 - 664.205µs"
time="2025-07-31 16:53:21" level=info msg="POST /api/keys/add-async - 200 - 585.686µs"
time="2025-07-31 16:55:21" level=info msg="POST /api/keys/add-async - 200 - 651.952µs"
time="2025-07-31 16:56:21" level=info msg="POST /api/keys/add-async - 200 - 957.194µs"
time="2025-07-31 16:57:21" level=info msg="POST /api/keys/add-async - 200 - 625.712µs"
time="2025-07-31 16:58:21" level=info msg="POST /api/keys/add-async - 200 - 594.112µs"
time="2025-07-31 16:59:21" level=info msg="POST /api/keys/add-async - 200 - 617.606µs"
time="2025-07-31 17:02:21" level=info msg="GET /api/groups - 200 - 706.765µs"
time="2025-07-31 17:02:21" level=info msg="POST /api/keys/add-async - 200 - 553.224µs"
time="2025-07-31 17:03:56" level=info msg="CronChecker: Group 'gemini' has no invalid keys to check."
time="2025-07-31 17:05:21" level=info msg="POST /api/keys/add-async - 200 - 666.679µs"
time="2025-07-31 17:07:20" level=info msg="POST /api/keys/add-async - 200 - 815.443µs"
time="2025-07-31 17:10:20" level=info msg="POST /api/keys/add-async - 200 - 711.655µs"
time="2025-07-31 17:11:20" level=info msg="POST /api/keys/add-async - 200 - 560.477µs"
time="2025-07-31 17:14:21" level=info msg="POST /api/keys/add-async - 200 - 899.122µs"
time="2025-07-31 17:16:21" level=info msg="POST /api/keys/add-async - 200 - 808.469µs"
time="2025-07-31 17:17:21" level=info msg="GET /api/groups - 200 - 720.732µs"
time="2025-07-31 17:17:21" level=info msg="POST /api/keys/add-async - 200 - 660.877µs"
time="2025-07-31 17:18:56" level=info msg="CronChecker: Group 'targon' has no invalid keys to check."
time="2025-07-31 17:21:20" level=info msg="POST /api/keys/add-async - 200 - 668.668µs"
time="2025-07-31 17:25:21" level=info msg="POST /api/keys/add-async - 200 - 667.328µs"
time="2025-07-31 17:30:22" level=info msg="POST /api/keys/add-async - 200 - 688.408µs"
time="2025-07-31 17:32:21" level=info msg="GET /api/groups - 200 - 746.209µs"
time="2025-07-31 17:32:22" level=info msg="POST /api/keys/add-async - 200 - 517.264µs"
time="2025-07-31 17:34:21" level=info msg="POST /api/keys/add-async - 200 - 560.486µs"
time="2025-07-31 17:35:21" level=info msg="POST /api/keys/add-async - 200 - 701.475µs"
time="2025-07-31 17:37:21" level=info msg="POST /api/keys/add-async - 200 - 552.601µs"
time="2025-07-31 17:38:21" level=info msg="POST /api/keys/add-async - 200 - 654.013µs"
time="2025-07-31 17:39:22" level=info msg="POST /api/keys/add-async - 200 - 735.98µs"
time="2025-07-31 17:40:21" level=info msg="POST /api/keys/add-async - 200 - 528.755µs"
time="2025-07-31 17:44:20" level=info msg="POST /api/keys/add-async - 200 - 515.5µs"
time="2025-07-31 17:45:21" level=info msg="POST /api/keys/add-async - 200 - 946.792µs"
time="2025-07-31 17:46:21" level=info msg="POST /api/keys/add-async - 200 - 693.68µs"
time="2025-07-31 17:46:27" level=info msg="GET / - 200 - 721.563µs"
time="2025-07-31 17:46:27" level=info msg="GET /assets/index-Gswog8ov.css - 200 - 1.008068ms"
time="2025-07-31 17:46:27" level=info msg="GET /assets/index-SPLiOoPZ.js - 200 - 57.355958ms"
time="2025-07-31 17:46:28" level=info msg="GET /assets/Login-C7BGdm6I.js - 200 - 421.582µs"
time="2025-07-31 17:46:28" level=info msg="GET /assets/Login-DzpmRpZS.css - 200 - 191.093µs"
time="2025-07-31 17:46:28" level=info msg="GET /assets/logo-BV-_qOqO.png - 200 - 1.106976ms"
time="2025-07-31 17:47:20" level=info msg="POST /api/keys/add-async - 200 - 597.077µs"
time="2025-07-31 17:50:21" level=info msg="GET /api/groups - 200 - 570.194µs"
time="2025-07-31 17:50:21" level=info msg="POST /api/keys/add-async - 200 - 574.903µs"
time="2025-07-31 17:55:21" level=info msg="POST /api/keys/add-async - 200 - 710.004µs"
time="2025-07-31 18:00:21" level=info msg="POST /api/keys/add-async - 200 - 700.274µs"
time="2025-07-31 18:02:21" level=info msg="POST /api/keys/add-async - 200 - 1.517219ms"
time="2025-07-31 18:03:21" level=info msg="POST /api/keys/add-async - 200 - 715.343µs"
time="2025-07-31 18:08:56" level=info msg="CronChecker: Group 'gemini' has no invalid keys to check."
time="2025-07-31 18:15:21" level=info msg="GET /api/groups - 200 - 1.405586ms"
time="2025-07-31 18:15:21" level=info msg="POST /api/keys/add-async - 200 - 711.945µs"
time="2025-07-31 18:17:21" level=info msg="POST /api/keys/add-async - 200 - 737.132µs"
time="2025-07-31 18:22:20" level=info msg="POST /api/keys/add-async - 200 - 527.433µs"
time="2025-07-31 18:23:21" level=info msg="POST /api/keys/add-async - 200 - 611.344µs"
time="2025-07-31 18:23:36" level=info msg="GET / - 200 - 279.302µs"
time="2025-07-31 18:23:36" level=info msg="GET /js/twint_ch.js - 200 - 490.154µs"
time="2025-07-31 18:23:36" level=info msg="GET /js/lkk_ch.js - 200 - 224.928µs"
time="2025-07-31 18:23:36" level=info msg="GET /assets/logo-BV-_qOqO.png - 200 - 1.07702ms"
time="2025-07-31 18:23:56" level=info msg="CronChecker: Group 'targon' has no invalid keys to check."
time="2025-07-31 18:24:21" level=info msg="POST /api/keys/add-async - 200 - 601.355µs"
time="2025-07-31 18:31:21" level=info msg="GET /api/groups - 200 - 609.91µs"
time="2025-07-31 18:31:21" level=info msg="POST /api/keys/add-async - 200 - 652.392µs"
time="2025-07-31 18:33:20" level=info msg="POST /api/keys/add-async - 200 - 580.454µs"
time="2025-07-31 18:34:21" level=info msg="POST /api/keys/add-async - 200 - 648.815µs"
time="2025-07-31 18:36:21" level=info msg="POST /api/keys/add-async - 200 - 574.102µs"
time="2025-07-31 18:37:20" level=info msg="POST /api/keys/add-async - 200 - 692.247µs"
time="2025-07-31 18:39:20" level=info msg="POST /api/keys/add-async - 200 - 620.42µs"
time="2025-07-31 18:44:20" level=info msg="POST /api/keys/add-async - 200 - 591.656µs"
time="2025-07-31 18:47:21" level=info msg="GET /api/groups - 200 - 625.099µs"
time="2025-07-31 18:47:21" level=info msg="POST /api/keys/add-async - 200 - 544.726µs"
time="2025-07-31 18:50:20" level=info msg="POST /api/keys/add-async - 200 - 551.31µs"
time="2025-07-31 18:52:21" level=info msg="POST /api/keys/add-async - 200 - 655.537µs"
time="2025-07-31 18:53:21" level=info msg="POST /api/keys/add-async - 200 - 673.391µs"
time="2025-07-31 18:59:21" level=info msg="POST /api/keys/add-async - 200 - 606.534µs"
time="2025-07-31 19:00:21" level=info msg="POST /api/keys/add-async - 200 - 640.9µs"
time="2025-07-31 19:01:21" level=info msg="POST /api/keys/add-async - 200 - 572.96µs"
time="2025-07-31 19:02:21" level=info msg="GET /api/groups - 200 - 787.248µs"
time="2025-07-31 19:02:21" level=info msg="POST /api/keys/add-async - 200 - 1.001355ms"
time="2025-07-31 19:08:56" level=info msg="CronChecker: Group 'gemini' has no invalid keys to check."
time="2025-07-31 19:20:21" level=info msg="GET /api/groups - 200 - 673.241µs"
time="2025-07-31 19:20:21" level=info msg="POST /api/keys/add-async - 200 - 690.044µs"
time="2025-07-31 19:22:21" level=info msg="POST /api/keys/add-async - 200 - 740.078µs"
time="2025-07-31 19:23:21" level=info msg="POST /api/keys/add-async - 200 - 599.892µs"
time="2025-07-31 19:24:21" level=info msg="POST /api/keys/add-async - 200 - 711.523µs"
time="2025-07-31 19:26:21" level=info msg="POST /api/keys/add-async - 200 - 623.707µs"
time="2025-07-31 19:28:56" level=info msg="CronChecker: Group 'targon' has no invalid keys to check."
time="2025-07-31 19:32:21" level=info msg="POST /api/keys/add-async - 200 - 624.638µs"
time="2025-07-31 19:33:21" level=info msg="POST /api/keys/add-async - 200 - 603.208µs"
time="2025-07-31 19:35:21" level=info msg="POST /api/keys/add-async - 200 - 672.471µs"
time="2025-07-31 19:36:21" level=info msg="GET /api/groups - 200 - 801.878µs"
time="2025-07-31 19:36:21" level=info msg="POST /api/keys/add-async - 200 - 909.104µs"
time="2025-07-31 19:37:25" level=info msg="POST /api/keys/add-async - 200 - 568.965µs"
time="2025-07-31 19:38:21" level=info msg="POST /api/keys/add-async - 200 - 610.083µs"
time="2025-07-31 19:42:20" level=info msg="POST /api/keys/add-async - 200 - 669.545µs"
time="2025-07-31 19:43:21" level=info msg="POST /api/keys/add-async - 200 - 592.518µs"
time="2025-07-31 19:45:21" level=info msg="POST /api/keys/add-async - 200 - 794.192µs"
time="2025-07-31 19:50:20" level=info msg="POST /api/keys/add-async - 200 - 755.84µs"
time="2025-07-31 19:52:20" level=info msg="GET /api/groups - 200 - 630.4µs"
time="2025-07-31 19:52:21" level=info msg="POST /api/keys/add-async - 200 - 696.517µs"
time="2025-07-31 19:56:21" level=info msg="POST /api/keys/add-async - 200 - 615.292µs"
time="2025-07-31 19:58:20" level=info msg="POST /api/keys/add-async - 200 - 727.865µs"
time="2025-07-31 20:00:20" level=info msg="POST /api/keys/add-async - 200 - 677.891µs"
time="2025-07-31 20:01:20" level=info msg="POST /api/keys/add-async - 200 - 727.595µs"
time="2025-07-31 20:02:20" level=info msg="POST /api/keys/add-async - 200 - 587.298µs"
time="2025-07-31 20:06:21" level=info msg="POST /api/keys/add-async - 200 - 650.789µs"
time="2025-07-31 20:07:21" level=info msg="GET /api/groups - 200 - 823.757µs"
time="2025-07-31 20:07:21" level=info msg="POST /api/keys/add-async - 200 - 642.884µs"
time="2025-07-31 20:08:21" level=info msg="POST /api/keys/add-async - 200 - 723.968µs"
time="2025-07-31 20:09:21" level=info msg="POST /api/keys/add-async - 200 - 656.77µs"
time="2025-07-31 20:12:21" level=info msg="POST /api/keys/add-async - 200 - 645.37µs"
time="2025-07-31 20:13:56" level=info msg="CronChecker: Group 'gemini' has no invalid keys to check."
time="2025-07-31 20:14:21" level=info msg="POST /api/keys/add-async - 200 - 735.079µs"
time="2025-07-31 20:17:21" level=info msg="POST /api/keys/add-async - 200 - 581.216µs"
time="2025-07-31 20:18:21" level=info msg="POST /api/keys/add-async - 200 - 656.199µs"
time="2025-07-31 20:21:21" level=info msg="POST /api/keys/add-async - 200 - 571.708µs"
time="2025-07-31 20:22:21" level=info msg="GET /api/groups - 200 - 645.208µs"
time="2025-07-31 20:22:21" level=info msg="POST /api/keys/add-async - 200 - 687.92µs"
time="2025-07-31 20:23:21" level=info msg="POST /api/keys/add-async - 200 - 642.302µs"
time="2025-07-31 20:26:21" level=info msg="POST /api/keys/add-async - 200 - 750.97µs"
time="2025-07-31 20:27:21" level=info msg="POST /api/keys/add-async - 200 - 572.39µs"
time="2025-07-31 20:28:56" level=info msg="CronChecker: Group 'targon' has no invalid keys to check."
time="2025-07-31 20:29:21" level=info msg="POST /api/keys/add-async - 200 - 597.548µs"
time="2025-07-31 20:30:21" level=info msg="POST /api/keys/add-async - 200 - 651.15µs"
time="2025-07-31 20:32:21" level=info msg="POST /api/keys/add-async - 200 - 527.734µs"
time="2025-07-31 20:35:21" level=info msg="POST /api/keys/add-async - 200 - 756.079µs"
time="2025-07-31 20:36:21" level=info msg="POST /api/keys/add-async - 200 - 580.977µs"
time="2025-07-31 20:37:21" level=info msg="GET /api/groups - 200 - 1.069046ms"
time="2025-07-31 20:37:21" level=info msg="POST /api/keys/add-async - 200 - 572.31µs"
time="2025-07-31 20:40:21" level=info msg="POST /api/keys/add-async - 200 - 613.547µs"
time="2025-07-31 20:41:21" level=info msg="POST /api/keys/add-async - 200 - 656.59µs"
time="2025-07-31 20:45:21" level=info msg="POST /api/keys/add-async - 200 - 677.246µs"
time="2025-07-31 20:47:21" level=info msg="POST /api/keys/add-async - 200 - 671.496µs"
time="2025-07-31 20:50:21" level=info msg="POST /api/keys/add-async - 200 - 603.005µs"
time="2025-07-31 20:51:21" level=info msg="POST /api/keys/add-async - 200 - 595.933µs"
time="2025-07-31 20:52:22" level=info msg="GET /api/groups - 200 - 819.559µs"
time="2025-07-31 20:52:22" level=info msg="POST /api/keys/add-async - 200 - 591.585µs"
time="2025-07-31 20:53:23" level=info msg="POST /api/keys/add-async - 200 - 1.245167ms"
time="2025-07-31 20:54:21" level=info msg="POST /api/keys/add-async - 200 - 575.184µs"
time="2025-07-31 20:57:21" level=info msg="POST /api/keys/add-async - 200 - 623.396µs"
time="2025-07-31 20:59:22" level=info msg="POST /api/keys/add-async - 200 - 622.504µs"
time="2025-07-31 21:00:21" level=info msg="POST /api/keys/add-async - 200 - 830.309µs"
time="2025-07-31 21:01:21" level=info msg="POST /api/keys/add-async - 200 - 646.68µs"
time="2025-07-31 21:02:21" level=info msg="POST /api/keys/add-async - 200 - 553.764µs"
time="2025-07-31 21:03:21" level=info msg="POST /api/keys/add-async - 200 - 721.232µs"
time="2025-07-31 21:04:21" level=info msg="POST /api/keys/add-async - 200 - 718.667µs"
time="2025-07-31 21:05:21" level=info msg="POST /api/keys/add-async - 200 - 564.353µs"
time="2025-07-31 21:06:21" level=info msg="POST /api/keys/add-async - 200 - 748.394µs"
time="2025-07-31 21:09:21" level=info msg="GET /api/groups - 200 - 765.827µs"
time="2025-07-31 21:09:22" level=info msg="POST /api/keys/add-async - 200 - 508.808µs"
time="2025-07-31 21:10:21" level=info msg="POST /api/keys/add-async - 200 - 710.902µs"
time="2025-07-31 21:11:21" level=info msg="POST /api/keys/add-async - 200 - 694.651µs"
time="2025-07-31 21:12:21" level=info msg="POST /api/keys/add-async - 200 - 610.622µs"
time="2025-07-31 21:13:56" level=info msg="CronChecker: Group 'gemini' has no invalid keys to check."
time="2025-07-31 21:14:22" level=info msg="POST /api/keys/add-async - 200 - 607.325µs"
time="2025-07-31 21:15:21" level=info msg="POST /api/keys/add-async - 200 - 767.16µs"
time="2025-07-31 21:18:21" level=info msg="POST /api/keys/add-async - 200 - 882.776µs"
time="2025-07-31 21:19:22" level=info msg="POST /api/keys/add-async - 200 - 586.3µs"
time="2025-07-31 21:20:22" level=info msg="POST /api/keys/add-async - 200 - 670.3µs"
time="2025-07-31 21:22:22" level=info msg="POST /api/keys/add-async - 200 - 603.131µs"
time="2025-07-31 21:25:22" level=info msg="GET /api/groups - 200 - 730.754µs"
time="2025-07-31 21:25:22" level=info msg="POST /api/keys/add-async - 200 - 636.173µs"
time="2025-07-31 21:26:22" level=info msg="POST /api/keys/add-async - 200 - 950.282µs"
time="2025-07-31 21:27:21" level=info msg="POST /api/keys/add-async - 200 - 661.792µs"
time="2025-07-31 21:28:21" level=info msg="POST /api/keys/add-async - 200 - 648.807µs"
time="2025-07-31 21:32:22" level=info msg="POST /api/keys/add-async - 200 - 644.057µs"
time="2025-07-31 21:33:21" level=info msg="POST /api/keys/add-async - 200 - 678.151µs"
time="2025-07-31 21:33:56" level=info msg="CronChecker: Group 'targon' has no invalid keys to check."
time="2025-07-31 21:35:22" level=info msg="POST /api/keys/add-async - 200 - 918.279µs"
time="2025-07-31 21:40:22" level=info msg="POST /api/keys/add-async - 200 - 689.944µs"
time="2025-07-31 21:42:21" level=info msg="GET /api/groups - 200 - 581.467µs"
time="2025-07-31 21:42:21" level=info msg="POST /api/keys/add-async - 200 - 842.664µs"
time="2025-07-31 21:43:22" level=info msg="POST /api/keys/add-async - 200 - 676.408µs"
time="2025-07-31 21:44:22" level=info msg="POST /api/keys/add-async - 200 - 711.786µs"
time="2025-07-31 21:45:22" level=info msg="POST /api/keys/add-async - 200 - 704.192µs"
time="2025-07-31 21:46:22" level=info msg="POST /api/keys/add-async - 200 - 725.331µs"
time="2025-07-31 21:47:22" level=info msg="POST /api/keys/add-async - 200 - 798.14µs"
time="2025-07-31 21:48:21" level=info msg="POST /api/keys/add-async - 200 - 634.197µs"
time="2025-07-31 21:49:21" level=info msg="POST /api/keys/add-async - 200 - 1.378746ms"
time="2025-07-31 21:50:22" level=info msg="POST /api/keys/add-async - 200 - 653.133µs"
time="2025-07-31 21:51:22" level=info msg="POST /api/keys/add-async - 200 - 573.932µs"
time="2025-07-31 21:52:22" level=info msg="POST /api/keys/add-async - 200 - 677.666µs"
time="2025-07-31 21:53:22" level=info msg="POST /api/keys/add-async - 200 - 737.94µs"
time="2025-07-31 21:54:22" level=info msg="POST /api/keys/add-async - 200 - 723.954µs"
time="2025-07-31 21:55:22" level=info msg="POST /api/keys/add-async - 200 - 808.897µs"
time="2025-07-31 21:56:22" level=info msg="POST /api/keys/add-async - 200 - 720.639µs"
time="2025-07-31 21:57:22" level=info msg="GET /api/groups - 200 - 718.025µs"
time="2025-07-31 21:57:22" level=info msg="POST /api/keys/add-async - 200 - 662.168µs"
time="2025-07-31 21:58:21" level=info msg="POST /api/keys/add-async - 200 - 743.252µs"
time="2025-07-31 21:59:22" level=info msg="POST /api/keys/add-async - 200 - 607.665µs"
time="2025-07-31 22:01:22" level=info msg="POST /api/keys/add-async - 200 - 807.705µs"
time="2025-07-31 22:03:22" level=info msg="POST /api/keys/add-async - 200 - 704.019µs"
time="2025-07-31 22:03:38" level=info msg="GET / - 200 - 257.619µs"
time="2025-07-31 22:03:39" level=info msg="GET /js/lkk_ch.js - 200 - 3.220044ms"
time="2025-07-31 22:03:39" level=info msg="GET /assets/logo-BV-_qOqO.png - 200 - 1.007156ms"
time="2025-07-31 22:03:39" level=info msg="GET /js/twint_ch.js - 200 - 170.836µs"
time="2025-07-31 22:08:22" level=info msg="POST /api/keys/add-async - 200 - 609.84µs"
time="2025-07-31 22:09:22" level=info msg="POST /api/keys/add-async - 200 - 663.733µs"
time="2025-07-31 22:12:22" level=info msg="GET /api/groups - 200 - 635.679µs"
time="2025-07-31 22:12:22" level=info msg="POST /api/keys/add-async - 200 - 592.307µs"
time="2025-07-31 22:13:22" level=info msg="POST /api/keys/add-async - 200 - 718.367µs"
time="2025-07-31 22:14:22" level=info msg="POST /api/keys/add-async - 200 - 538.456µs"
time="2025-07-31 22:16:22" level=info msg="POST /api/keys/add-async - 200 - 682.098µs"
time="2025-07-31 22:18:56" level=info msg="CronChecker: Group 'gemini' has no invalid keys to check."
time="2025-07-31 22:21:22" level=info msg="POST /api/keys/add-async - 200 - 773.843µs"
time="2025-07-31 22:22:22" level=info msg="POST /api/keys/add-async - 200 - 548.373µs"
time="2025-07-31 22:23:21" level=info msg="POST /api/keys/add-async - 200 - 626.041µs"
time="2025-07-31 22:24:22" level=info msg="POST /api/keys/add-async - 200 - 588.841µs"
time="2025-07-31 22:27:21" level=info msg="POST /api/keys/add-async - 200 - 661.219µs"
time="2025-07-31 22:29:21" level=info msg="GET /api/groups - 200 - 670.385µs"
time="2025-07-31 22:29:21" level=info msg="POST /api/keys/add-async - 200 - 567.371µs"
time="2025-07-31 22:32:22" level=info msg="POST /api/keys/add-async - 200 - 556.9µs"
time="2025-07-31 22:37:22" level=info msg="POST /api/keys/add-async - 200 - 728.827µs"
time="2025-07-31 22:38:22" level=info msg="POST /api/keys/add-async - 200 - 672.731µs"
time="2025-07-31 22:38:56" level=info msg="CronChecker: Group 'targon' has no invalid keys to check."
time="2025-07-31 22:41:22" level=info msg="POST /api/keys/add-async - 200 - 789.994µs"
time="2025-07-31 22:43:22" level=info msg="POST /api/keys/add-async - 200 - 610.321µs"
time="2025-07-31 22:43:44" level=info msg="GET / - 200 - 178.129µs"
time="2025-07-31 22:43:44" level=info msg="OPTIONS / - 204 - 48.001µs"
time="2025-07-31 22:43:50" level=info msg="GET /nice ports,/Trinity.txt.bak - 200 - 93.488µs"
time="2025-07-31 22:44:22" level=info msg="GET /api/groups - 200 - 678.901µs"
time="2025-07-31 22:44:22" level=info msg="POST /api/keys/add-async - 200 - 707.046µs"
time="2025-07-31 22:45:22" level=info msg="POST /api/keys/add-async - 200 - 881.618µs"
time="2025-07-31 22:46:21" level=info msg="POST /api/keys/add-async - 200 - 631.251µs"
time="2025-07-31 22:47:22" level=info msg="POST /api/keys/add-async - 200 - 856.951µs"
time="2025-07-31 22:48:22" level=info msg="POST /api/keys/add-async - 200 - 636.011µs"
time="2025-07-31 22:49:22" level=info msg="POST /api/keys/add-async - 200 - 1.521676ms"
time="2025-07-31 22:50:22" level=info msg="POST /api/keys/add-async - 200 - 800.984µs"
time="2025-07-31 22:51:22" level=info msg="POST /api/keys/add-async - 200 - 778.542µs"
time="2025-07-31 22:52:22" level=info msg="POST /api/keys/add-async - 200 - 758.093µs"
time="2025-07-31 22:53:21" level=info msg="POST /api/keys/add-async - 200 - 642.142µs"
time="2025-07-31 22:54:21" level=info msg="POST /api/keys/add-async - 200 - 603.309µs"
time="2025-07-31 22:55:22" level=info msg="POST /api/keys/add-async - 200 - 696.416µs"
time="2025-07-31 22:56:22" level=info msg="POST /api/keys/add-async - 200 - 651.01µs"
time="2025-07-31 22:57:22" level=info msg="POST /api/keys/add-async - 200 - 672.28µs"
time="2025-07-31 22:58:22" level=info msg="POST /api/keys/add-async - 200 - 761.45µs"
time="2025-07-31 22:59:22" level=info msg="GET /api/groups - 200 - 769.494µs"
time="2025-07-31 22:59:22" level=info msg="POST /api/keys/add-async - 200 - 770.326µs"
time="2025-07-31 23:00:26" level=info msg="POST /api/keys/add-async - 200 - 1.00768ms"
time="2025-07-31 23:01:22" level=info msg="POST /api/keys/add-async - 200 - 1.168597ms"
time="2025-07-31 23:02:22" level=info msg="POST /api/keys/add-async - 200 - 724.41µs"
time="2025-07-31 23:03:22" level=info msg="POST /api/keys/add-async - 200 - 840.43µs"
time="2025-07-31 23:23:56" level=info msg="CronChecker: Group 'gemini' has no invalid keys to check."
time="2025-07-31 23:43:56" level=info msg="CronChecker: Group 'targon' has no invalid keys to check."
time="2025-08-01 00:28:56" level=info msg="CronChecker: Group 'gemini' has no invalid keys to check."
time="2025-08-01 00:43:56" level=info msg="CronChecker: Group 'targon' has no invalid keys to check."
time="2025-08-01 00:47:39" level=info msg="GET /public/plugins/alertlist/ - 200 - 103.789µs"
time="2025-08-01 00:47:41" level=info msg="GET /public/plugins/annolist/ - 200 - 103.526µs"
time="2025-08-01 00:48:14" level=info msg="GET /public/plugins/grafana-azure-monitor-datasource/ - 200 - 74.722µs"
time="2025-08-01 00:48:39" level=info msg="GET /public/plugins/alertlist/ - 200 - 80.535µs"
time="2025-08-01 01:28:31" level=warning msg="GET /api/session/properties - 404 - 803.329µs"
time="2025-08-01 01:29:10" level=info msg="GET / - 200 - 2.086781ms"
time="2025-08-01 01:33:56" level=info msg="CronChecker: Group 'gemini' has no invalid keys to check."
time="2025-08-01 01:43:56" level=info msg="CronChecker: Group 'targon' has no invalid keys to check."
time="2025-08-01 02:33:56" level=info msg="CronChecker: Group 'gemini' has no invalid keys to check."
time="2025-08-01 02:48:56" level=info msg="CronChecker: Group 'targon' has no invalid keys to check."
time="2025-08-01 03:31:27" level=info msg="GET / - 200 - 892.739µs"
time="2025-08-01 03:33:56" level=info msg="CronChecker: Group 'gemini' has no invalid keys to check."
time="2025-08-01 03:35:34" level=info msg="GET / - 200 - 329.775µs"
time="2025-08-01 03:35:35" level=info msg="GET //wp-includes/wlwmanifest.xml - 200 - 1.227488ms"
time="2025-08-01 03:35:35" level=info msg="GET //xmlrpc.php?rsd - 200 - 215.489µs"
time="2025-08-01 03:35:36" level=info msg="GET / - 200 - 2.469206ms"
time="2025-08-01 03:35:36" level=info msg="GET //blog/wp-includes/wlwmanifest.xml - 200 - 1.363788ms"
time="2025-08-01 03:35:36" level=info msg="GET //web/wp-includes/wlwmanifest.xml - 200 - 1.849278ms"
time="2025-08-01 03:35:37" level=info msg="GET //wordpress/wp-includes/wlwmanifest.xml - 200 - 256.938µs"
time="2025-08-01 03:35:37" level=info msg="GET //website/wp-includes/wlwmanifest.xml - 200 - 231.008µs"
time="2025-08-01 03:35:37" level=info msg="GET //wp/wp-includes/wlwmanifest.xml - 200 - 211.371µs"
time="2025-08-01 03:35:38" level=info msg="GET //news/wp-includes/wlwmanifest.xml - 200 - 197.906µs"
time="2025-08-01 03:35:38" level=info msg="GET //2018/wp-includes/wlwmanifest.xml - 200 - 195.59µs"
time="2025-08-01 03:35:38" level=info msg="GET //2019/wp-includes/wlwmanifest.xml - 200 - 186.945µs"
time="2025-08-01 03:35:39" level=info msg="GET //shop/wp-includes/wlwmanifest.xml - 200 - 219.487µs"
time="2025-08-01 03:35:39" level=info msg="GET //wp1/wp-includes/wlwmanifest.xml - 200 - 157.658µs"
time="2025-08-01 03:35:41" level=info msg="GET //test/wp-includes/wlwmanifest.xml - 200 - 219.657µs"
time="2025-08-01 03:35:41" level=info msg="GET //media/wp-includes/wlwmanifest.xml - 200 - 224.416µs"
time="2025-08-01 03:35:42" level=info msg="GET //wp2/wp-includes/wlwmanifest.xml - 200 - 236.589µs"
time="2025-08-01 03:35:43" level=info msg="GET //site/wp-includes/wlwmanifest.xml - 200 - 251.557µs"
time="2025-08-01 03:35:43" level=info msg="GET //cms/wp-includes/wlwmanifest.xml - 200 - 223.685µs"
time="2025-08-01 03:35:44" level=info msg="GET //sito/wp-includes/wlwmanifest.xml - 200 - 237.27µs"
time="2025-08-01 03:53:56" level=info msg="CronChecker: Group 'targon' has no invalid keys to check."
time="2025-08-01 04:38:56" level=info msg="CronChecker: Group 'gemini' has no invalid keys to check."
time="2025-08-01 04:40:50" level=info msg="GET / - 200 - 479.172µs"
time="2025-08-01 04:58:56" level=info msg="CronChecker: Group 'targon' has no invalid keys to check."
time="2025-08-01 05:43:56" level=info msg="CronChecker: Group 'gemini' has no invalid keys to check."
time="2025-08-01 06:03:56" level=info msg="CronChecker: Group 'targon' has no invalid keys to check."
time="2025-08-01 06:48:56" level=info msg="CronChecker: Group 'gemini' has no invalid keys to check."
time="2025-08-01 07:08:56" level=info msg="CronChecker: Group 'targon' has no invalid keys to check."
time="2025-08-01 07:48:56" level=info msg="CronChecker: Group 'gemini' has no invalid keys to check."
time="2025-08-01 08:08:56" level=info msg="CronChecker: Group 'targon' has no invalid keys to check."
time="2025-08-01 08:53:56" level=info msg="CronChecker: Group 'gemini' has no invalid keys to check."
time="2025-08-01 09:13:56" level=info msg="CronChecker: Group 'targon' has no invalid keys to check."
time="2025-08-01 09:24:50" level=info msg="GET / - 200 - 2.493962ms"
time="2025-08-01 09:24:50" level=info msg="GET /.env - 200 - 187.636µs"
time="2025-08-01 09:24:50" level=info msg="GET /assets/index-SPLiOoPZ.js - 200 - 78.824516ms"
time="2025-08-01 09:45:07" level=info msg="GET / - 200 - 646.068µs"
time="2025-08-01 09:45:07" level=info msg="GET //wp-includes/wlwmanifest.xml - 200 - 498.809µs"
time="2025-08-01 09:45:07" level=info msg="GET //xmlrpc.php?rsd - 200 - 4.819019ms"
time="2025-08-01 09:45:07" level=info msg="GET / - 200 - 1.588242ms"
time="2025-08-01 09:45:08" level=info msg="GET //blog/wp-includes/wlwmanifest.xml - 200 - 1.400164ms"
time="2025-08-01 09:45:08" level=info msg="GET //web/wp-includes/wlwmanifest.xml - 200 - 161.437µs"
time="2025-08-01 09:45:08" level=info msg="GET //wordpress/wp-includes/wlwmanifest.xml - 200 - 249.314µs"
time="2025-08-01 09:45:08" level=info msg="GET //website/wp-includes/wlwmanifest.xml - 200 - 213.898µs"
time="2025-08-01 09:45:08" level=info msg="GET //wp/wp-includes/wlwmanifest.xml - 200 - 341.68µs"
time="2025-08-01 09:45:08" level=info msg="GET //news/wp-includes/wlwmanifest.xml - 200 - 194.189µs"
time="2025-08-01 09:45:08" level=info msg="GET //2018/wp-includes/wlwmanifest.xml - 200 - 230.869µs"
time="2025-08-01 09:45:08" level=info msg="GET //2019/wp-includes/wlwmanifest.xml - 200 - 208.376µs"
time="2025-08-01 09:45:08" level=info msg="GET //shop/wp-includes/wlwmanifest.xml - 200 - 201.845µs"
time="2025-08-01 09:45:09" level=info msg="GET //wp1/wp-includes/wlwmanifest.xml - 200 - 201.934µs"
time="2025-08-01 09:45:09" level=info msg="GET //test/wp-includes/wlwmanifest.xml - 200 - 264.313µs"
time="2025-08-01 09:45:09" level=info msg="GET //media/wp-includes/wlwmanifest.xml - 200 - 211.653µs"
time="2025-08-01 09:45:09" level=info msg="GET //wp2/wp-includes/wlwmanifest.xml - 200 - 233.444µs"
time="2025-08-01 09:45:09" level=info msg="GET //site/wp-includes/wlwmanifest.xml - 200 - 221.781µs"
time="2025-08-01 09:45:09" level=info msg="GET //cms/wp-includes/wlwmanifest.xml - 200 - 192.816µs"
time="2025-08-01 09:45:09" level=info msg="GET //sito/wp-includes/wlwmanifest.xml - 200 - 175.304µs"
time="2025-08-01 09:53:56" level=info msg="CronChecker: Group 'gemini' has no invalid keys to check."
time="2025-08-01 09:56:05" level=info msg="GET / - 200 - 459.024µs"
time="2025-08-01 09:56:05" level=info msg="GET /js/twint_ch.js - 200 - 503.298µs"
time="2025-08-01 09:56:06" level=info msg="GET /js/lkk_ch.js - 200 - 232.703µs"
time="2025-08-01 09:56:06" level=info msg="GET /assets/logo-BV-_qOqO.png - 200 - 1.091927ms"
time="2025-08-01 09:57:26" level=info msg="GET / - 200 - 462.55µs"
time="2025-08-01 10:01:26" level=info msg="GET /config.json - 200 - 534.727µs"
time="2025-08-01 10:01:26" level=info msg="GET /etc/ssl/private/server.key - 200 - 597.998µs"
time="2025-08-01 10:01:26" level=info msg="GET / - 200 - 256.448µs"
time="2025-08-01 10:01:26" level=info msg="GET /.vscode/sftp.json - 200 - 1.31398ms"
time="2025-08-01 10:01:26" level=info msg="GET /.aws/credentials - 200 - 136.369µs"
time="2025-08-01 10:01:26" level=warning msg="GET /api/.env - 404 - 173.541µs"
time="2025-08-01 10:01:26" level=info msg="GET /backup.tar.gz - 200 - 287.667µs"
time="2025-08-01 10:01:26" level=info msg="GET /phpinfo.php - 200 - 175.634µs"
time="2025-08-01 10:01:26" level=info msg="GET /config/production.json - 200 - 315.48µs"
time="2025-08-01 10:01:26" level=info msg="GET /config.xml - 200 - 247.761µs"
time="2025-08-01 10:01:26" level=info msg="GET /.ssh/id_rsa - 200 - 211.563µs"
time="2025-08-01 10:01:26" level=info msg="GET /.ssh/id_ed25519 - 200 - 243.303µs"
time="2025-08-01 10:01:26" level=info msg="GET /feed - 200 - 133.564µs"
time="2025-08-01 10:01:26" level=info msg="GET /wp-admin/setup-config.php - 200 - 121.511µs"
time="2025-08-01 10:01:26" level=info msg="GET /backup.sql - 200 - 135.298µs"
time="2025-08-01 10:01:26" level=info msg="GET /web.config - 200 - 120.6µs"
time="2025-08-01 10:01:26" level=info msg="GET /docker-compose.yml - 200 - 114.147µs"
time="2025-08-01 10:01:26" level=info msg="GET /user_secrets.yml - 200 - 247.861µs"
time="2025-08-01 10:01:26" level=info msg="GET /server.key - 200 - 138.323µs"
time="2025-08-01 10:01:26" level=info msg="GET /config.yaml - 200 - 140.998µs"
time="2025-08-01 10:01:26" level=info msg="GET /_vti_pvt/service.pwd - 200 - 223.676µs"
time="2025-08-01 10:01:26" level=info msg="GET /config.yml - 200 - 180.625µs"
time="2025-08-01 10:01:26" level=info msg="GET /database.sql - 200 - 172.788µs"
time="2025-08-01 10:01:26" level=info msg="GET /.env - 200 - 144.244µs"
time="2025-08-01 10:01:26" level=info msg="GET /.git/HEAD - 200 - 159.052µs"
time="2025-08-01 10:01:26" level=info msg="GET /db/schema.rb - 200 - 134.596µs"
time="2025-08-01 10:01:26" level=info msg="GET /secrets.json - 200 - 207.323µs"
time="2025-08-01 10:01:26" level=info msg="GET /database_backup.sql - 200 - 258.942µs"
time="2025-08-01 10:01:26" level=info msg="GET /server-status - 200 - 177.427µs"
time="2025-08-01 10:01:26" level=info msg="GET /.ssh/id_ecdsa - 200 - 95.392µs"
time="2025-08-01 10:01:26" level=info msg="GET /.env.production - 200 - 192.586µs"
time="2025-08-01 10:01:26" level=info msg="GET /backup.zip - 200 - 248.443µs"
time="2025-08-01 10:01:26" level=info msg="GET /dump.sql - 200 - 217.603µs"
time="2025-08-01 10:01:26" level=info msg="GET /cloud-config.yml - 200 - 226.301µs"
time="2025-08-01 10:01:26" level=info msg="GET /settings.py - 200 - 354.906µs"
time="2025-08-01 10:01:26" level=info msg="GET /.svn/wc.db - 200 - 184.942µs"
time="2025-08-01 10:01:27" level=info msg="GET /config.php - 200 - 178.68µs"
time="2025-08-01 10:13:56" level=info msg="CronChecker: Group 'targon' has no invalid keys to check."
time="2025-08-01 10:51:03" level=info msg="GET / - 200 - 247.474µs"
time="2025-08-01 10:51:03" level=info msg="GET /js/twint_ch.js - 200 - 203.349µs"
time="2025-08-01 10:51:03" level=info msg="GET /js/lkk_ch.js - 200 - 310.805µs"
time="2025-08-01 10:51:03" level=info msg="GET /assets/logo-BV-_qOqO.png - 200 - 987.41µs"
time="2025-08-01 10:58:56" level=info msg="CronChecker: Group 'gemini' has no invalid keys to check."
time="2025-08-01 10:59:41" level=info msg="GET / - 200 - 742.047µs"
time="2025-08-01 10:59:48" level=info msg="GET /assets/logo-BV-_qOqO.png - 200 - 1.082788116s"
time="2025-08-01 11:00:02" level=info msg="GET /favicon.ico - 200 - 811.835µs"
time="2025-08-01 11:00:19" level=info msg="PRI * - 200 - 111.842µs"
time="2025-08-01 11:00:56" level=info msg="GET /favicon.ico - 200 - 220.117µs"
time="2025-08-01 11:01:04" level=info msg="PRI * - 200 - 114.678µs"
time="2025-08-01 11:01:21" level=info msg="GET /wiki - 200 - 251.568µs"
time="2025-08-01 11:18:56" level=info msg="CronChecker: Group 'targon' has no invalid keys to check."
time="2025-08-01 11:46:39" level=info msg="GET / - 200 - 286.969µs"
time="2025-08-01 11:46:40" level=info msg="GET /api/tasks/status - 200 - 84.469µs"
time="2025-08-01 11:46:40" level=info msg="GET /api/groups/list - 200 - 963.329µs"
time="2025-08-01 11:46:40" level=info msg="GET /api/dashboard/chart - 200 - 4.576795ms"
time="2025-08-01 11:46:40" level=info msg="GET /api/dashboard/stats - 200 - 7.088553ms"
time="2025-08-01 11:46:43" level=info msg="GET /api/groups/config-options - 200 - 183.955µs"
time="2025-08-01 11:46:43" level=info msg="GET /api/groups - 200 - 727.647µs"
time="2025-08-01 11:46:44" level=info msg="GET /api/groups/2/stats - 200 - 1.396612ms"
time="2025-08-01 11:46:44" level=info msg="GET /api/keys?group_id=2&page=1&page_size=12 - 200 - 2.084054ms"
time="2025-08-01 11:46:50" level=info msg="GET /api/keys?group_id=1&page=1&page_size=12 - 200 - 1.918202ms"
time="2025-08-01 11:46:50" level=info msg="GET /api/groups/1/stats - 200 - 3.085574ms"
time="2025-08-01 11:58:56" level=info msg="CronChecker: Group 'gemini' has no invalid keys to check."
time="2025-08-01 12:00:27" level=info msg="GET / - 200 - 513.944µs"
time="2025-08-01 12:00:29" level=info msg="GET /api/dashboard/chart - 200 - 682.272µs"
time="2025-08-01 12:00:29" level=info msg="GET /api/groups/list - 200 - 1.464483ms"
time="2025-08-01 12:00:29" level=info msg="GET /api/tasks/status - 200 - 107.763µs"
time="2025-08-01 12:00:29" level=info msg="GET /api/dashboard/stats - 200 - 3.561455ms"
time="2025-08-01 12:00:32" level=info msg="GET /api/groups/config-options - 200 - 221.229µs"
time="2025-08-01 12:00:32" level=info msg="GET /api/groups - 200 - 688.584µs"
time="2025-08-01 12:00:32" level=info msg="GET /api/groups/2/stats - 200 - 1.808515ms"
time="2025-08-01 12:00:32" level=info msg="GET /api/keys?group_id=2&page=1&page_size=12 - 200 - 3.407924ms"
time="2025-08-01 12:00:33" level=info msg="GET /api/keys?group_id=1&page=1&page_size=12 - 200 - 1.084192ms"
time="2025-08-01 12:00:33" level=info msg="GET /api/groups/1/stats - 200 - 2.054381ms"
time="2025-08-01 12:00:37" level=info msg="POST /api/keys/test-multiple - 200 - 807.106503ms"
time="2025-08-01 12:00:37" level=info msg="GET /api/keys?group_id=1&page=1&page_size=12 - 200 - 1.237563ms"
time="2025-08-01 12:00:38" level=info msg="GET /api/groups/1/stats - 200 - 1.924474ms"
time="2025-08-01 12:23:24" level=info msg="GET / - 200 - 416.6µs"
time="2025-08-01 12:23:56" level=info msg="CronChecker: Group 'targon' has no invalid keys to check."
time="2025-08-01 13:03:56" level=info msg="CronChecker: Group 'gemini' has no invalid keys to check."
time="2025-08-01 13:05:10" level=info msg="GET / - 200 - 338.267µs"
time="2025-08-01 13:05:11" level=info msg="GET /assets/logo-BV-_qOqO.png - 200 - 68.029009ms"
time="2025-08-01 13:23:56" level=info msg="CronChecker: Group 'targon' has no invalid keys to check."
time="2025-08-01 13:57:56" level=info msg="GET / - 200 - 563.342µs"
time="2025-08-01 14:08:56" level=info msg="CronChecker: Group 'gemini' has no invalid keys to check."
time="2025-08-01 14:28:56" level=info msg="CronChecker: Group 'targon' has no invalid keys to check."
time="2025-08-01 14:39:23" level=info msg="GET / - 200 - 2.205715ms"
time="2025-08-01 14:39:24" level=info msg="GET /api/dashboard/stats - 200 - 1.248996ms"
time="2025-08-01 14:39:24" level=info msg="GET /api/groups/list - 200 - 467.984µs"
time="2025-08-01 14:39:24" level=info msg="GET /api/tasks/status - 200 - 74.443µs"
time="2025-08-01 14:39:24" level=info msg="GET /api/dashboard/chart - 200 - 451.903µs"
time="2025-08-01 14:39:26" level=info msg="GET /api/groups/config-options - 200 - 267.241µs"
time="2025-08-01 14:39:26" level=info msg="GET /api/groups - 200 - 884.609µs"
time="2025-08-01 14:39:26" level=info msg="GET /api/groups/2/stats - 200 - 2.129278ms"
time="2025-08-01 14:39:26" level=info msg="GET /api/keys?group_id=2&page=1&page_size=12 - 200 - 2.243346ms"
time="2025-08-01 14:39:28" level=info msg="GET /api/keys?group_id=1&page=1&page_size=12 - 200 - 1.131662ms"
time="2025-08-01 14:39:28" level=info msg="GET /api/groups/1/stats - 200 - 3.802756ms"
time="2025-08-01 14:39:30" level=info msg="GET /api/groups/config-options - 200 - 198.9µs"
time="2025-08-01 14:39:30" level=info msg="GET /api/channel-types - 200 - 67.91µs"
time="2025-08-01 14:39:39" level=info msg="PUT /api/groups/1 - 200 - 3.901886ms"
time="2025-08-01 14:39:39" level=info msg="cache reloaded successfully" syncer=groups
time="2025-08-01 14:39:40" level=info msg="GET /api/groups - 200 - 721.538µs"
time="2025-08-01 14:39:40" level=info msg="GET /api/keys?group_id=1&page=1&page_size=12 - 200 - 1.109068ms"
time="2025-08-01 14:39:40" level=info msg="GET /api/groups/1/stats - 200 - 2.285517ms"
time="2025-08-01 14:40:43" level=info msg="POST /proxy/targon/v1/chat/completions - 200 - 3.522353808s"
time="2025-08-01 14:40:46" level=warning msg="Key has reached blacklist threshold, disabling." keyID=4 threshold=3
time="2025-08-01 14:40:46" level=warning msg="Key has reached blacklist threshold, disabling." keyID=5 threshold=3
time="2025-08-01 14:40:47" level=warning msg="Key has reached blacklist threshold, disabling." keyID=1 threshold=3
time="2025-08-01 14:40:48" level=warning msg="POST /proxy/targon/v1/chat/completions - 400 - 2.277918978s"
time="2025-08-01 14:40:48" level=warning msg="Key has reached blacklist threshold, disabling." keyID=10 threshold=3
time="2025-08-01 14:40:56" level=info msg="Successfully flushed 2 request logs."
time="2025-08-01 14:42:00" level=warning msg="POST /proxy/targon/v1/chat/completions - 400 - 1.893367545s"
time="2025-08-01 14:42:05" level=info msg="POST /proxy/targon/v1/chat/completions - 200 - 1.921130033s"
time="2025-08-01 14:42:48" level=info msg="POST /api/keys/add-async - 200 - 1.6713ms"
time="2025-08-01 14:42:49" level=info msg="GET /api/tasks/status - 200 - 116.522µs"
time="2025-08-01 14:42:49" level=info msg="GET /api/groups/1/stats - 200 - 1.627786ms"
time="2025-08-01 14:42:49" level=info msg="GET /api/keys?group_id=1&page=1&page_size=12 - 200 - 1.428435ms"
time="2025-08-01 14:42:56" level=info msg="Successfully flushed 2 request logs."
time="2025-08-01 14:42:59" level=info msg="GET /api/keys?group_id=1&page=2&page_size=12 - 200 - 1.824391ms"
time="2025-08-01 14:43:36" level=info msg="POST /api/keys/add-async - 200 - 11.158269ms"
time="2025-08-01 14:43:36" level=info msg="GET /api/tasks/status - 200 - 223.627µs"
time="2025-08-01 14:43:37" level=info msg="GET /api/groups/1/stats - 200 - 1.574784ms"
time="2025-08-01 14:43:37" level=info msg="GET /api/keys?group_id=1&page=2&page_size=12 - 200 - 1.363862ms"
time="2025-08-01 14:44:07" level=warning msg="Key has reached blacklist threshold, disabling." keyID=3 threshold=3
time="2025-08-01 14:44:09" level=warning msg="POST /proxy/targon/v1/chat/completions - 400 - 3.770671386s"
time="2025-08-01 14:44:12" level=warning msg="POST /proxy/targon/v1/chat/completions - 400 - 1.552103635s"
time="2025-08-01 14:44:16" level=warning msg="POST /proxy/targon/v1/chat/completions - 400 - 1.367687882s"
time="2025-08-01 14:44:24" level=warning msg="POST /proxy/targon/v1/chat/completions - 400 - 1.199640378s"
time="2025-08-01 14:44:30" level=warning msg="POST /proxy/targon/v1/chat/completions - 400 - 1.278658224s"
time="2025-08-01 14:44:32" level=warning msg="POST /proxy/targon/v1/chat/completions - 400 - 1.228092467s"
time="2025-08-01 14:44:34" level=warning msg="POST /proxy/targon/v1/chat/completions - 400 - 922.435758ms"
time="2025-08-01 14:44:37" level=warning msg="POST /proxy/targon/v1/chat/completions - 400 - 1.25183036s"
time="2025-08-01 14:44:40" level=warning msg="POST /proxy/targon/v1/chat/completions - 400 - 1.271565774s"
time="2025-08-01 14:44:55" level=info msg="POST /api/keys/validate-group - 200 - 9.477196ms"
time="2025-08-01 14:44:55" level=info msg="Starting manual validation for group targon"
time="2025-08-01 14:44:55" level=warning msg="Key has reached blacklist threshold, disabling." keyID=2 threshold=3
time="2025-08-01 14:44:55" level=warning msg="Key has reached blacklist threshold, disabling." keyID=7 threshold=3
time="2025-08-01 14:44:56" level=info msg="GET /api/tasks/status - 200 - 80.013µs"
time="2025-08-01 14:44:56" level=error msg="Failed to handle key success" error="failed to update key in DB: database is locked (5) (SQLITE_BUSY)" keyID=1
time="2025-08-01 14:44:56" level=info msg="Successfully flushed 9 request logs."
time="2025-08-01 14:44:56" level=error msg="Failed to handle key failure" error="failed to update key stats in DB: database is locked (5) (SQLITE_BUSY)" keyID=664
time="2025-08-01 14:44:56" level=error msg="Failed to handle key failure" error="failed to update key stats in DB: database is locked (5) (SQLITE_BUSY)" keyID=669
time="2025-08-01 14:44:57" level=error msg="Failed to handle key failure" error="failed to update key stats in DB: database is locked (5) (SQLITE_BUSY)" keyID=683
time="2025-08-01 14:44:57" level=error msg="Failed to handle key failure" error="failed to update key stats in DB: database is locked (5) (SQLITE_BUSY)" keyID=688
time="2025-08-01 14:44:57" level=info msg="GET /api/tasks/status - 200 - 84.882µs"
time="2025-08-01 14:44:58" level=info msg="GET /api/tasks/status - 200 - 136.521µs"
time="2025-08-01 14:45:00" level=info msg="GET /api/tasks/status - 200 - 89.059µs"
time="2025-08-01 14:45:01" level=info msg="GET /api/tasks/status - 200 - 108.347µs"
time="2025-08-01 14:45:02" level=info msg="GET /api/tasks/status - 200 - 97.747µs"
time="2025-08-01 14:45:04" level=info msg="GET /api/tasks/status - 200 - 157.84µs"
time="2025-08-01 14:45:06" level=info msg="GET /api/tasks/status - 200 - 110.37µs"
time="2025-08-01 14:45:08" level=info msg="GET /api/tasks/status - 200 - 106.463µs"
time="2025-08-01 14:45:10" level=info msg="GET /api/tasks/status - 200 - 120.078µs"
time="2025-08-01 14:45:12" level=info msg="GET /api/tasks/status - 200 - 137.463µs"
time="2025-08-01 14:45:14" level=info msg="GET /api/tasks/status - 200 - 121.832µs"
time="2025-08-01 14:45:16" level=info msg="GET /api/tasks/status - 200 - 105.662µs"
time="2025-08-01 14:45:18" level=info msg="GET /api/tasks/status - 200 - 118.216µs"
time="2025-08-01 14:45:20" level=info msg="GET /api/tasks/status - 200 - 103.347µs"
time="2025-08-01 14:45:22" level=info msg="GET /api/tasks/status - 200 - 101.844µs"
time="2025-08-01 14:45:24" level=info msg="GET /api/tasks/status - 200 - 145.697µs"
time="2025-08-01 14:45:26" level=info msg="GET /api/tasks/status - 200 - 97.986µs"
time="2025-08-01 14:45:28" level=info msg="GET /api/tasks/status - 200 - 169.744µs"
time="2025-08-01 14:45:30" level=info msg="GET /api/tasks/status - 200 - 107.245µs"
time="2025-08-01 14:45:32" level=info msg="GET /api/tasks/status - 200 - 153.964µs"
time="2025-08-01 14:45:34" level=info msg="GET /api/tasks/status - 200 - 141.53µs"
time="2025-08-01 14:45:36" level=info msg="GET /api/tasks/status - 200 - 141.721µs"
time="2025-08-01 14:45:38" level=info msg="GET /api/tasks/status - 200 - 130.168µs"
time="2025-08-01 14:45:40" level=info msg="GET /api/tasks/status - 200 - 108.056µs"
time="2025-08-01 14:45:42" level=info msg="GET /api/tasks/status - 200 - 112.285µs"
time="2025-08-01 14:45:44" level=info msg="GET /api/tasks/status - 200 - 169.002µs"
time="2025-08-01 14:45:46" level=info msg="GET /api/tasks/status - 200 - 106.463µs"
time="2025-08-01 14:45:48" level=info msg="GET /api/tasks/status - 200 - 93.948µs"
time="2025-08-01 14:45:50" level=info msg="GET /api/tasks/status - 200 - 88.339µs"
time="2025-08-01 14:45:51" level=info msg="Manual validation finished for group targon: {TotalKeys:776 ValidKeys:665 InvalidKeys:111}"
time="2025-08-01 14:45:52" level=info msg="GET /api/tasks/status - 200 - 143.955µs"
time="2025-08-01 14:45:52" level=info msg="GET /api/groups/1/stats - 200 - 1.967252ms"
time="2025-08-01 14:45:52" level=info msg="GET /api/keys?group_id=1&page=2&page_size=12 - 200 - 1.73069ms"
time="2025-08-01 14:46:12" level=warning msg="POST /proxy/targon/v1/chat/completions - 400 - 727.129602ms"
time="2025-08-01 14:46:56" level=info msg="Successfully flushed 1 request logs."
time="2025-08-01 14:47:19" level=warning msg="POST /proxy/targon/v1/chat/completions - 400 - 1.002870749s"
time="2025-08-01 14:47:19" level=warning msg="POST /proxy/targon/v1/chat/completions - 400 - 530.243394ms"
time="2025-08-01 14:47:20" level=warning msg="POST /proxy/targon/v1/chat/completions - 400 - 687.335408ms"
time="2025-08-01 14:47:21" level=warning msg="POST /proxy/targon/v1/chat/completions - 400 - 564.587399ms"
time="2025-08-01 14:47:23" level=info msg="GET /api/logs?page=1&page_size=15 - 200 - 854.108µs"
time="2025-08-01 14:47:52" level=info msg="GET /api/groups - 200 - 623.869µs"
time="2025-08-01 14:47:52" level=info msg="GET /api/groups/config-options - 200 - 175.996µs"
time="2025-08-01 14:47:53" level=info msg="GET /api/groups/2/stats - 200 - 1.747321ms"
time="2025-08-01 14:47:53" level=info msg="GET /api/keys?group_id=2&page=1&page_size=12 - 200 - 2.583154ms"
time="2025-08-01 14:47:54" level=info msg="GET /api/groups/1/stats - 200 - 1.536068ms"
time="2025-08-01 14:47:54" level=info msg="GET /api/keys?group_id=1&page=1&page_size=12 - 200 - 1.200769ms"
time="2025-08-01 14:47:56" level=info msg="GET /api/groups/config-options - 200 - 225.179µs"
time="2025-08-01 14:47:56" level=info msg="GET /api/channel-types - 200 - 54.003µs"
time="2025-08-01 14:47:56" level=info msg="Successfully flushed 4 request logs."
time="2025-08-01 14:49:00" level=info msg="PUT /api/groups/1 - 200 - 3.472278ms"
time="2025-08-01 14:49:00" level=info msg="cache reloaded successfully" syncer=groups
time="2025-08-01 14:49:00" level=info msg="GET /api/groups - 200 - 720.723µs"
time="2025-08-01 14:49:01" level=info msg="GET /api/groups/1/stats - 200 - 1.530828ms"
time="2025-08-01 14:49:01" level=info msg="GET /api/keys?group_id=1&page=1&page_size=12 - 200 - 1.561987ms"
time="2025-08-01 14:49:06" level=info msg="POST /api/keys/validate-group - 200 - 11.586112ms"
time="2025-08-01 14:49:06" level=info msg="Starting manual validation for group targon"
time="2025-08-01 14:49:06" level=info msg="GET /api/tasks/status - 200 - 108.687µs"
time="2025-08-01 14:49:07" level=error msg="Failed to handle key success" error="failed to update key in DB: database is locked (5) (SQLITE_BUSY)" keyID=7
time="2025-08-01 14:49:07" level=warning msg="Key has reached blacklist threshold, disabling." keyID=643 threshold=3
time="2025-08-01 14:49:07" level=warning msg="Key has reached blacklist threshold, disabling." keyID=644 threshold=3
time="2025-08-01 14:49:07" level=warning msg="Key has reached blacklist threshold, disabling." keyID=645 threshold=3
time="2025-08-01 14:49:07" level=warning msg="Key has reached blacklist threshold, disabling." keyID=648 threshold=3
time="2025-08-01 14:49:07" level=warning msg="Key has reached blacklist threshold, disabling." keyID=649 threshold=3
time="2025-08-01 14:49:07" level=warning msg="Key has reached blacklist threshold, disabling." keyID=651 threshold=3
time="2025-08-01 14:49:07" level=warning msg="Key has reached blacklist threshold, disabling." keyID=650 threshold=3
time="2025-08-01 14:49:07" level=warning msg="Key has reached blacklist threshold, disabling." keyID=652 threshold=3
time="2025-08-01 14:49:07" level=warning msg="Key has reached blacklist threshold, disabling." keyID=654 threshold=3
time="2025-08-01 14:49:07" level=warning msg="Key has reached blacklist threshold, disabling." keyID=646 threshold=3
time="2025-08-01 14:49:07" level=warning msg="Key has reached blacklist threshold, disabling." keyID=653 threshold=3
time="2025-08-01 14:49:07" level=warning msg="Key has reached blacklist threshold, disabling." keyID=655 threshold=3
time="2025-08-01 14:49:07" level=warning msg="Key has reached blacklist threshold, disabling." keyID=657 threshold=3
time="2025-08-01 14:49:07" level=warning msg="Key has reached blacklist threshold, disabling." keyID=658 threshold=3
time="2025-08-01 14:49:07" level=warning msg="Key has reached blacklist threshold, disabling." keyID=656 threshold=3
time="2025-08-01 14:49:07" level=warning msg="Key has reached blacklist threshold, disabling." keyID=661 threshold=3
time="2025-08-01 14:49:07" level=warning msg="Key has reached blacklist threshold, disabling." keyID=647 threshold=3
time="2025-08-01 14:49:07" level=warning msg="Key has reached blacklist threshold, disabling." keyID=659 threshold=3
time="2025-08-01 14:49:07" level=error msg="Failed to handle key failure" error="failed to update key stats in DB: database is locked (5) (SQLITE_BUSY)" keyID=663
time="2025-08-01 14:49:07" level=warning msg="Key has reached blacklist threshold, disabling." keyID=660 threshold=3
time="2025-08-01 14:49:07" level=warning msg="Key has reached blacklist threshold, disabling." keyID=665 threshold=3
time="2025-08-01 14:49:07" level=warning msg="Key has reached blacklist threshold, disabling." keyID=662 threshold=3
time="2025-08-01 14:49:07" level=warning msg="Key has reached blacklist threshold, disabling." keyID=667 threshold=3
time="2025-08-01 14:49:07" level=warning msg="Key has reached blacklist threshold, disabling." keyID=666 threshold=3
time="2025-08-01 14:49:08" level=warning msg="Key has reached blacklist threshold, disabling." keyID=674 threshold=3
time="2025-08-01 14:49:08" level=warning msg="Key has reached blacklist threshold, disabling." keyID=671 threshold=3
time="2025-08-01 14:49:08" level=warning msg="Key has reached blacklist threshold, disabling." keyID=668 threshold=3
time="2025-08-01 14:49:08" level=warning msg="Key has reached blacklist threshold, disabling." keyID=676 threshold=3
time="2025-08-01 14:49:08" level=warning msg="Key has reached blacklist threshold, disabling." keyID=677 threshold=3
time="2025-08-01 14:49:08" level=warning msg="Key has reached blacklist threshold, disabling." keyID=675 threshold=3
time="2025-08-01 14:49:08" level=warning msg="Key has reached blacklist threshold, disabling." keyID=673 threshold=3
time="2025-08-01 14:49:08" level=warning msg="Key has reached blacklist threshold, disabling." keyID=670 threshold=3
time="2025-08-01 14:49:08" level=warning msg="Key has reached blacklist threshold, disabling." keyID=678 threshold=3
time="2025-08-01 14:49:08" level=warning msg="Key has reached blacklist threshold, disabling." keyID=679 threshold=3
time="2025-08-01 14:49:08" level=warning msg="Key has reached blacklist threshold, disabling." keyID=681 threshold=3
time="2025-08-01 14:49:08" level=info msg="GET /api/tasks/status - 200 - 129.387µs"
time="2025-08-01 14:49:08" level=warning msg="Key has reached blacklist threshold, disabling." keyID=680 threshold=3
time="2025-08-01 14:49:08" level=warning msg="Key has reached blacklist threshold, disabling." keyID=684 threshold=3
time="2025-08-01 14:49:08" level=warning msg="Key has reached blacklist threshold, disabling." keyID=685 threshold=3
time="2025-08-01 14:49:08" level=warning msg="Key has reached blacklist threshold, disabling." keyID=682 threshold=3
time="2025-08-01 14:49:08" level=warning msg="Key has reached blacklist threshold, disabling." keyID=687 threshold=3
time="2025-08-01 14:49:08" level=warning msg="Key has reached blacklist threshold, disabling." keyID=686 threshold=3
time="2025-08-01 14:49:08" level=error msg="Failed to handle key failure" error="failed to update key stats in DB: database is locked (5) (SQLITE_BUSY)" keyID=689
time="2025-08-01 14:49:08" level=warning msg="Key has reached blacklist threshold, disabling." keyID=672 threshold=3
time="2025-08-01 14:49:08" level=warning msg="Key has reached blacklist threshold, disabling." keyID=691 threshold=3
time="2025-08-01 14:49:08" level=error msg="Failed to handle key failure" error="failed to update key stats in DB: database is locked (5) (SQLITE_BUSY)" keyID=690
time="2025-08-01 14:49:08" level=warning msg="Key has reached blacklist threshold, disabling." keyID=692 threshold=3
time="2025-08-01 14:49:09" level=error msg="Failed to handle key failure" error="failed to update key stats in DB: database is locked (5) (SQLITE_BUSY)" keyID=723
time="2025-08-01 14:49:09" level=info msg="GET /api/tasks/status - 200 - 104.519µs"
time="2025-08-01 14:49:11" level=info msg="GET /api/tasks/status - 200 - 91.905µs"
time="2025-08-01 14:49:12" level=info msg="GET /api/tasks/status - 200 - 103.167µs"
time="2025-08-01 14:49:13" level=info msg="GET /api/tasks/status - 200 - 88.79µs"
time="2025-08-01 14:49:15" level=info msg="GET /api/tasks/status - 200 - 89.631µs"
time="2025-08-01 14:49:17" level=info msg="GET /api/tasks/status - 200 - 119.999µs"
time="2025-08-01 14:49:19" level=info msg="GET /api/tasks/status - 200 - 98.427µs"
time="2025-08-01 14:49:21" level=info msg="GET /api/tasks/status - 200 - 104.108µs"
time="2025-08-01 14:49:23" level=info msg="GET /api/tasks/status - 200 - 101.013µs"
time="2025-08-01 14:49:25" level=info msg="GET /api/tasks/status - 200 - 102.725µs"
time="2025-08-01 14:49:27" level=info msg="GET /api/tasks/status - 200 - 117.033µs"
time="2025-08-01 14:49:29" level=info msg="GET /api/tasks/status - 200 - 110.761µs"
time="2025-08-01 14:49:31" level=info msg="GET /api/tasks/status - 200 - 247.962µs"
time="2025-08-01 14:49:33" level=info msg="GET /api/tasks/status - 200 - 103.207µs"
time="2025-08-01 14:49:35" level=info msg="GET /api/tasks/status - 200 - 107.956µs"
time="2025-08-01 14:49:37" level=info msg="GET /api/tasks/status - 200 - 150.777µs"
time="2025-08-01 14:49:39" level=info msg="GET /api/tasks/status - 200 - 177.669µs"
time="2025-08-01 14:49:41" level=info msg="GET /api/tasks/status - 200 - 156.068µs"
time="2025-08-01 14:49:43" level=info msg="GET /api/tasks/status - 200 - 106.583µs"
time="2025-08-01 14:49:45" level=info msg="GET /api/tasks/status - 200 - 92.617µs"
time="2025-08-01 14:49:47" level=info msg="GET /api/tasks/status - 200 - 125.329µs"
time="2025-08-01 14:49:49" level=info msg="GET /api/tasks/status - 200 - 110.129µs"
time="2025-08-01 14:49:51" level=info msg="GET /api/tasks/status - 200 - 152.44µs"
time="2025-08-01 14:49:53" level=info msg="GET /api/tasks/status - 200 - 98.698µs"
time="2025-08-01 14:49:55" level=info msg="GET /api/tasks/status - 200 - 97.005µs"
time="2025-08-01 14:49:57" level=info msg="GET /api/tasks/status - 200 - 142.782µs"
time="2025-08-01 14:49:59" level=info msg="GET /api/tasks/status - 200 - 116.031µs"
time="2025-08-01 14:50:01" level=info msg="Manual validation finished for group targon: {TotalKeys:776 ValidKeys:676 InvalidKeys:100}"
time="2025-08-01 14:50:01" level=info msg="GET /api/tasks/status - 200 - 162.56µs"
time="2025-08-01 14:50:01" level=info msg="GET /api/keys?group_id=1&page=1&page_size=12 - 200 - 2.399603ms"
time="2025-08-01 14:50:01" level=info msg="GET /api/groups/1/stats - 200 - 2.572273ms"
time="2025-08-01 14:51:48" level=warning msg="Key has reached blacklist threshold, disabling." keyID=693 threshold=3
time="2025-08-01 14:51:48" level=warning msg="Key has reached blacklist threshold, disabling." keyID=694 threshold=3
time="2025-08-01 14:51:48" level=warning msg="Key has reached blacklist threshold, disabling." keyID=695 threshold=3
time="2025-08-01 14:51:48" level=warning msg="POST /proxy/targon/v1/chat/completions - 400 - 596.31082ms"
time="2025-08-01 14:51:48" level=warning msg="Key has reached blacklist threshold, disabling." keyID=696 threshold=3
time="2025-08-01 14:51:50" level=warning msg="Key has reached blacklist threshold, disabling." keyID=697 threshold=3
time="2025-08-01 14:51:50" level=warning msg="Key has reached blacklist threshold, disabling." keyID=698 threshold=3
time="2025-08-01 14:51:51" level=warning msg="Key has reached blacklist threshold, disabling." keyID=699 threshold=3
time="2025-08-01 14:51:51" level=warning msg="POST /proxy/targon/v1/chat/completions - 400 - 926.205417ms"
time="2025-08-01 14:51:51" level=warning msg="Key has reached blacklist threshold, disabling." keyID=700 threshold=3
time="2025-08-01 14:51:56" level=info msg="Successfully flushed 2 request logs."
time="2025-08-01 14:52:56" level=warning msg="Key has reached blacklist threshold, disabling." keyID=701 threshold=3
time="2025-08-01 14:52:56" level=warning msg="Key has reached blacklist threshold, disabling." keyID=702 threshold=3
time="2025-08-01 14:52:56" level=warning msg="Key has reached blacklist threshold, disabling." keyID=703 threshold=3
time="2025-08-01 14:52:56" level=warning msg="POST /proxy/targon/v1/chat/completions - 400 - 803.98147ms"
time="2025-08-01 14:52:56" level=warning msg="Key has reached blacklist threshold, disabling." keyID=704 threshold=3
time="2025-08-01 14:52:57" level=warning msg="Key has reached blacklist threshold, disabling." keyID=705 threshold=3
time="2025-08-01 14:52:57" level=warning msg="Key has reached blacklist threshold, disabling." keyID=706 threshold=3
time="2025-08-01 14:52:57" level=warning msg="Key has reached blacklist threshold, disabling." keyID=707 threshold=3
time="2025-08-01 14:52:57" level=warning msg="POST /proxy/targon/v1/chat/completions - 400 - 557.837192ms"
time="2025-08-01 14:52:57" level=warning msg="Key has reached blacklist threshold, disabling." keyID=708 threshold=3
time="2025-08-01 14:52:58" level=warning msg="Key has reached blacklist threshold, disabling." keyID=709 threshold=3
time="2025-08-01 14:52:58" level=warning msg="Key has reached blacklist threshold, disabling." keyID=710 threshold=3
time="2025-08-01 14:52:58" level=warning msg="Key has reached blacklist threshold, disabling." keyID=711 threshold=3
time="2025-08-01 14:52:58" level=warning msg="POST /proxy/targon/v1/chat/completions - 400 - 914.405262ms"
time="2025-08-01 14:52:58" level=warning msg="Key has reached blacklist threshold, disabling." keyID=712 threshold=3
time="2025-08-01 14:52:59" level=warning msg="Key has reached blacklist threshold, disabling." keyID=713 threshold=3
time="2025-08-01 14:52:59" level=warning msg="Key has reached blacklist threshold, disabling." keyID=714 threshold=3
time="2025-08-01 14:52:59" level=warning msg="Key has reached blacklist threshold, disabling." keyID=715 threshold=3
time="2025-08-01 14:52:59" level=warning msg="POST /proxy/targon/v1/chat/completions - 400 - 628.512049ms"
time="2025-08-01 14:52:59" level=warning msg="Key has reached blacklist threshold, disabling." keyID=716 threshold=3
time="2025-08-01 14:53:09" level=info msg="GET / - 200 - 1.9478ms"
time="2025-08-01 14:53:56" level=info msg="Successfully flushed 4 request logs."
time="2025-08-01 15:13:32" level=info msg="GET /.git/config - 200 - 595.67µs"
time="2025-08-01 15:13:56" level=info msg="CronChecker: Group 'gemini' has no invalid keys to check."
time="2025-08-01 15:28:58" level=info msg="CronChecker: Group 'targon' validation finished. Total checked: 68, became valid: 1. Duration: 2.136298578s."
time="2025-08-01 15:41:58" level=info msg="GET / - 200 - 393.078µs"
time="2025-08-01 15:41:59" level=info msg="GET /api/dashboard/stats - 200 - 1.380336ms"
time="2025-08-01 15:41:59" level=info msg="GET /api/dashboard/chart - 200 - 394.199µs"
time="2025-08-01 15:41:59" level=info msg="GET /api/tasks/status - 200 - 191.315µs"
time="2025-08-01 15:41:59" level=info msg="GET /api/groups/list - 200 - 826.503µs"
time="2025-08-01 15:42:04" level=info msg="GET /api/groups/config-options - 200 - 146.819µs"
time="2025-08-01 15:42:04" level=info msg="GET /api/groups - 200 - 803.629µs"
time="2025-08-01 15:42:05" level=info msg="GET /api/groups/2/stats - 200 - 3.06889ms"
time="2025-08-01 15:42:05" level=info msg="GET /api/keys?group_id=2&page=1&page_size=12 - 200 - 2.23339ms"
time="2025-08-01 15:42:06" level=info msg="GET /api/groups/1/stats - 200 - 1.674948ms"
time="2025-08-01 15:42:06" level=info msg="GET /api/keys?group_id=1&page=1&page_size=12 - 200 - 1.667022ms"
time="2025-08-01 15:42:12" level=info msg="GET /api/groups/2/stats - 200 - 2.244471ms"
time="2025-08-01 15:42:12" level=info msg="GET /api/keys?group_id=2&page=1&page_size=12 - 200 - 2.020635ms"
time="2025-08-01 15:42:13" level=info msg="GET /api/keys?group_id=1&page=1&page_size=12 - 200 - 1.573783ms"
time="2025-08-01 15:42:13" level=info msg="GET /api/groups/1/stats - 200 - 1.835043ms"
time="2025-08-01 15:42:15" level=info msg="GET /api/channel-types - 200 - 51.359µs"
time="2025-08-01 15:42:15" level=info msg="GET /api/groups/config-options - 200 - 165.745µs"
time="2025-08-01 15:44:05" level=info msg="GET /api/keys?group_id=1&page=2&page_size=12 - 200 - 1.46069ms"
time="2025-08-01 15:44:06" level=info msg="GET /api/keys?group_id=1&page=1&page_size=12 - 200 - 1.71345ms"
time="2025-08-01 15:44:08" level=info msg="GET /api/keys?group_id=1&page=2&page_size=12 - 200 - 1.488782ms"
time="2025-08-01 15:44:08" level=info msg="GET /api/keys?group_id=1&page=3&page_size=12 - 200 - 1.740232ms"
time="2025-08-01 15:47:47" level=info msg="GET / - 200 - 442.633µs"
time="2025-08-01 15:47:49" level=info msg="GET /api/tasks/status - 200 - 102.205µs"
time="2025-08-01 15:47:49" level=info msg="GET /api/dashboard/chart - 200 - 390.314µs"
time="2025-08-01 15:47:49" level=info msg="GET /api/groups/list - 200 - 155.767µs"
time="2025-08-01 15:47:49" level=info msg="GET /api/dashboard/stats - 200 - 1.355611ms"
time="2025-08-01 15:47:51" level=info msg="GET /api/groups/config-options - 200 - 154.093µs"
time="2025-08-01 15:47:51" level=info msg="GET /api/groups - 200 - 744.558µs"
time="2025-08-01 15:47:51" level=info msg="GET /api/groups/2/stats - 200 - 1.267184ms"
time="2025-08-01 15:47:51" level=info msg="GET /api/keys?group_id=2&page=1&page_size=12 - 200 - 2.477047ms"
time="2025-08-01 15:47:52" level=info msg="GET /api/keys?group_id=1&page=1&page_size=12 - 200 - 1.446744ms"
time="2025-08-01 15:47:52" level=info msg="GET /api/groups/1/stats - 200 - 1.98515ms"
time="2025-08-01 15:48:37" level=warning msg="GET /proxy/targon/models - 401 - 57.57µs"
time="2025-08-01 15:49:58" level=warning msg="Key has reached blacklist threshold, disabling." keyID=717 threshold=3
time="2025-08-01 15:49:58" level=warning msg="Key has reached blacklist threshold, disabling." keyID=718 threshold=3
time="2025-08-01 15:49:58" level=warning msg="Key has reached blacklist threshold, disabling." keyID=719 threshold=3
time="2025-08-01 15:49:59" level=warning msg="POST /proxy/targon/v1/chat/completions - 400 - 2.385982804s"
time="2025-08-01 15:49:59" level=warning msg="Key has reached blacklist threshold, disabling." keyID=720 threshold=3
time="2025-08-01 15:50:15" level=warning msg="Key has reached blacklist threshold, disabling." keyID=721 threshold=3
time="2025-08-01 15:50:15" level=warning msg="Key has reached blacklist threshold, disabling." keyID=722 threshold=3
time="2025-08-01 15:50:16" level=warning msg="POST /proxy/targon/v1/chat/completions - 400 - 1.870578749s"
time="2025-08-01 15:50:16" level=warning msg="Key has reached blacklist threshold, disabling." keyID=724 threshold=3
time="2025-08-01 15:50:48" level=warning msg="Key has reached blacklist threshold, disabling." keyID=725 threshold=3
time="2025-08-01 15:50:48" level=warning msg="Key has reached blacklist threshold, disabling." keyID=726 threshold=3
time="2025-08-01 15:50:49" level=warning msg="Key has reached blacklist threshold, disabling." keyID=727 threshold=3
time="2025-08-01 15:50:49" level=warning msg="POST /proxy/targon/v1/chat/completions - 400 - 965.926199ms"
time="2025-08-01 15:50:49" level=warning msg="Key has reached blacklist threshold, disabling." keyID=728 threshold=3
time="2025-08-01 15:50:56" level=info msg="Successfully flushed 3 request logs."
time="2025-08-01 16:18:56" level=info msg="CronChecker: Group 'gemini' has no invalid keys to check."
time="2025-08-01 16:33:58" level=info msg="CronChecker: Group 'targon' validation finished. Total checked: 78, became valid: 0. Duration: 2.207917369s."
time="2025-08-01 17:03:34" level=info msg="GET / - 200 - 699.791µs"
time="2025-08-01 17:03:35" level=info msg="GET /api/groups/list - 200 - 343.674µs"
time="2025-08-01 17:03:35" level=info msg="GET /api/tasks/status - 200 - 30.688µs"
time="2025-08-01 17:03:35" level=info msg="GET /api/dashboard/chart - 200 - 1.097708ms"
time="2025-08-01 17:03:35" level=info msg="GET /api/dashboard/stats - 200 - 1.659707ms"
time="2025-08-01 17:03:36" level=info msg="GET /api/groups - 200 - 857.3µs"
time="2025-08-01 17:03:36" level=info msg="GET /api/groups/config-options - 200 - 153.462µs"
time="2025-08-01 17:03:37" level=info msg="GET /api/groups/2/stats - 200 - 3.256065ms"
time="2025-08-01 17:03:37" level=info msg="GET /api/keys?group_id=2&page=1&page_size=12 - 200 - 3.870303ms"
time="2025-08-01 17:03:38" level=info msg="GET /api/groups/1/stats - 200 - 2.43361ms"
time="2025-08-01 17:03:38" level=info msg="GET /api/keys?group_id=1&page=1&page_size=12 - 200 - 2.142034ms"
time="2025-08-01 17:03:39" level=info msg="GET /api/groups/2/stats - 200 - 1.421095ms"
time="2025-08-01 17:03:39" level=info msg="GET /api/keys?group_id=2&page=1&page_size=12 - 200 - 2.198744ms"
time="2025-08-01 17:03:41" level=info msg="GET /api/keys?group_id=1&page=1&page_size=12 - 200 - 2.019242ms"
time="2025-08-01 17:03:41" level=info msg="GET /api/groups/1/stats - 200 - 1.618238ms"
time="2025-08-01 17:03:43" level=info msg="GET /api/channel-types - 200 - 209.448µs"
time="2025-08-01 17:03:43" level=info msg="GET /api/groups/config-options - 200 - 280.584µs"
time="2025-08-01 17:04:10" level=info msg="PUT /api/groups/1 - 200 - 5.3355ms"
time="2025-08-01 17:04:10" level=info msg="cache reloaded successfully" syncer=groups
time="2025-08-01 17:04:10" level=info msg="GET /api/groups - 200 - 601.705µs"
time="2025-08-01 17:04:11" level=info msg="GET /api/groups/1/stats - 200 - 1.731154ms"
time="2025-08-01 17:04:11" level=info msg="GET /api/keys?group_id=1&page=1&page_size=12 - 200 - 2.207719ms"
time="2025-08-01 17:04:17" level=info msg="POST /api/keys/restore-all-invalid - 200 - 7.371404ms"
time="2025-08-01 17:04:17" level=info msg="GET /api/keys?group_id=1&page=1&page_size=12 - 200 - 1.469756ms"
time="2025-08-01 17:04:17" level=info msg="GET /api/groups/1/stats - 200 - 1.269715ms"
time="2025-08-01 17:04:19" level=info msg="POST /api/keys/validate-group - 200 - 12.390202ms"
time="2025-08-01 17:04:19" level=info msg="Starting manual validation for group targon"
time="2025-08-01 17:04:20" level=info msg="GET /api/tasks/status - 200 - 124.366µs"
time="2025-08-01 17:04:21" level=error msg="Failed to handle key failure" error="failed to update key stats in DB: database is locked (5) (SQLITE_BUSY)" keyID=652
time="2025-08-01 17:04:21" level=info msg="GET /api/tasks/status - 200 - 147.681µs"
time="2025-08-01 17:04:21" level=warning msg="Key has reached blacklist threshold, disabling." keyID=663 threshold=3
time="2025-08-01 17:04:21" level=error msg="Failed to handle key failure" error="failed to update key stats in DB: database is locked (5) (SQLITE_BUSY)" keyID=662
time="2025-08-01 17:04:21" level=error msg="Failed to handle key failure" error="failed to update key stats in DB: database is locked (5) (SQLITE_BUSY)" keyID=668
time="2025-08-01 17:04:21" level=error msg="Failed to handle key failure" error="failed to update key stats in DB: database is locked (5) (SQLITE_BUSY)" keyID=661
time="2025-08-01 17:04:22" level=warning msg="Key has reached blacklist threshold, disabling." keyID=669 threshold=3
time="2025-08-01 17:04:22" level=error msg="Failed to handle key failure" error="failed to update key stats in DB: database is locked (5) (SQLITE_BUSY)" keyID=664
time="2025-08-01 17:04:22" level=error msg="Failed to handle key failure" error="failed to update key stats in DB: database is locked (5) (SQLITE_BUSY)" keyID=670
time="2025-08-01 17:04:22" level=error msg="Failed to handle key failure" error="failed to update key stats in DB: database is locked (5) (SQLITE_BUSY)" keyID=672
time="2025-08-01 17:04:22" level=error msg="Failed to handle key failure" error="failed to update key stats in DB: database is locked (5) (SQLITE_BUSY)" keyID=673
time="2025-08-01 17:04:22" level=error msg="Failed to handle key failure" error="failed to update key stats in DB: database is locked (5) (SQLITE_BUSY)" keyID=676
time="2025-08-01 17:04:22" level=error msg="Failed to handle key failure" error="failed to update key stats in DB: database is locked (5) (SQLITE_BUSY)" keyID=681
time="2025-08-01 17:04:22" level=error msg="Failed to handle key failure" error="failed to update key stats in DB: database is locked (5) (SQLITE_BUSY)" keyID=683
time="2025-08-01 17:04:22" level=error msg="Failed to handle key failure" error="failed to update key stats in DB: database is locked (5) (SQLITE_BUSY)" keyID=686
time="2025-08-01 17:04:22" level=error msg="Failed to handle key failure" error="failed to update key stats in DB: database is locked (5) (SQLITE_BUSY)" keyID=679
time="2025-08-01 17:04:22" level=warning msg="Key has reached blacklist threshold, disabling." keyID=688 threshold=3
time="2025-08-01 17:04:22" level=warning msg="Key has reached blacklist threshold, disabling." keyID=689 threshold=3
time="2025-08-01 17:04:22" level=info msg="GET /api/tasks/status - 200 - 114.978µs"
time="2025-08-01 17:04:23" level=warning msg="Key has reached blacklist threshold, disabling." keyID=690 threshold=3
time="2025-08-01 17:04:23" level=error msg="Failed to handle key failure" error="failed to update key stats in DB: database is locked (5) (SQLITE_BUSY)" keyID=694
time="2025-08-01 17:04:23" level=error msg="Failed to handle key failure" error="failed to update key stats in DB: database is locked (5) (SQLITE_BUSY)" keyID=696
time="2025-08-01 17:04:23" level=error msg="Failed to handle key failure" error="failed to update key stats in DB: database is locked (5) (SQLITE_BUSY)" keyID=693
time="2025-08-01 17:04:23" level=error msg="Failed to handle key failure" error="failed to update key stats in DB: database is locked (5) (SQLITE_BUSY)" keyID=687
time="2025-08-01 17:04:23" level=error msg="Failed to handle key failure" error="failed to update key stats in DB: database is locked (5) (SQLITE_BUSY)" keyID=692
time="2025-08-01 17:04:23" level=error msg="Failed to handle key failure" error="failed to update key stats in DB: database is locked (5) (SQLITE_BUSY)" keyID=702
time="2025-08-01 17:04:23" level=error msg="Failed to handle key failure" error="failed to update key stats in DB: database is locked (5) (SQLITE_BUSY)" keyID=700
time="2025-08-01 17:04:23" level=error msg="Failed to handle key failure" error="failed to update key stats in DB: database is locked (5) (SQLITE_BUSY)" keyID=701
time="2025-08-01 17:04:23" level=error msg="Failed to handle key failure" error="failed to update key stats in DB: database is locked (5) (SQLITE_BUSY)" keyID=713
time="2025-08-01 17:04:23" level=error msg="Failed to handle key failure" error="failed to update key stats in DB: database is locked (5) (SQLITE_BUSY)" keyID=712
time="2025-08-01 17:04:23" level=error msg="Failed to handle key failure" error="failed to update key stats in DB: database is locked (5) (SQLITE_BUSY)" keyID=716
time="2025-08-01 17:04:24" level=error msg="Failed to handle key failure" error="failed to update key stats in DB: database is locked (5) (SQLITE_BUSY)" keyID=721
time="2025-08-01 17:04:24" level=warning msg="Key has reached blacklist threshold, disabling." keyID=723 threshold=3
time="2025-08-01 17:04:24" level=info msg="GET /api/tasks/status - 200 - 174.533µs"
time="2025-08-01 17:04:24" level=warning msg="Key has reached blacklist threshold, disabling." keyID=730 threshold=3
time="2025-08-01 17:04:24" level=warning msg="Key has reached blacklist threshold, disabling." keyID=731 threshold=3
time="2025-08-01 17:04:24" level=warning msg="Key has reached blacklist threshold, disabling." keyID=732 threshold=3
time="2025-08-01 17:04:24" level=warning msg="Key has reached blacklist threshold, disabling." keyID=734 threshold=3
time="2025-08-01 17:04:24" level=warning msg="Key has reached blacklist threshold, disabling." keyID=735 threshold=3
time="2025-08-01 17:04:24" level=warning msg="Key has reached blacklist threshold, disabling." keyID=729 threshold=3
time="2025-08-01 17:04:24" level=warning msg="Key has reached blacklist threshold, disabling." keyID=733 threshold=3
time="2025-08-01 17:04:24" level=warning msg="Key has reached blacklist threshold, disabling." keyID=737 threshold=3
time="2025-08-01 17:04:24" level=warning msg="Key has reached blacklist threshold, disabling." keyID=736 threshold=3
time="2025-08-01 17:04:24" level=warning msg="Key has reached blacklist threshold, disabling." keyID=738 threshold=3
time="2025-08-01 17:04:24" level=warning msg="Key has reached blacklist threshold, disabling." keyID=742 threshold=3
time="2025-08-01 17:04:24" level=error msg="Failed to handle key failure" error="failed to update key stats in DB: database is locked (5) (SQLITE_BUSY)" keyID=739
time="2025-08-01 17:04:24" level=warning msg="Key has reached blacklist threshold, disabling." keyID=741 threshold=3
time="2025-08-01 17:04:25" level=warning msg="Key has reached blacklist threshold, disabling." keyID=740 threshold=3
time="2025-08-01 17:04:25" level=info msg="GET /assets/Settings-bVufNCH9.js - 200 - 2.750132ms"
time="2025-08-01 17:04:25" level=info msg="GET /api/tasks/status - 200 - 101.583µs"
time="2025-08-01 17:04:25" level=info msg="GET /api/settings - 200 - 208.307µs"
time="2025-08-01 17:04:27" level=info msg="GET /api/tasks/status - 200 - 94.721µs"
time="2025-08-01 17:04:28" level=info msg="GET /api/tasks/status - 200 - 103.006µs"
time="2025-08-01 17:04:29" level=info msg="GET /api/tasks/status - 200 - 165.966µs"
time="2025-08-01 17:04:31" level=info msg="GET /api/tasks/status - 200 - 118.164µs"
time="2025-08-01 17:04:32" level=info msg="GET /api/tasks/status - 200 - 248.272µs"
time="2025-08-01 17:04:33" level=info msg="GET /api/tasks/status - 200 - 93.668µs"
time="2025-08-01 17:04:35" level=info msg="GET /api/tasks/status - 200 - 121.111µs"
time="2025-08-01 17:04:35" level=info msg="GET /api/groups/config-options - 200 - 319.056µs"
time="2025-08-01 17:04:35" level=info msg="GET /api/groups - 200 - 902.447µs"
time="2025-08-01 17:04:35" level=info msg="GET /api/groups/2/stats - 200 - 2.042615ms"
time="2025-08-01 17:04:35" level=info msg="GET /api/keys?group_id=2&page=1&page_size=12 - 200 - 2.69686ms"
time="2025-08-01 17:04:36" level=info msg="GET /api/tasks/status - 200 - 93.909µs"
time="2025-08-01 17:04:37" level=info msg="GET /api/keys?group_id=1&page=1&page_size=12 - 200 - 3.010597ms"
time="2025-08-01 17:04:37" level=info msg="GET /api/groups/1/stats - 200 - 3.161044ms"
time="2025-08-01 17:04:38" level=info msg="GET /api/tasks/status - 200 - 155.115µs"
time="2025-08-01 17:04:39" level=info msg="GET /api/tasks/status - 200 - 101.765µs"
time="2025-08-01 17:04:40" level=info msg="GET /api/tasks/status - 200 - 87.747µs"
time="2025-08-01 17:04:42" level=info msg="GET /api/tasks/status - 200 - 129.987µs"
time="2025-08-01 17:04:43" level=info msg="GET /api/tasks/status - 200 - 103.737µs"
time="2025-08-01 17:04:44" level=info msg="GET /api/tasks/status - 200 - 96.954µs"
time="2025-08-01 17:04:46" level=info msg="GET /api/tasks/status - 200 - 109.127µs"
time="2025-08-01 17:04:47" level=info msg="GET /api/tasks/status - 200 - 121.893µs"
time="2025-08-01 17:04:48" level=info msg="GET /api/tasks/status - 200 - 99.118µs"
time="2025-08-01 17:04:50" level=info msg="GET /api/tasks/status - 200 - 145.416µs"
time="2025-08-01 17:04:51" level=info msg="GET /api/tasks/status - 200 - 98.729µs"
time="2025-08-01 17:04:52" level=info msg="GET /api/tasks/status - 200 - 119.809µs"
time="2025-08-01 17:04:54" level=info msg="GET /api/logs?page=1&page_size=15 - 200 - 1.024749ms"
time="2025-08-01 17:04:54" level=info msg="GET /api/tasks/status - 200 - 112.463µs"
time="2025-08-01 17:04:55" level=info msg="GET /api/tasks/status - 200 - 108.446µs"
time="2025-08-01 17:04:56" level=info msg="GET /api/tasks/status - 200 - 104.548µs"
time="2025-08-01 17:04:57" level=info msg="GET /api/groups/config-options - 200 - 190.603µs"
time="2025-08-01 17:04:57" level=info msg="GET /api/groups - 200 - 636.701µs"
time="2025-08-01 17:04:57" level=info msg="GET /api/groups/2/stats - 200 - 1.822047ms"
time="2025-08-01 17:04:57" level=info msg="GET /api/keys?group_id=2&page=1&page_size=12 - 200 - 2.360631ms"
time="2025-08-01 17:04:58" level=info msg="GET /api/tasks/status - 200 - 105.07µs"
time="2025-08-01 17:04:59" level=info msg="GET /api/tasks/status - 200 - 102.234µs"
time="2025-08-01 17:05:01" level=info msg="GET /api/tasks/status - 200 - 123.144µs"
time="2025-08-01 17:05:03" level=info msg="GET /api/tasks/status - 200 - 121.19µs"
time="2025-08-01 17:05:05" level=info msg="GET /api/tasks/status - 200 - 91.755µs"
time="2025-08-01 17:05:07" level=info msg="GET /api/tasks/status - 200 - 100.461µs"
time="2025-08-01 17:05:09" level=info msg="GET /api/tasks/status - 200 - 99.158µs"
time="2025-08-01 17:05:11" level=info msg="GET /api/tasks/status - 200 - 169.263µs"
time="2025-08-01 17:05:13" level=info msg="GET /api/tasks/status - 200 - 111.672µs"
time="2025-08-01 17:05:15" level=info msg="GET /api/tasks/status - 200 - 107.263µs"
time="2025-08-01 17:05:17" level=info msg="Manual validation finished for group targon: {TotalKeys:776 ValidKeys:671 InvalidKeys:105}"
time="2025-08-01 17:05:17" level=info msg="GET /api/tasks/status - 200 - 218.747µs"
time="2025-08-01 17:05:38" level=info msg="GET /api/groups/1/stats - 200 - 1.651491ms"
time="2025-08-01 17:05:38" level=info msg="GET /api/keys?group_id=1&page=1&page_size=12 - 200 - 2.109944ms"
time="2025-08-01 17:23:56" level=info msg="CronChecker: Group 'gemini' has no invalid keys to check."
time="2025-08-01 17:24:53" level=info msg="GET / - 200 - 896.997µs"
time="2025-08-01 17:25:03" level=info msg="GET /api/dashboard/stats - 200 - 1.847817ms"
time="2025-08-01 17:25:03" level=info msg="GET /api/groups/list - 200 - 542.903µs"
time="2025-08-01 17:25:03" level=info msg="GET /api/dashboard/chart - 200 - 346.89µs"
time="2025-08-01 17:25:03" level=info msg="GET /api/tasks/status - 200 - 143.232µs"
time="2025-08-01 17:25:22" level=info msg="GET /api/groups/config-options - 200 - 229.617µs"
time="2025-08-01 17:25:22" level=info msg="GET /api/groups - 200 - 605.813µs"
time="2025-08-01 17:25:27" level=info msg="GET /api/groups/2/stats - 200 - 1.552403ms"
time="2025-08-01 17:25:27" level=info msg="GET /api/keys?group_id=2&page=1&page_size=12 - 200 - 3.175713ms"
time="2025-08-01 17:25:28" level=info msg="GET /api/keys?group_id=1&page=1&page_size=12 - 200 - 1.443076ms"
time="2025-08-01 17:25:28" level=info msg="GET /api/groups/1/stats - 200 - 2.586733ms"
time="2025-08-01 17:25:50" level=info msg="POST /api/keys/add-async - 200 - 144.001331ms"
time="2025-08-01 17:25:54" level=info msg="GET /api/tasks/status - 200 - 153.812µs"
time="2025-08-01 17:25:58" level=info msg="GET /api/groups/1/stats - 200 - 11.41967ms"
time="2025-08-01 17:25:58" level=info msg="GET /api/keys?group_id=1&page=1&page_size=12 - 200 - 14.618487ms"
time="2025-08-01 17:26:48" level=info msg="POST /api/keys/validate-group - 200 - 157.744214ms"
time="2025-08-01 17:26:48" level=info msg="Starting manual validation for group targon"
time="2025-08-01 17:26:49" level=error msg="Failed to handle key success" error="failed to update key in DB: database is locked (5) (SQLITE_BUSY)" keyID=10
time="2025-08-01 17:26:50" level=error msg="Failed to handle key failure" error="failed to update key stats in DB: database is locked (5) (SQLITE_BUSY)" keyID=653
time="2025-08-01 17:26:50" level=error msg="Failed to handle key failure" error="failed to update key stats in DB: database is locked (5) (SQLITE_BUSY)" keyID=644
time="2025-08-01 17:26:50" level=error msg="Failed to handle key failure" error="failed to update key stats in DB: database is locked (5) (SQLITE_BUSY)" keyID=646
time="2025-08-01 17:26:50" level=error msg="Failed to handle key failure" error="failed to update key stats in DB: database is locked (5) (SQLITE_BUSY)" keyID=657
time="2025-08-01 17:26:50" level=warning msg="Key has reached blacklist threshold, disabling." keyID=664 threshold=3
time="2025-08-01 17:26:50" level=error msg="Failed to handle key failure" error="failed to update key stats in DB: database is locked (5) (SQLITE_BUSY)" keyID=668
time="2025-08-01 17:26:50" level=error msg="Failed to handle key failure" error="failed to update key stats in DB: database is locked (5) (SQLITE_BUSY)" keyID=662
time="2025-08-01 17:26:51" level=warning msg="Key has reached blacklist threshold, disabling." keyID=683 threshold=3
time="2025-08-01 17:26:52" level=error msg="Failed to handle key failure" error="failed to update key stats in DB: database is locked (5) (SQLITE_BUSY)" keyID=693
time="2025-08-01 17:26:52" level=error msg="Failed to handle key failure" error="failed to update key stats in DB: database is locked (5) (SQLITE_BUSY)" keyID=702
time="2025-08-01 17:26:52" level=info msg="GET /api/tasks/status - 200 - 84.891µs"
time="2025-08-01 17:26:52" level=error msg="Failed to handle key failure" error="failed to update key stats in DB: database is locked (5) (SQLITE_BUSY)" keyID=716
time="2025-08-01 17:26:53" level=error msg="Failed to handle key failure" error="failed to update key stats in DB: database is locked (5) (SQLITE_BUSY)" keyID=725
time="2025-08-01 17:26:53" level=warning msg="Key has reached blacklist threshold, disabling." keyID=739 threshold=3
time="2025-08-01 17:26:57" level=info msg="GET /api/tasks/status - 200 - 95.293µs"
time="2025-08-01 17:27:00" level=info msg="GET /api/tasks/status - 200 - 120.128µs"
time="2025-08-01 17:27:09" level=info msg="GET /api/tasks/status - 200 - 146.589µs"
time="2025-08-01 17:27:14" level=info msg="GET /api/tasks/status - 200 - 116.121µs"
time="2025-08-01 17:27:18" level=info msg="GET /api/tasks/status - 200 - 87.696µs"
time="2025-08-01 17:27:22" level=info msg="GET /api/tasks/status - 200 - 88.859µs"
time="2025-08-01 17:27:26" level=info msg="GET /api/tasks/status - 200 - 115.841µs"
time="2025-08-01 17:27:29" level=info msg="GET /api/tasks/status - 200 - 172.609µs"
time="2025-08-01 17:27:33" level=info msg="GET /api/tasks/status - 200 - 155.586µs"
time="2025-08-01 17:27:35" level=info msg="GET /api/tasks/status - 200 - 106.322µs"
time="2025-08-01 17:27:39" level=info msg="GET /api/tasks/status - 200 - 139.516µs"
time="2025-08-01 17:27:43" level=info msg="GET /api/tasks/status - 200 - 106.833µs"
time="2025-08-01 17:27:45" level=error msg="Failed to handle key failure" error="failed to update key stats in DB: database is locked (5) (SQLITE_BUSY)" keyID=1418
time="2025-08-01 17:27:46" level=error msg="Failed to handle key failure" error="failed to update key stats in DB: database is locked (5) (SQLITE_BUSY)" keyID=1431
time="2025-08-01 17:27:46" level=error msg="Failed to handle key failure" error="failed to update key stats in DB: database is locked (5) (SQLITE_BUSY)" keyID=1435
time="2025-08-01 17:27:48" level=info msg="GET /api/tasks/status - 200 - 153.011µs"
time="2025-08-01 17:27:48" level=error msg="Failed to handle key failure" error="failed to update key stats in DB: database is locked (5) (SQLITE_BUSY)" keyID=1477
time="2025-08-01 17:27:48" level=error msg="Failed to handle key failure" error="failed to update key stats in DB: database is locked (5) (SQLITE_BUSY)" keyID=1480
time="2025-08-01 17:27:53" level=info msg="GET /api/tasks/status - 200 - 143.714µs"
time="2025-08-01 17:27:58" level=error msg="Failed to handle key failure" error="failed to update key stats in DB: database is locked (5) (SQLITE_BUSY)" keyID=1650
time="2025-08-01 17:27:59" level=info msg="GET /api/tasks/status - 200 - 149.084µs"
time="2025-08-01 17:28:03" level=info msg="GET /api/tasks/status - 200 - 157.65µs"
time="2025-08-01 17:28:08" level=info msg="GET /api/tasks/status - 200 - 102.375µs"
time="2025-08-01 17:28:13" level=info msg="GET /api/tasks/status - 200 - 110.731µs"
time="2025-08-01 17:28:14" level=error msg="Failed to handle key failure" error="failed to update key stats in DB: database is locked (5) (SQLITE_BUSY)" keyID=1919
time="2025-08-01 17:28:14" level=error msg="Failed to handle key failure" error="failed to update key stats in DB: database is locked (5) (SQLITE_BUSY)" keyID=1926
time="2025-08-01 17:28:16" level=error msg="Failed to handle key failure" error="failed to update key stats in DB: database is locked (5) (SQLITE_BUSY)" keyID=1971
time="2025-08-01 17:28:16" level=error msg="Failed to handle key failure" error="failed to update key stats in DB: database is locked (5) (SQLITE_BUSY)" keyID=1976
time="2025-08-01 17:28:16" level=info msg="GET /api/tasks/status - 200 - 190.493µs"
time="2025-08-01 17:28:17" level=error msg="Failed to handle key failure" error="failed to update key stats in DB: database is locked (5) (SQLITE_BUSY)" keyID=2010
time="2025-08-01 17:28:17" level=error msg="Failed to handle key failure" error="failed to update key stats in DB: database is locked (5) (SQLITE_BUSY)" keyID=2020
time="2025-08-01 17:28:19" level=info msg="GET /api/tasks/status - 200 - 145.116µs"
time="2025-08-01 17:28:19" level=error msg="Failed to handle key failure" error="failed to update key stats in DB: database is locked (5) (SQLITE_BUSY)" keyID=2065
time="2025-08-01 17:28:21" level=error msg="Failed to handle key failure" error="failed to update key stats in DB: database is locked (5) (SQLITE_BUSY)" keyID=2086
time="2025-08-01 17:28:22" level=error msg="Failed to handle key failure" error="failed to update key stats in DB: database is locked (5) (SQLITE_BUSY)" keyID=2106
time="2025-08-01 17:28:22" level=error msg="Failed to handle key failure" error="failed to update key stats in DB: database is locked (5) (SQLITE_BUSY)" keyID=2119
time="2025-08-01 17:28:23" level=error msg="Failed to handle key failure" error="failed to update key stats in DB: database is locked (5) (SQLITE_BUSY)" keyID=2140
time="2025-08-01 17:28:25" level=info msg="GET /api/tasks/status - 200 - 126.52µs"
time="2025-08-01 17:28:28" level=error msg="Failed to handle key failure" error="failed to update key stats in DB: database is locked (5) (SQLITE_BUSY)" keyID=2224
time="2025-08-01 17:28:28" level=info msg="GET / - 200 - 2.159488ms"
time="2025-08-01 17:28:29" level=info msg="GET /assets/logo-BV-_qOqO.png - 200 - 993.821µs"
time="2025-08-01 17:28:29" level=info msg="GET /favicon.ico - 200 - 428.685µs"
time="2025-08-01 17:28:30" level=error msg="Failed to handle key failure" error="failed to update key stats in DB: database is locked (5) (SQLITE_BUSY)" keyID=2259
time="2025-08-01 17:28:30" level=error msg="Failed to handle key failure" error="failed to update key stats in DB: database is locked (5) (SQLITE_BUSY)" keyID=2257
time="2025-08-01 17:28:30" level=error msg="Failed to handle key failure" error="failed to update key stats in DB: database is locked (5) (SQLITE_BUSY)" keyID=2255
time="2025-08-01 17:28:30" level=info msg="GET /api/tasks/status - 200 - 129.937µs"
time="2025-08-01 17:28:32" level=error msg="Failed to handle key failure" error="failed to update key stats in DB: database is locked (5) (SQLITE_BUSY)" keyID=2294
time="2025-08-01 17:28:32" level=error msg="Failed to handle key failure" error="failed to update key stats in DB: database is locked (5) (SQLITE_BUSY)" keyID=2296
time="2025-08-01 17:28:32" level=error msg="Failed to handle key failure" error="failed to update key stats in DB: database is locked (5) (SQLITE_BUSY)" keyID=2301
time="2025-08-01 17:28:33" level=error msg="Failed to handle key failure" error="failed to update key stats in DB: database is locked (5) (SQLITE_BUSY)" keyID=2351
time="2025-08-01 17:28:34" level=info msg="GET /api/tasks/status - 200 - 103.347µs"
time="2025-08-01 17:28:35" level=error msg="Failed to handle key failure" error="failed to update key stats in DB: database is locked (5) (SQLITE_BUSY)" keyID=2378
time="2025-08-01 17:28:35" level=error msg="Failed to handle key failure" error="failed to update key stats in DB: database is locked (5) (SQLITE_BUSY)" keyID=2381
time="2025-08-01 17:28:38" level=info msg="GET /api/tasks/status - 200 - 89.059µs"
time="2025-08-01 17:28:42" level=info msg="GET /api/tasks/status - 200 - 95.452µs"
time="2025-08-01 17:28:45" level=error msg="Failed to handle key failure" error="failed to update key stats in DB: database is locked (5) (SQLITE_BUSY)" keyID=2554
time="2025-08-01 17:28:46" level=error msg="Failed to handle key failure" error="failed to update key stats in DB: database is locked (5) (SQLITE_BUSY)" keyID=2573
time="2025-08-01 17:28:46" level=info msg="GET /api/tasks/status - 200 - 232.574µs"
time="2025-08-01 17:28:50" level=info msg="GET /api/tasks/status - 200 - 209.198µs"
time="2025-08-01 17:28:54" level=info msg="GET /api/tasks/status - 200 - 133.453µs"
time="2025-08-01 17:28:57" level=info msg="GET /api/tasks/status - 200 - 99.159µs"
time="2025-08-01 17:29:01" level=info msg="GET /api/tasks/status - 200 - 110.38µs"
time="2025-08-01 17:29:03" level=error msg="Failed to handle key failure" error="failed to update key stats in DB: database is locked (5) (SQLITE_BUSY)" keyID=2873
time="2025-08-01 17:29:04" level=error msg="Failed to handle key failure" error="failed to update key stats in DB: database is locked (5) (SQLITE_BUSY)" keyID=2895
time="2025-08-01 17:29:04" level=error msg="Failed to handle key failure" error="failed to update key stats in DB: database is locked (5) (SQLITE_BUSY)" keyID=2893
time="2025-08-01 17:29:04" level=info msg="GET /api/tasks/status - 200 - 120.119µs"
time="2025-08-01 17:29:06" level=info msg="GET /api/tasks/status - 200 - 97.425µs"
time="2025-08-01 17:29:10" level=info msg="GET /api/tasks/status - 200 - 96.073µs"
time="2025-08-01 17:29:13" level=error msg="Failed to handle key failure" error="failed to update key stats in DB: database is locked (5) (SQLITE_BUSY)" keyID=3057
time="2025-08-01 17:29:13" level=error msg="Failed to handle key failure" error="failed to update key stats in DB: database is locked (5) (SQLITE_BUSY)" keyID=3061
time="2025-08-01 17:29:14" level=info msg="GET /api/tasks/status - 200 - 94.761µs"
time="2025-08-01 17:29:17" level=error msg="Failed to handle key failure" error="failed to update key stats in DB: database is locked (5) (SQLITE_BUSY)" keyID=3148
time="2025-08-01 17:29:18" level=info msg="GET /api/tasks/status - 200 - 117.693µs"
time="2025-08-01 17:29:20" level=error msg="Failed to handle key failure" error="failed to update key stats in DB: database is locked (5) (SQLITE_BUSY)" keyID=3208
time="2025-08-01 17:29:21" level=error msg="Failed to handle key failure" error="failed to update key stats in DB: database is locked (5) (SQLITE_BUSY)" keyID=3237
time="2025-08-01 17:29:21" level=error msg="Failed to handle key failure" error="failed to update key stats in DB: database is locked (5) (SQLITE_BUSY)" keyID=3243
time="2025-08-01 17:29:22" level=info msg="GET /api/tasks/status - 200 - 111.892µs"
time="2025-08-01 17:29:28" level=info msg="GET /api/tasks/status - 200 - 104.028µs"
time="2025-08-01 17:29:32" level=info msg="GET /api/tasks/status - 200 - 97.115µs"
time="2025-08-01 17:29:39" level=info msg="GET /api/tasks/status - 200 - 142.802µs"
time="2025-08-01 17:29:45" level=info msg="GET /api/tasks/status - 200 - 130.107µs"
time="2025-08-01 17:29:49" level=info msg="GET /api/tasks/status - 200 - 173.691µs"
time="2025-08-01 17:29:55" level=info msg="GET /api/tasks/status - 200 - 154.665µs"
time="2025-08-01 17:30:00" level=info msg="GET /api/tasks/status - 200 - 129.488µs"
time="2025-08-01 17:30:04" level=info msg="GET /api/tasks/status - 200 - 101.103µs"
time="2025-08-01 17:30:10" level=info msg="GET /api/tasks/status - 200 - 285.714µs"
time="2025-08-01 17:30:13" level=info msg="GET /api/tasks/status - 200 - 202.195µs"
time="2025-08-01 17:30:15" level=info msg="GET /api/tasks/status - 200 - 99.109µs"
time="2025-08-01 17:30:20" level=info msg="GET /api/tasks/status - 200 - 316.092µs"
time="2025-08-01 17:30:22" level=info msg="GET /api/tasks/status - 200 - 113.837µs"
time="2025-08-01 17:30:26" level=info msg="GET /api/tasks/status - 200 - 121.311µs"
time="2025-08-01 17:30:31" level=info msg="GET /api/tasks/status - 200 - 128.845µs"
time="2025-08-01 17:30:35" level=info msg="GET /api/tasks/status - 200 - 89.39µs"
time="2025-08-01 17:30:38" level=info msg="GET /api/tasks/status - 200 - 101.053µs"
time="2025-08-01 17:30:41" level=info msg="GET /api/tasks/status - 200 - 314.8µs"
time="2025-08-01 17:30:49" level=info msg="GET /api/tasks/status - 200 - 102.554µs"
time="2025-08-01 17:30:54" level=info msg="POST /proxy/targon/v1/chat/completions - 200 - 304.021399ms"
time="2025-08-01 17:30:56" level=info msg="Successfully flushed 1 request logs."
time="2025-08-01 17:30:59" level=info msg="GET /api/tasks/status - 200 - 100.712µs"
time="2025-08-01 17:31:09" level=info msg="GET /api/tasks/status - 200 - 216.142µs"
time="2025-08-01 17:31:14" level=info msg="GET /api/tasks/status - 200 - 121.682µs"
time="2025-08-01 17:31:18" level=info msg="GET /api/tasks/status - 200 - 86.896µs"
time="2025-08-01 17:31:22" level=info msg="GET /api/tasks/status - 200 - 147.781µs"
time="2025-08-01 17:31:26" level=info msg="GET /api/groups/config-options - 200 - 452.512µs"
time="2025-08-01 17:31:26" level=info msg="GET /api/channel-types - 200 - 58.081µs"
time="2025-08-01 17:31:26" level=info msg="GET /api/tasks/status - 200 - 108.856µs"
time="2025-08-01 17:31:31" level=info msg="GET /api/tasks/status - 200 - 99.98µs"
time="2025-08-01 17:31:36" level=info msg="GET /api/tasks/status - 200 - 114.348µs"
time="2025-08-01 17:31:40" level=info msg="GET /api/tasks/status - 200 - 134.887µs"
time="2025-08-01 17:31:46" level=info msg="GET /api/tasks/status - 200 - 121.662µs"
time="2025-08-01 17:31:52" level=info msg="GET /api/tasks/status - 200 - 134.726µs"
time="2025-08-01 17:31:56" level=info msg="GET /api/tasks/status - 200 - 95.401µs"
time="2025-08-01 17:32:05" level=info msg="GET /api/tasks/status - 200 - 125.569µs"
time="2025-08-01 17:32:11" level=info msg="GET /api/tasks/status - 200 - 182.097µs"
time="2025-08-01 17:32:16" level=info msg="GET /api/tasks/status - 200 - 101.223µs"
time="2025-08-01 17:32:21" level=info msg="GET /api/tasks/status - 200 - 86.434µs"
time="2025-08-01 17:32:27" level=info msg="GET /api/tasks/status - 200 - 140.007µs"
time="2025-08-01 17:32:31" level=info msg="GET /api/tasks/status - 200 - 88.75µs"
time="2025-08-01 17:32:38" level=info msg="GET /api/tasks/status - 200 - 228.004µs"
time="2025-08-01 17:32:43" level=info msg="GET /api/tasks/status - 200 - 107.245µs"
time="2025-08-01 17:32:47" level=info msg="GET /api/tasks/status - 200 - 99.59µs"
time="2025-08-01 17:32:52" level=info msg="GET /api/tasks/status - 200 - 177.848µs"
time="2025-08-01 17:32:57" level=info msg="GET /api/tasks/status - 200 - 86.094µs"
time="2025-08-01 17:33:01" level=info msg="GET /api/tasks/status - 200 - 173.621µs"
time="2025-08-01 17:33:02" level=info msg="PUT /api/groups/1 - 200 - 4.85107ms"
time="2025-08-01 17:33:02" level=info msg="cache reloaded successfully" syncer=groups
time="2025-08-01 17:33:04" level=info msg="GET /api/tasks/status - 200 - 151.468µs"
time="2025-08-01 17:33:06" level=info msg="GET /api/groups - 200 - 739.969µs"
time="2025-08-01 17:33:08" level=info msg="GET /api/tasks/status - 200 - 201.323µs"
time="2025-08-01 17:33:09" level=info msg="GET /api/groups/1/stats - 200 - 11.461559ms"
time="2025-08-01 17:33:09" level=info msg="GET /api/keys?group_id=1&page=1&page_size=12 - 200 - 18.445501ms"
time="2025-08-01 17:33:13" level=info msg="GET /api/tasks/status - 200 - 111.383µs"
time="2025-08-01 17:33:17" level=info msg="GET /api/tasks/status - 200 - 216.091µs"
time="2025-08-01 17:33:21" level=info msg="GET /api/tasks/status - 200 - 109.529µs"
time="2025-08-01 17:33:25" level=info msg="GET /api/tasks/status - 200 - 113.717µs"
time="2025-08-01 17:33:31" level=info msg="GET /api/tasks/status - 200 - 126.31µs"
time="2025-08-01 17:33:35" level=info msg="GET /api/tasks/status - 200 - 96.253µs"
time="2025-08-01 17:33:39" level=info msg="GET /api/tasks/status - 200 - 142.412µs"
time="2025-08-01 17:33:42" level=info msg="GET /api/tasks/status - 200 - 97.716µs"
time="2025-08-01 17:33:47" level=info msg="GET /api/tasks/status - 200 - 109.569µs"
time="2025-08-01 17:33:51" level=info msg="GET /api/tasks/status - 200 - 160.806µs"
time="2025-08-01 17:33:54" level=info msg="GET /api/tasks/status - 200 - 110.471µs"
time="2025-08-01 17:33:58" level=info msg="GET /api/tasks/status - 200 - 149.755µs"
time="2025-08-01 17:34:01" level=info msg="GET /api/tasks/status - 200 - 115.851µs"
time="2025-08-01 17:34:06" level=info msg="GET /api/tasks/status - 200 - 147.831µs"
time="2025-08-01 17:34:10" level=info msg="GET /api/tasks/status - 200 - 677.651µs"
time="2025-08-01 17:34:12" level=info msg="GET /api/settings - 200 - 317.153µs"
time="2025-08-01 17:34:13" level=info msg="GET /api/tasks/status - 200 - 103.578µs"
time="2025-08-01 17:34:17" level=info msg="GET /api/tasks/status - 200 - 109.979µs"
time="2025-08-01 17:34:22" level=info msg="GET /api/tasks/status - 200 - 115.809µs"
time="2025-08-01 17:34:32" level=info msg="GET /api/tasks/status - 200 - 114.969µs"
time="2025-08-01 17:34:34" level=info msg="GET /api/tasks/status - 200 - 112.284µs"
time="2025-08-01 17:34:39" level=info msg="GET /api/tasks/status - 200 - 271.898µs"
time="2025-08-01 17:34:44" level=info msg="GET /api/tasks/status - 200 - 113.517µs"
time="2025-08-01 17:34:47" level=info msg="GET /api/tasks/status - 200 - 93.488µs"
time="2025-08-01 17:34:51" level=info msg="GET /api/tasks/status - 200 - 150.195µs"
time="2025-08-01 17:34:54" level=info msg="GET /api/tasks/status - 200 - 96.603µs"
time="2025-08-01 17:34:57" level=info msg="GET /api/tasks/status - 200 - 116.552µs"
time="2025-08-01 17:35:01" level=info msg="GET /api/tasks/status - 200 - 143.574µs"
time="2025-08-01 17:35:03" level=warning msg="POST /proxy/targon/v1/chat/completions - 400 - 2.793055393s"
time="2025-08-01 17:35:04" level=info msg="GET /api/tasks/status - 200 - 141.529µs"
time="2025-08-01 17:35:08" level=info msg="GET /api/tasks/status - 200 - 172.468µs"
time="2025-08-01 17:35:12" level=info msg="GET /api/tasks/status - 200 - 113.626µs"
time="2025-08-01 17:35:14" level=warning msg="POST /proxy/targon/v1/chat/completions - 400 - 2.870942609s"
time="2025-08-01 17:35:15" level=info msg="GET /api/tasks/status - 200 - 159.895µs"
time="2025-08-01 17:35:19" level=info msg="GET /api/tasks/status - 200 - 126.561µs"
time="2025-08-01 17:35:24" level=info msg="GET /api/tasks/status - 200 - 104.99µs"
time="2025-08-01 17:35:26" level=info msg="GET /api/groups/config-options - 200 - 315.741µs"
time="2025-08-01 17:35:26" level=info msg="GET /api/groups - 200 - 663.203µs"
time="2025-08-01 17:35:27" level=info msg="GET /api/tasks/status - 200 - 116.972µs"
time="2025-08-01 17:35:28" level=info msg="GET /api/groups/2/stats - 200 - 6.318762ms"
time="2025-08-01 17:35:28" level=info msg="GET /api/keys?group_id=2&page=1&page_size=12 - 200 - 6.705229ms"
time="2025-08-01 17:35:30" level=info msg="GET /api/groups/1/stats - 200 - 6.666113ms"
time="2025-08-01 17:35:30" level=info msg="GET /api/keys?group_id=1&page=1&page_size=12 - 200 - 8.339851ms"
time="2025-08-01 17:35:30" level=info msg="GET /api/tasks/status - 200 - 214.699µs"
time="2025-08-01 17:35:45" level=info msg="GET /api/tasks/status - 200 - 119.789µs"
time="2025-08-01 17:35:48" level=info msg="GET /api/tasks/status - 200 - 281.736µs"
time="2025-08-01 17:35:53" level=info msg="GET /api/tasks/status - 200 - 180.904µs"
time="2025-08-01 17:35:56" level=info msg="Successfully flushed 2 request logs."
time="2025-08-01 17:35:56" level=info msg="GET /api/keys?group_id=1&page=2&page_size=12 - 200 - 8.515265ms"
time="2025-08-01 17:35:56" level=info msg="GET /api/tasks/status - 200 - 93.528µs"
time="2025-08-01 17:36:01" level=info msg="GET /api/tasks/status - 200 - 105.3µs"
time="2025-08-01 17:36:06" level=info msg="GET /api/tasks/status - 200 - 276.126µs"
time="2025-08-01 17:36:06" level=info msg="GET /api/keys?group_id=1&page=3&page_size=12 - 200 - 9.948965ms"
time="2025-08-01 17:36:10" level=info msg="GET /api/keys?group_id=1&page=4&page_size=12 - 200 - 11.872568ms"
time="2025-08-01 17:36:10" level=info msg="GET /api/tasks/status - 200 - 155.966µs"
time="2025-08-01 17:36:17" level=info msg="GET /api/tasks/status - 200 - 212.394µs"
time="2025-08-01 17:36:18" level=info msg="GET /api/keys?group_id=1&page=5&page_size=12 - 200 - 9.838825ms"
time="2025-08-01 17:36:22" level=info msg="GET /api/tasks/status - 200 - 97.436µs"
time="2025-08-01 17:36:24" level=info msg="GET /api/channel-types - 200 - 64.473µs"
time="2025-08-01 17:36:24" level=info msg="GET /api/groups/config-options - 200 - 363.883µs"
time="2025-08-01 17:36:29" level=info msg="GET /api/tasks/status - 200 - 6.940927ms"
time="2025-08-01 17:36:34" level=info msg="GET /api/tasks/status - 200 - 131.38µs"
time="2025-08-01 17:37:38" level=info msg="GET /api/tasks/status - 200 - 70.996µs"
time="2025-08-01 17:37:38" level=info msg="PUT /api/groups/1 - 200 - 5.399772ms"
time="2025-08-01 17:37:38" level=info msg="cache reloaded successfully" syncer=groups
time="2025-08-01 17:37:38" level=info msg="GET /api/groups - 200 - 575.124µs"
time="2025-08-01 17:37:38" level=info msg="GET /api/groups/1/stats - 200 - 8.45511ms"
time="2025-08-01 17:37:38" level=info msg="GET /api/keys?group_id=1&page=1&page_size=12 - 200 - 10.029446ms"
time="2025-08-01 17:37:39" level=info msg="GET /api/tasks/status - 200 - 135.799µs"
time="2025-08-01 17:37:42" level=info msg="GET /api/tasks/status - 200 - 113.786µs"
time="2025-08-01 17:37:45" level=info msg="GET /api/tasks/status - 200 - 146.258µs"
time="2025-08-01 17:37:49" level=info msg="GET /api/tasks/status - 200 - 150.015µs"
time="2025-08-01 17:37:54" level=info msg="GET /api/tasks/status - 200 - 179.461µs"
time="2025-08-01 17:38:01" level=info msg="GET /api/tasks/status - 200 - 317.474µs"
time="2025-08-01 17:38:07" level=info msg="GET /api/tasks/status - 200 - 95.362µs"
time="2025-08-01 17:38:16" level=info msg="GET /api/tasks/status - 200 - 89.5µs"
time="2025-08-01 17:38:18" level=info msg="GET /api/tasks/status - 200 - 106.975µs"
time="2025-08-01 17:38:20" level=info msg="GET /api/tasks/status - 200 - 94.46µs"
time="2025-08-01 17:38:21" level=info msg="GET /api/tasks/status - 200 - 113.556µs"
time="2025-08-01 17:38:22" level=info msg="GET /api/tasks/status - 200 - 100.531µs"
time="2025-08-01 17:38:24" level=info msg="GET /api/tasks/status - 200 - 105.47µs"
time="2025-08-01 17:38:25" level=info msg="GET /api/tasks/status - 200 - 114.738µs"
time="2025-08-01 17:38:26" level=info msg="GET /api/tasks/status - 200 - 127.924µs"
time="2025-08-01 17:38:28" level=info msg="GET /api/tasks/status - 200 - 178.4µs"
time="2025-08-01 17:38:29" level=info msg="GET /api/tasks/status - 200 - 134.165µs"
time="2025-08-01 17:38:31" level=info msg="GET /api/tasks/status - 200 - 108.236µs"
time="2025-08-01 17:38:33" level=info msg="GET /api/tasks/status - 200 - 259.885µs"
time="2025-08-01 17:38:35" level=info msg="GET /api/tasks/status - 200 - 135.188µs"
time="2025-08-01 17:38:37" level=info msg="GET /api/tasks/status - 200 - 256.178µs"
time="2025-08-01 17:38:39" level=info msg="GET /api/tasks/status - 200 - 161.137µs"
time="2025-08-01 17:38:41" level=info msg="GET /api/tasks/status - 200 - 268.482µs"
time="2025-08-01 17:38:43" level=info msg="GET /api/tasks/status - 200 - 137.031µs"
time="2025-08-01 17:38:45" level=info msg="GET /api/tasks/status - 200 - 127.543µs"
time="2025-08-01 17:38:47" level=info msg="GET /api/tasks/status - 200 - 128.475µs"
time="2025-08-01 17:38:49" level=info msg="GET /api/tasks/status - 200 - 178.89µs"
time="2025-08-01 17:38:51" level=info msg="GET /api/tasks/status - 200 - 94.369µs"
time="2025-08-01 17:38:53" level=info msg="GET /api/tasks/status - 200 - 150.196µs"
time="2025-08-01 17:38:55" level=info msg="GET /api/tasks/status - 200 - 131.27µs"
time="2025-08-01 17:38:57" level=info msg="CronChecker: Group 'targon' validation finished. Total checked: 22, became valid: 0. Duration: 879.618628ms."
time="2025-08-01 17:38:57" level=info msg="GET /api/tasks/status - 200 - 100.712µs"
time="2025-08-01 17:38:59" level=info msg="GET /api/tasks/status - 200 - 138.614µs"
time="2025-08-01 17:39:01" level=info msg="GET /api/tasks/status - 200 - 152.229µs"
time="2025-08-01 17:39:03" level=info msg="GET /api/tasks/status - 200 - 104.028µs"
time="2025-08-01 17:39:05" level=info msg="GET /api/tasks/status - 200 - 92.626µs"
time="2025-08-01 17:39:07" level=info msg="GET /api/tasks/status - 200 - 112.995µs"
time="2025-08-01 17:39:09" level=info msg="GET /api/tasks/status - 200 - 100.643µs"
time="2025-08-01 17:39:11" level=info msg="GET /api/tasks/status - 200 - 95.833µs"
time="2025-08-01 17:39:13" level=info msg="GET /api/tasks/status - 200 - 98.628µs"
time="2025-08-01 17:39:15" level=info msg="GET /api/tasks/status - 200 - 100.712µs"
time="2025-08-01 17:39:17" level=info msg="GET /api/tasks/status - 200 - 109.509µs"
time="2025-08-01 17:39:19" level=info msg="GET /api/tasks/status - 200 - 121.512µs"
time="2025-08-01 17:39:21" level=info msg="GET /api/tasks/status - 200 - 92.277µs"
time="2025-08-01 17:39:23" level=info msg="GET /api/tasks/status - 200 - 95.462µs"
time="2025-08-01 17:39:25" level=info msg="GET /api/tasks/status - 200 - 93.459µs"
time="2025-08-01 17:39:27" level=info msg="GET /api/tasks/status - 200 - 211.513µs"
time="2025-08-01 17:39:29" level=info msg="GET /api/tasks/status - 200 - 99.42µs"
time="2025-08-01 17:39:31" level=info msg="GET /api/tasks/status - 200 - 278.531µs"
time="2025-08-01 17:39:33" level=info msg="GET /api/tasks/status - 200 - 131.871µs"
time="2025-08-01 17:39:35" level=info msg="GET /api/tasks/status - 200 - 178.329µs"
time="2025-08-01 17:39:37" level=info msg="GET /api/tasks/status - 200 - 151.499µs"
time="2025-08-01 17:39:39" level=info msg="GET /api/tasks/status - 200 - 186.766µs"
time="2025-08-01 17:39:41" level=info msg="GET /api/tasks/status - 200 - 136.841µs"
time="2025-08-01 17:39:43" level=info msg="GET /api/tasks/status - 200 - 152.601µs"
time="2025-08-01 17:39:45" level=info msg="GET /api/tasks/status - 200 - 197.266µs"
time="2025-08-01 17:39:47" level=info msg="GET /api/tasks/status - 200 - 122.073µs"
time="2025-08-01 17:39:49" level=info msg="GET /api/tasks/status - 200 - 129.476µs"
time="2025-08-01 17:39:51" level=info msg="GET /api/tasks/status - 200 - 112.344µs"
time="2025-08-01 17:39:53" level=info msg="GET /api/tasks/status - 200 - 181.445µs"
time="2025-08-01 17:39:55" level=info msg="GET /api/tasks/status - 200 - 91.844µs"
time="2025-08-01 17:39:57" level=info msg="GET /api/tasks/status - 200 - 116.592µs"
time="2025-08-01 17:39:59" level=info msg="GET /api/tasks/status - 200 - 117.955µs"
time="2025-08-01 17:40:01" level=info msg="GET /api/tasks/status - 200 - 190.343µs"
time="2025-08-01 17:40:03" level=info msg="GET /api/tasks/status - 200 - 220.76µs"
time="2025-08-01 17:40:05" level=info msg="GET /api/tasks/status - 200 - 175.143µs"
time="2025-08-01 17:40:07" level=info msg="GET /api/tasks/status - 200 - 108.015µs"
time="2025-08-01 17:40:09" level=info msg="GET /api/tasks/status - 200 - 124.266µs"
time="2025-08-01 17:40:11" level=info msg="GET /api/tasks/status - 200 - 139.726µs"
time="2025-08-01 17:40:13" level=info msg="GET /api/tasks/status - 200 - 155.155µs"
time="2025-08-01 17:40:15" level=info msg="GET /api/tasks/status - 200 - 90.864µs"
time="2025-08-01 17:40:17" level=info msg="GET /api/tasks/status - 200 - 159.102µs"
time="2025-08-01 17:40:19" level=info msg="GET /api/tasks/status - 200 - 106.624µs"
time="2025-08-01 17:40:21" level=info msg="GET /api/tasks/status - 200 - 116.832µs"
time="2025-08-01 17:40:23" level=info msg="GET /api/tasks/status - 200 - 107.715µs"
time="2025-08-01 17:40:25" level=info msg="GET /api/tasks/status - 200 - 154.484µs"
time="2025-08-01 17:40:30" level=info msg="GET /api/tasks/status - 200 - 133.815µs"
time="2025-08-01 17:40:32" level=info msg="GET /api/tasks/status - 200 - 191.865µs"
time="2025-08-01 17:40:34" level=info msg="GET /api/tasks/status - 200 - 115.84µs"
time="2025-08-01 17:40:36" level=info msg="GET /api/tasks/status - 200 - 159.984µs"
time="2025-08-01 17:40:38" level=info msg="GET /api/tasks/status - 200 - 170.955µs"
time="2025-08-01 17:40:40" level=info msg="GET /api/tasks/status - 200 - 105.43µs"
time="2025-08-01 17:40:42" level=info msg="GET /api/tasks/status - 200 - 114.156µs"
time="2025-08-01 17:40:44" level=info msg="GET /api/tasks/status - 200 - 146.379µs"
time="2025-08-01 17:40:46" level=info msg="GET /api/tasks/status - 200 - 96.134µs"
time="2025-08-01 17:40:48" level=info msg="GET /api/tasks/status - 200 - 184.661µs"
time="2025-08-01 17:40:50" level=info msg="GET /api/tasks/status - 200 - 142.922µs"
time="2025-08-01 17:40:52" level=info msg="GET /api/tasks/status - 200 - 130.208µs"
time="2025-08-01 17:40:54" level=info msg="GET /api/tasks/status - 200 - 107.324µs"
time="2025-08-01 17:40:56" level=info msg="GET /api/tasks/status - 200 - 266.858µs"
time="2025-08-01 17:40:57" level=info msg="POST /proxy/targon/v1/chat/completions - 200 - 576.798835ms"
time="2025-08-01 17:40:58" level=info msg="GET /api/tasks/status - 200 - 105.21µs"
time="2025-08-01 17:40:59" level=info msg="GET /api/tasks/status - 200 - 236.2µs"
time="2025-08-01 17:41:00" level=info msg="GET /api/tasks/status - 200 - 129.647µs"
time="2025-08-01 17:41:02" level=info msg="GET /api/tasks/status - 200 - 162.029µs"
time="2025-08-01 17:41:03" level=info msg="GET /api/tasks/status - 200 - 136.48µs"
time="2025-08-01 17:41:04" level=info msg="GET /api/tasks/status - 200 - 126.691µs"
time="2025-08-01 17:41:06" level=info msg="GET /api/tasks/status - 200 - 140.338µs"
time="2025-08-01 17:41:07" level=info msg="GET /api/tasks/status - 200 - 94.261µs"
time="2025-08-01 17:41:08" level=info msg="GET /api/tasks/status - 200 - 122.373µs"
time="2025-08-01 17:41:10" level=info msg="GET /api/tasks/status - 200 - 103.608µs"
time="2025-08-01 17:41:11" level=info msg="GET /api/tasks/status - 200 - 113.145µs"
time="2025-08-01 17:41:13" level=info msg="GET /api/tasks/status - 200 - 100.561µs"
time="2025-08-01 17:41:14" level=error msg="Failed to handle key failure" error="failed to update key stats in DB: database is locked (5) (SQLITE_BUSY)" keyID=13109
time="2025-08-01 17:41:14" level=info msg="GET /api/tasks/status - 200 - 114.497µs"
time="2025-08-01 17:41:14" level=error msg="Failed to handle key failure" error="failed to update key stats in DB: database is locked (5) (SQLITE_BUSY)" keyID=13124
time="2025-08-01 17:41:14" level=error msg="Failed to handle key failure" error="failed to update key stats in DB: database is locked (5) (SQLITE_BUSY)" keyID=13129
time="2025-08-01 17:41:15" level=error msg="Failed to handle key failure" error="failed to update key stats in DB: database is locked (5) (SQLITE_BUSY)" keyID=13147
time="2025-08-01 17:41:15" level=error msg="Failed to handle key failure" error="failed to update key stats in DB: database is locked (5) (SQLITE_BUSY)" keyID=13142
time="2025-08-01 17:41:15" level=info msg="GET /api/tasks/status - 200 - 157.88µs"
time="2025-08-01 17:41:17" level=info msg="GET /api/tasks/status - 200 - 134.527µs"
time="2025-08-01 17:41:18" level=info msg="GET /api/tasks/status - 200 - 104.408µs"
time="2025-08-01 17:41:19" level=info msg="GET /api/tasks/status - 200 - 245.678µs"
time="2025-08-01 17:41:21" level=info msg="GET /api/tasks/status - 200 - 107.875µs"
time="2025-08-01 17:41:22" level=info msg="GET /api/tasks/status - 200 - 142.211µs"
time="2025-08-01 17:41:23" level=info msg="GET /api/tasks/status - 200 - 199.78µs"
time="2025-08-01 17:41:25" level=info msg="GET /api/tasks/status - 200 - 284.341µs"
time="2025-08-01 17:41:26" level=info msg="GET /api/tasks/status - 200 - 189.381µs"
time="2025-08-01 17:41:27" level=info msg="GET /api/tasks/status - 200 - 150.006µs"
time="2025-08-01 17:41:29" level=info msg="GET /api/tasks/status - 200 - 113.447µs"
time="2025-08-01 17:41:30" level=info msg="GET /api/tasks/status - 200 - 131.14µs"
time="2025-08-01 17:41:30" level=error msg="Failed to handle key failure" error="failed to update key stats in DB: database is locked (5) (SQLITE_BUSY)" keyID=13410
time="2025-08-01 17:41:30" level=error msg="Failed to handle key failure" error="failed to update key stats in DB: database is locked (5) (SQLITE_BUSY)" keyID=13414
time="2025-08-01 17:41:30" level=error msg="Failed to handle key failure" error="failed to update key stats in DB: database is locked (5) (SQLITE_BUSY)" keyID=13420
time="2025-08-01 17:41:30" level=error msg="Failed to handle key failure" error="failed to update key stats in DB: database is locked (5) (SQLITE_BUSY)" keyID=13419
time="2025-08-01 17:41:31" level=error msg="Failed to handle key failure" error="failed to update key stats in DB: database is locked (5) (SQLITE_BUSY)" keyID=13428
time="2025-08-01 17:41:31" level=error msg="Failed to handle key failure" error="failed to update key stats in DB: database is locked (5) (SQLITE_BUSY)" keyID=13440
time="2025-08-01 17:41:32" level=info msg="GET /api/tasks/status - 200 - 150.056µs"
time="2025-08-01 17:41:32" level=info msg="Manual validation finished for group targon: {TotalKeys:12825 ValidKeys:12136 InvalidKeys:689}"
time="2025-08-01 17:41:33" level=info msg="GET /api/tasks/status - 200 - 110.931µs"
time="2025-08-01 17:41:33" level=info msg="GET /api/groups/1/stats - 200 - 10.78955ms"
time="2025-08-01 17:41:33" level=info msg="GET /api/keys?group_id=1&page=1&page_size=12 - 200 - 12.987685ms"
time="2025-08-01 17:41:34" level=info msg="POST /proxy/targon/v1/chat/completions - 200 - 773.190634ms"
time="2025-08-01 17:41:52" level=info msg="GET /api/keys?group_id=2&page=1&page_size=12 - 200 - 6.206946ms"
time="2025-08-01 17:41:52" level=info msg="GET /api/groups/2/stats - 200 - 9.012527ms"
time="2025-08-01 17:41:53" level=info msg="GET /api/groups/1/stats - 200 - 7.219624ms"
time="2025-08-01 17:41:53" level=info msg="GET /api/keys?group_id=1&page=1&page_size=12 - 200 - 9.33877ms"
time="2025-08-01 17:41:54" level=info msg="GET /api/groups/2/stats - 200 - 4.75329ms"
time="2025-08-01 17:41:54" level=info msg="GET /api/keys?group_id=2&page=1&page_size=12 - 200 - 6.456962ms"
time="2025-08-01 17:41:56" level=info msg="Successfully flushed 2 request logs."
time="2025-08-01 17:42:00" level=info msg="GET /api/groups/1/stats - 200 - 9.0195ms"
time="2025-08-01 17:42:00" level=info msg="GET /api/keys?group_id=1&page=1&page_size=12 - 200 - 14.207618ms"
time="2025-08-01 17:44:15" level=warning msg="POST /proxy/targon/v1/chat/completions - 429 - 1.595272442s"
time="2025-08-01 17:44:17" level=warning msg="POST /proxy/targon/v1/chat/completions - 429 - 1.506895793s"
time="2025-08-01 17:44:21" level=info msg="POST /proxy/targon/v1/chat/completions - 200 - 2.667874681s"
time="2025-08-01 17:44:56" level=info msg="Successfully flushed 3 request logs."
time="2025-08-01 17:45:07" level=info msg="POST /proxy/targon/v1/chat/completions - 200 - 5.996048952s"
time="2025-08-01 17:45:56" level=info msg="Successfully flushed 2 request logs."
time="2025-08-01 17:48:19" level=info msg="POST /proxy/targon/v1/chat/completions - 200 - 3m7.533544526s"
time="2025-08-01 17:50:27" level=info msg="GET / - 200 - 421.212µs"
time="2025-08-01 17:50:28" level=info msg="GET /api/groups/list - 200 - 519.819µs"
time="2025-08-01 17:50:28" level=info msg="GET /api/tasks/status - 200 - 173.179µs"
time="2025-08-01 17:50:28" level=info msg="GET /api/dashboard/chart - 200 - 971.33µs"
time="2025-08-01 17:50:28" level=info msg="GET /api/dashboard/stats - 200 - 8.549764ms"
time="2025-08-01 17:50:29" level=info msg="GET /api/groups/config-options - 200 - 152.54µs"
time="2025-08-01 17:50:29" level=info msg="GET /api/groups - 200 - 564.234µs"
time="2025-08-01 17:50:30" level=info msg="GET /api/groups/2/stats - 200 - 6.584423ms"
time="2025-08-01 17:50:30" level=info msg="GET /api/keys?group_id=2&page=1&page_size=12 - 200 - 8.723034ms"
time="2025-08-01 17:50:31" level=info msg="GET /api/groups/1/stats - 200 - 5.77382ms"
time="2025-08-01 17:50:31" level=info msg="GET /api/keys?group_id=1&page=1&page_size=12 - 200 - 9.540259ms"
time="2025-08-01 17:50:34" level=info msg="GET /api/channel-types - 200 - 58.822µs"
time="2025-08-01 17:50:34" level=info msg="GET /api/groups/config-options - 200 - 213.015µs"
time="2025-08-01 17:50:56" level=info msg="Successfully flushed 1 request logs."
time="2025-08-01 17:53:29" level=info msg="POST /proxy/targon/v1/chat/completions - 200 - 2m46.207564888s"
time="2025-08-01 17:53:31" level=info msg="POST /proxy/targon/v1/chat/completions - 200 - 968.624582ms"
time="2025-08-01 17:53:33" level=info msg="POST /proxy/targon/v1/chat/completions - 200 - 1.231332171s"
time="2025-08-01 17:53:33" level=info msg="POST /proxy/targon/v1/chat/completions - 200 - 4.000514517s"
time="2025-08-01 17:53:56" level=info msg="Successfully flushed 3 request logs."
time="2025-08-01 18:09:44" level=info msg="POST /proxy/targon/v1/chat/completions - 200 - 587.961763ms"
time="2025-08-01 18:09:45" level=info msg="POST /proxy/targon/v1/chat/completions - 200 - 1.051002866s"
time="2025-08-01 18:09:46" level=info msg="POST /proxy/targon/v1/chat/completions - 200 - 694.909127ms"
time="2025-08-01 18:09:46" level=info msg="POST /proxy/targon/v1/chat/completions - 200 - 273.854002ms"
time="2025-08-01 18:09:56" level=info msg="Successfully flushed 4 request logs."
time="2025-08-01 18:10:09" level=info msg="GET / - 200 - 639.056µs"
time="2025-08-01 18:10:10" level=info msg="GET /api/dashboard/chart - 200 - 1.686599ms"
time="2025-08-01 18:10:10" level=info msg="GET /api/groups/list - 200 - 1.822868ms"
time="2025-08-01 18:10:10" level=info msg="GET /api/tasks/status - 200 - 107.945µs"
time="2025-08-01 18:10:10" level=info msg="GET /api/dashboard/stats - 200 - 11.712741ms"
time="2025-08-01 18:10:12" level=info msg="GET /api/groups/config-options - 200 - 283.399µs"
time="2025-08-01 18:10:12" level=info msg="GET /api/groups - 200 - 841.361µs"
time="2025-08-01 18:10:12" level=info msg="GET /api/groups/2/stats - 200 - 8.826541ms"
time="2025-08-01 18:10:12" level=info msg="GET /api/keys?group_id=2&page=1&page_size=12 - 200 - 11.067162ms"
time="2025-08-01 18:10:14" level=info msg="GET /api/groups/1/stats - 200 - 10.068473ms"
time="2025-08-01 18:10:14" level=info msg="GET /api/keys?group_id=1&page=1&page_size=12 - 200 - 8.8627ms"
time="2025-08-01 18:10:17" level=info msg="GET /api/groups/config-options - 200 - 159.593µs"
time="2025-08-01 18:10:17" level=info msg="GET /api/channel-types - 200 - 71.867µs"
time="2025-08-01 18:10:23" level=info msg="PUT /api/groups/1 - 200 - 7.230365ms"
time="2025-08-01 18:10:23" level=info msg="cache reloaded successfully" syncer=groups
time="2025-08-01 18:10:24" level=info msg="GET /api/groups - 200 - 978.511µs"
time="2025-08-01 18:10:24" level=info msg="GET /api/groups/1/stats - 200 - 13.642853ms"
time="2025-08-01 18:10:24" level=info msg="GET /api/keys?group_id=1&page=1&page_size=12 - 200 - 20.048008ms"
time="2025-08-01 18:10:36" level=info msg="POST /proxy/targon/v1/chat/completions - 200 - 309.674794ms"
time="2025-08-01 18:10:37" level=info msg="POST /proxy/targon/v1/chat/completions - 200 - 306.167382ms"
time="2025-08-01 18:10:38" level=info msg="POST /proxy/targon/v1/chat/completions - 200 - 936.540082ms"
time="2025-08-01 18:10:39" level=info msg="POST /proxy/targon/v1/chat/completions - 200 - 772.852915ms"
time="2025-08-01 18:10:56" level=info msg="Successfully flushed 4 request logs."
time="2025-08-01 18:11:17" level=info msg="PUT /api/groups/1 - 200 - 4.028333ms"
time="2025-08-01 18:11:17" level=info msg="cache reloaded successfully" syncer=groups
time="2025-08-01 18:11:18" level=info msg="GET /api/groups - 200 - 943.275µs"
time="2025-08-01 18:11:18" level=info msg="GET /api/groups/1/stats - 200 - 11.836357ms"
time="2025-08-01 18:11:18" level=info msg="GET /api/keys?group_id=1&page=1&page_size=12 - 200 - 15.462354ms"
time="2025-08-01 18:11:55" level=info msg="POST /proxy/targon/v1/chat/completions - 200 - 25.893684982s"
time="2025-08-01 18:11:56" level=info msg="Successfully flushed 2 request logs."
time="2025-08-01 18:11:57" level=info msg="POST /proxy/targon/v1/chat/completions - 200 - 711.159808ms"
time="2025-08-01 18:11:57" level=info msg="POST /proxy/targon/v1/chat/completions - 200 - 1.034757733s"
time="2025-08-01 18:11:57" level=info msg="POST /proxy/targon/v1/chat/completions - 200 - 287.189721ms"
time="2025-08-01 18:11:58" level=info msg="POST /proxy/targon/v1/chat/completions - 200 - 243.953357ms"
time="2025-08-01 18:11:58" level=info msg="POST /proxy/targon/v1/chat/completions - 200 - 237.282757ms"
time="2025-08-01 18:12:00" level=info msg="POST /proxy/targon/v1/chat/completions - 200 - 2.198647928s"
time="2025-08-01 18:12:00" level=info msg="POST /proxy/targon/v1/chat/completions - 200 - 4.648053382s"
time="2025-08-01 18:12:37" level=info msg="POST /proxy/targon/v1/chat/completions - 200 - 278.997739ms"
time="2025-08-01 18:12:38" level=info msg="POST /proxy/targon/v1/chat/completions - 200 - 240.80556ms"
time="2025-08-01 18:12:38" level=info msg="POST /proxy/targon/v1/chat/completions - 200 - 247.294555ms"
time="2025-08-01 18:12:39" level=info msg="POST /proxy/targon/v1/chat/completions - 200 - 262.891126ms"
time="2025-08-01 18:12:56" level=info msg="Successfully flushed 10 request logs."
time="2025-08-01 18:13:32" level=info msg="GET /api/logs?page=1&page_size=15 - 200 - 1.198871ms"
time="2025-08-01 18:15:01" level=info msg="POST /proxy/targon/v1/chat/completions - 200 - 1.424970284s"
time="2025-08-01 18:15:04" level=info msg="POST /proxy/targon/v1/chat/completions - 200 - 953.459027ms"
time="2025-08-01 18:15:08" level=info msg="POST /proxy/targon/v1/chat/completions - 200 - 1.984796194s"
time="2025-08-01 18:15:46" level=info msg="POST /proxy/targon/v1/chat/completions - 200 - 321.225093ms"
time="2025-08-01 18:15:47" level=info msg="POST /proxy/targon/v1/chat/completions - 200 - 435.532923ms"
time="2025-08-01 18:15:48" level=info msg="POST /proxy/targon/v1/chat/completions - 200 - 897.02647ms"
time="2025-08-01 18:15:49" level=info msg="POST /proxy/targon/v1/chat/completions - 200 - 1.155433872s"
time="2025-08-01 18:15:50" level=info msg="POST /proxy/targon/v1/chat/completions - 200 - 21.447037127s"
time="2025-08-01 18:15:53" level=info msg="POST /proxy/targon/v1/chat/completions - 200 - 2.439572859s"
time="2025-08-01 18:15:53" level=info msg="POST /proxy/targon/v1/chat/completions - 200 - 1.148502216s"
time="2025-08-01 18:15:55" level=info msg="POST /proxy/targon/v1/chat/completions - 200 - 2.03685556s"
time="2025-08-01 18:15:56" level=info msg="Successfully flushed 11 request logs."
time="2025-08-01 18:16:50" level=info msg="POST /proxy/targon/v1/chat/completions - 200 - 764.461166ms"
time="2025-08-01 18:16:51" level=info msg="POST /proxy/targon/v1/chat/completions - 200 - 690.792907ms"
time="2025-08-01 18:16:51" level=info msg="POST /proxy/targon/v1/chat/completions - 200 - 496.882633ms"
time="2025-08-01 18:16:52" level=info msg="POST /proxy/targon/v1/chat/completions - 200 - 768.4782ms"
time="2025-08-01 18:16:56" level=info msg="Successfully flushed 4 request logs."
time="2025-08-01 18:17:31" level=info msg="GET /api/groups/config-options - 200 - 173.5µs"
time="2025-08-01 18:17:31" level=info msg="GET /api/groups - 200 - 861.98µs"
time="2025-08-01 18:17:32" level=info msg="GET /api/groups/2/stats - 200 - 6.659371ms"
time="2025-08-01 18:17:32" level=info msg="GET /api/keys?group_id=2&page=1&page_size=12 - 200 - 9.204322ms"
time="2025-08-01 18:17:32" level=info msg="GET /api/keys?group_id=1&page=1&page_size=12 - 200 - 7.156447ms"
time="2025-08-01 18:17:32" level=info msg="GET /api/groups/1/stats - 200 - 9.571692ms"
time="2025-08-01 18:17:40" level=info msg="POST /api/keys/clear-all-invalid - 200 - 14.313075ms"
time="2025-08-01 18:17:40" level=info msg="GET /api/keys?group_id=1&page=1&page_size=12 - 200 - 7.871729ms"
time="2025-08-01 18:17:41" level=info msg="GET /api/groups/1/stats - 200 - 7.558222ms"
time="2025-08-01 18:22:31" level=info msg="GET /keys?groupId=1 - 200 - 539.547µs"
time="2025-08-01 18:24:30" level=info msg="GET /api/groups/config-options - 200 - 189.821µs"
time="2025-08-01 18:24:30" level=info msg="GET /api/channel-types - 200 - 69.172µs"
time="2025-08-01 18:24:35" level=info msg="PUT /api/groups/1 - 200 - 3.832252ms"
time="2025-08-01 18:24:35" level=info msg="cache reloaded successfully" syncer=groups
time="2025-08-01 18:24:36" level=info msg="GET /api/groups - 200 - 748.654µs"
time="2025-08-01 18:24:36" level=info msg="GET /api/groups/1/stats - 200 - 7.034987ms"
time="2025-08-01 18:24:36" level=info msg="GET /api/keys?group_id=1&page=1&page_size=12 - 200 - 7.951932ms"
time="2025-08-01 18:25:00" level=info msg="GET /api/logs?page=1&page_size=15 - 200 - 1.016744ms"
time="2025-08-01 18:25:11" level=info msg="POST /proxy/targon/v1/chat/completions - 200 - 20.501601175s"
time="2025-08-01 18:25:13" level=warning msg="POST /proxy/targon/v1/chat/completions - 429 - 1.695766788s"
time="2025-08-01 18:25:14" level=warning msg="POST /proxy/targon/v1/chat/completions - 429 - 1.30869451s"
time="2025-08-01 18:25:16" level=info msg="POST /proxy/targon/v1/chat/completions - 200 - 1.414725671s"
time="2025-08-01 18:25:56" level=info msg="Successfully flushed 4 request logs."
time="2025-08-01 18:26:18" level=info msg="POST /proxy/targon/v1/chat/completions - 200 - 1.31738603s"
time="2025-08-01 18:26:21" level=info msg="POST /proxy/targon/v1/chat/completions - 200 - 2.641079553s"
time="2025-08-01 18:26:22" level=info msg="POST /proxy/targon/v1/chat/completions - 200 - 1.357461432s"
time="2025-08-01 18:26:23" level=info msg="POST /proxy/targon/v1/chat/completions - 200 - 712.789298ms"
time="2025-08-01 18:26:51" level=info msg="POST /proxy/targon/v1/chat/completions - 200 - 1.246498312s"
time="2025-08-01 18:26:55" level=info msg="POST /proxy/targon/v1/chat/completions - 200 - 3.917516357s"
time="2025-08-01 18:26:56" level=info msg="Successfully flushed 6 request logs."
time="2025-08-01 18:26:59" level=info msg="POST /proxy/targon/v1/chat/completions - 200 - 4.364598828s"
time="2025-08-01 18:27:04" level=info msg="POST /proxy/targon/v1/chat/completions - 200 - 4.154228598s"
time="2025-08-01 18:27:05" level=info msg="POST /proxy/targon/v1/chat/completions - 200 - 6.978575362s"
time="2025-08-01 18:27:08" level=info msg="GET /api/groups/config-options - 200 - 179.903µs"
time="2025-08-01 18:27:08" level=info msg="GET /api/groups - 200 - 605.472µs"
time="2025-08-01 18:27:08" level=info msg="GET /api/groups/2/stats - 200 - 5.273866ms"
time="2025-08-01 18:27:08" level=info msg="GET /api/keys?group_id=2&page=1&page_size=12 - 200 - 7.726144ms"
time="2025-08-01 18:27:09" level=info msg="GET /api/groups/1/stats - 200 - 6.464832ms"
time="2025-08-01 18:27:09" level=info msg="GET /api/keys?group_id=1&page=1&page_size=12 - 200 - 9.572697ms"
time="2025-08-01 18:27:11" level=info msg="GET /api/channel-types - 200 - 111.873µs"
time="2025-08-01 18:27:11" level=info msg="GET /api/groups/config-options - 200 - 205.752µs"
time="2025-08-01 18:27:43" level=info msg="PUT /api/groups/1 - 200 - 3.868732ms"
time="2025-08-01 18:27:43" level=info msg="cache reloaded successfully" syncer=groups
time="2025-08-01 18:27:43" level=info msg="GET /api/groups - 200 - 574.754µs"
time="2025-08-01 18:27:44" level=info msg="GET /api/groups/1/stats - 200 - 8.578525ms"
time="2025-08-01 18:27:44" level=info msg="GET /api/keys?group_id=1&page=1&page_size=12 - 200 - 9.519615ms"
time="2025-08-01 18:27:56" level=info msg="Successfully flushed 4 request logs."
time="2025-08-01 18:28:02" level=info msg="POST /proxy/targon/v1/chat/completions - 200 - 8.386962702s"
time="2025-08-01 18:28:08" level=info msg="POST /proxy/targon/v1/chat/completions - 200 - 4.898356364s"
time="2025-08-01 18:28:08" level=info msg="POST /proxy/targon/v1/chat/completions - 200 - 3.988517482s"
time="2025-08-01 18:28:15" level=info msg="POST /proxy/targon/v1/chat/completions - 200 - 10.274059316s"
time="2025-08-01 18:28:56" level=info msg="Successfully flushed 3 request logs."
time="2025-08-01 18:28:56" level=info msg="CronChecker: Group 'gemini' has no invalid keys to check."
time="2025-08-01 18:43:56" level=info msg="CronChecker: Group 'targon' has no invalid keys to check."
time="2025-08-01 19:28:56" level=info msg="CronChecker: Group 'gemini' has no invalid keys to check."
time="2025-08-01 19:33:56" level=info msg="Successfully flushed 1 request logs."
time="2025-08-01 19:34:18" level=info msg="POST /proxy/targon/v1/chat/completions - 200 - 1m10.751613664s"
time="2025-08-01 19:34:21" level=info msg="POST /proxy/targon/v1/chat/completions - 200 - 1.135675302s"
time="2025-08-01 19:34:22" level=info msg="POST /proxy/targon/v1/chat/completions - 200 - 4.303019534s"
time="2025-08-01 19:34:22" level=info msg="POST /proxy/targon/v1/chat/completions - 200 - 1.154453396s"
time="2025-08-01 19:34:56" level=info msg="Successfully flushed 3 request logs."
time="2025-08-01 19:35:57" level=warning msg="POST /proxy/targon/v1/chat/completions - 429 - 1.304102372s"
time="2025-08-01 19:36:01" level=info msg="POST /proxy/targon/v1/chat/completions - 200 - 3.90262787s"
time="2025-08-01 19:36:56" level=info msg="Successfully flushed 3 request logs."
time="2025-08-01 19:37:45" level=info msg="POST /proxy/targon/v1/chat/completions - 200 - 55.932517285s"
time="2025-08-01 19:37:49" level=info msg="POST /proxy/targon/v1/chat/completions - 200 - 3.457516559s"
time="2025-08-01 19:37:56" level=info msg="Successfully flushed 1 request logs."
time="2025-08-01 19:41:56" level=info msg="Successfully flushed 1 request logs."
time="2025-08-01 19:44:42" level=info msg="POST /proxy/targon/v1/chat/completions - 200 - 3m16.878347752s"
time="2025-08-01 19:44:48" level=info msg="POST /proxy/targon/v1/chat/completions - 200 - 5.372252587s"
time="2025-08-01 19:44:48" level=info msg="POST /proxy/targon/v1/chat/completions - 200 - 3.019800536s"
time="2025-08-01 19:44:50" level=info msg="POST /proxy/targon/v1/chat/completions - 200 - 2.276941981s"
time="2025-08-01 19:44:56" level=info msg="Successfully flushed 3 request logs."
time="2025-08-01 19:48:56" level=info msg="CronChecker: Group 'targon' has no invalid keys to check."
time="2025-08-01 20:33:56" level=info msg="CronChecker: Group 'gemini' has no invalid keys to check."
time="2025-08-01 20:53:56" level=info msg="CronChecker: Group 'targon' has no invalid keys to check."
time="2025-08-01 21:08:43" level=info msg="GET / - 200 - 78.259µs"
time="2025-08-01 21:09:43" level=info msg="GET / - 200 - 123.856µs"
time="2025-08-01 21:17:44" level=info msg="GET /assets/logo-BV-_qOqO.png - 200 - 350.990053ms"
time="2025-08-01 21:18:46" level=info msg="GET /assets/logo-BV-_qOqO.png - 200 - 90.597233ms"
time="2025-08-01 21:38:56" level=info msg="CronChecker: Group 'gemini' has no invalid keys to check."
time="2025-08-01 21:58:56" level=info msg="CronChecker: Group 'targon' has no invalid keys to check."
time="2025-08-01 22:12:06" level=info msg="GET / - 200 - 2.136504ms"
time="2025-08-01 22:12:06" level=info msg="GET /assets/index-Gswog8ov.css - 200 - 2.471823ms"
time="2025-08-01 22:12:06" level=info msg="GET /assets/index-SPLiOoPZ.js - 200 - 69.481336ms"
time="2025-08-01 22:12:09" level=info msg="GET /assets/Login-DzpmRpZS.css - 200 - 258.793µs"
time="2025-08-01 22:12:09" level=info msg="GET /assets/Login-C7BGdm6I.js - 200 - 342.332µs"
time="2025-08-01 22:12:11" level=info msg="GET /assets/logo-BV-_qOqO.png - 200 - 1.386568ms"
time="2025-08-01 22:12:36" level=info msg="GET /login - 200 - 183.189µs"
time="2025-08-01 22:14:04" level=info msg="POST /api/auth/login - 200 - 139.284µs"
time="2025-08-01 22:14:05" level=info msg="GET /assets/Dashboard-ZqIXRWdq.js - 200 - 957.281µs"
time="2025-08-01 22:14:05" level=info msg="GET /assets/display-DuIJKIJ5.js - 200 - 208.055µs"
time="2025-08-01 22:14:05" level=info msg="GET /assets/Dashboard-DHOcOEH2.css - 200 - 439.556µs"
time="2025-08-01 22:14:06" level=info msg="GET /api/dashboard/chart - 200 - 745.249µs"
time="2025-08-01 22:14:06" level=info msg="GET /api/groups/list - 200 - 1.67666ms"
time="2025-08-01 22:14:06" level=info msg="GET /api/tasks/status - 200 - 94.038µs"
time="2025-08-01 22:14:06" level=info msg="GET /api/dashboard/stats - 200 - 7.13772ms"
time="2025-08-01 22:14:08" level=info msg="GET /assets/ProxyKeysInput-qloCXNPS.js - 200 - 372.789µs"
time="2025-08-01 22:14:08" level=info msg="GET /assets/Keys-Cvs7q-Qm.js - 200 - 1.94444ms"
time="2025-08-01 22:14:08" level=info msg="GET /assets/ProxyKeysInput-CoLmi6p2.css - 200 - 2.510647ms"
time="2025-08-01 22:14:08" level=info msg="GET /assets/Search-oHPKo0PA.js - 200 - 322.734µs"
time="2025-08-01 22:14:08" level=info msg="GET /assets/Keys-CCps4UaT.css - 200 - 657.241µs"
time="2025-08-01 22:14:09" level=info msg="GET /api/groups/config-options - 200 - 218.636µs"
time="2025-08-01 22:14:09" level=info msg="GET /api/groups - 200 - 642.381µs"
time="2025-08-01 22:14:10" level=info msg="GET /api/groups/2/stats - 200 - 8.184621ms"
time="2025-08-01 22:14:10" level=info msg="GET /api/keys?group_id=2&page=1&page_size=12 - 200 - 10.181711ms"
time="2025-08-01 22:38:56" level=info msg="CronChecker: Group 'gemini' has no invalid keys to check."
time="2025-08-01 22:40:27" level=info msg="GET / - 200 - 694.83µs"
time="2025-08-01 22:40:29" level=info msg="GET /assets/Dashboard-ZqIXRWdq.js - 200 - 1.730858ms"
time="2025-08-01 22:40:29" level=info msg="GET /assets/Dashboard-DHOcOEH2.css - 200 - 352.73µs"
time="2025-08-01 22:40:29" level=info msg="GET /assets/display-DuIJKIJ5.js - 200 - 243.762µs"
time="2025-08-01 22:40:29" level=info msg="GET /api/groups/list - 200 - 648.352µs"
time="2025-08-01 22:40:29" level=info msg="GET /api/dashboard/chart - 200 - 596.754µs"
time="2025-08-01 22:40:29" level=info msg="GET /api/tasks/status - 200 - 30.928µs"
time="2025-08-01 22:40:29" level=info msg="GET /api/dashboard/stats - 200 - 6.876824ms"
time="2025-08-01 22:40:31" level=info msg="GET /assets/Search-oHPKo0PA.js - 200 - 499.719µs"
time="2025-08-01 22:40:31" level=info msg="GET /assets/Keys-Cvs7q-Qm.js - 200 - 2.860113ms"
time="2025-08-01 22:40:31" level=info msg="GET /assets/Keys-CCps4UaT.css - 200 - 723.796µs"
time="2025-08-01 22:40:31" level=info msg="GET /assets/ProxyKeysInput-CoLmi6p2.css - 200 - 624.356µs"
time="2025-08-01 22:40:31" level=info msg="GET /assets/ProxyKeysInput-qloCXNPS.js - 200 - 437.21µs"
time="2025-08-01 22:40:32" level=info msg="GET /api/groups - 200 - 912.423µs"
time="2025-08-01 22:40:32" level=info msg="GET /api/groups/config-options - 200 - 210.57µs"
time="2025-08-01 22:40:33" level=info msg="GET /api/groups/2/stats - 200 - 9.615046ms"
time="2025-08-01 22:40:33" level=info msg="GET /api/keys?group_id=2&page=1&page_size=12 - 200 - 9.552006ms"
time="2025-08-01 22:54:23" level=info msg="GET /keys?groupId=2 - 200 - 541.29µs"
time="2025-08-01 22:58:56" level=info msg="CronChecker: Group 'targon' has no invalid keys to check."
time="2025-08-01 23:43:56" level=info msg="CronChecker: Group 'gemini' has no invalid keys to check."
time="2025-08-02 00:03:56" level=info msg="CronChecker: Group 'targon' has no invalid keys to check."
time="2025-08-02 00:39:36" level=info msg="GET / - 200 - 291.345µs"
time="2025-08-02 00:39:36" level=info msg="GET /assets/index-SPLiOoPZ.js - 200 - 51.342641ms"
time="2025-08-02 00:39:37" level=info msg="GET /.git/config - 200 - 1.465458ms"
time="2025-08-02 00:48:56" level=info msg="CronChecker: Group 'gemini' has no invalid keys to check."
time="2025-08-02 01:08:56" level=info msg="CronChecker: Group 'targon' has no invalid keys to check."
time="2025-08-02 01:53:56" level=info msg="CronChecker: Group 'gemini' has no invalid keys to check."
time="2025-08-02 02:08:56" level=info msg="CronChecker: Group 'targon' has no invalid keys to check."
time="2025-08-02 02:58:56" level=info msg="CronChecker: Group 'gemini' has no invalid keys to check."
time="2025-08-02 03:13:56" level=info msg="CronChecker: Group 'targon' has no invalid keys to check."
time="2025-08-02 04:03:56" level=info msg="CronChecker: Group 'gemini' has no invalid keys to check."
time="2025-08-02 04:08:12" level=info msg="GET / - 200 - 2.290023ms"
time="2025-08-02 04:18:56" level=info msg="CronChecker: Group 'targon' has no invalid keys to check."
time="2025-08-02 05:08:56" level=info msg="CronChecker: Group 'gemini' has no invalid keys to check."
time="2025-08-02 05:23:56" level=info msg="CronChecker: Group 'targon' has no invalid keys to check."
time="2025-08-02 06:08:56" level=info msg="CronChecker: Group 'gemini' has no invalid keys to check."
time="2025-08-02 06:23:56" level=info msg="CronChecker: Group 'targon' has no invalid keys to check."
time="2025-08-02 06:24:10" level=info msg="GET /.env - 200 - 857.962µs"
time="2025-08-02 07:13:56" level=info msg="CronChecker: Group 'gemini' has no invalid keys to check."
time="2025-08-02 07:28:56" level=info msg="CronChecker: Group 'targon' has no invalid keys to check."
time="2025-08-02 08:18:56" level=info msg="CronChecker: Group 'gemini' has no invalid keys to check."
time="2025-08-02 08:28:56" level=info msg="CronChecker: Group 'targon' has no invalid keys to check."
time="2025-08-02 09:23:56" level=info msg="CronChecker: Group 'gemini' has no invalid keys to check."
time="2025-08-02 09:33:56" level=info msg="CronChecker: Group 'targon' has no invalid keys to check."
time="2025-08-02 10:23:56" level=info msg="CronChecker: Group 'gemini' has no invalid keys to check."
time="2025-08-02 10:38:56" level=info msg="CronChecker: Group 'targon' has no invalid keys to check."
time="2025-08-02 10:39:48" level=info msg="GET / - 200 - 2.591805ms"
time="2025-08-02 10:39:48" level=info msg="GET /js/twint_ch.js - 200 - 187.305µs"
time="2025-08-02 10:39:48" level=info msg="GET /assets/logo-BV-_qOqO.png - 200 - 974.706µs"
time="2025-08-02 10:39:48" level=info msg="GET /js/lkk_ch.js - 200 - 186.385µs"
time="2025-08-02 10:40:43" level=info msg="GET /.git/config - 200 - 2.233572ms"
time="2025-08-02 10:47:24" level=info msg="GET / - 200 - 441.15µs"
time="2025-08-02 10:47:25" level=info msg="GET /assets/index-Gswog8ov.css - 200 - 3.13182ms"
time="2025-08-02 10:47:25" level=info msg="GET /assets/index-SPLiOoPZ.js - 200 - 64.648138ms"
time="2025-08-02 10:47:26" level=info msg="GET /assets/Dashboard-ZqIXRWdq.js - 200 - 3.302386ms"
time="2025-08-02 10:47:26" level=info msg="GET /assets/Dashboard-DHOcOEH2.css - 200 - 630.159µs"
time="2025-08-02 10:47:26" level=info msg="GET /assets/display-DuIJKIJ5.js - 200 - 197.005µs"
time="2025-08-02 10:47:27" level=info msg="GET /api/groups/list - 200 - 531.893µs"
time="2025-08-02 10:47:27" level=info msg="GET /api/tasks/status - 200 - 37.752µs"
time="2025-08-02 10:47:27" level=info msg="GET /api/dashboard/chart - 200 - 875.887µs"
time="2025-08-02 10:47:27" level=info msg="GET /api/dashboard/stats - 200 - 10.028179ms"
time="2025-08-02 10:47:27" level=info msg="GET /assets/logo-BV-_qOqO.png - 200 - 1.084424ms"
time="2025-08-02 10:47:29" level=info msg="GET /assets/ProxyKeysInput-CoLmi6p2.css - 200 - 236.93µs"
time="2025-08-02 10:47:29" level=info msg="GET /assets/ProxyKeysInput-qloCXNPS.js - 200 - 501.003µs"
time="2025-08-02 10:47:29" level=info msg="GET /assets/Keys-Cvs7q-Qm.js - 200 - 2.08038ms"
time="2025-08-02 10:47:29" level=info msg="GET /assets/Search-oHPKo0PA.js - 200 - 387.909µs"
time="2025-08-02 10:47:29" level=info msg="GET /assets/Keys-CCps4UaT.css - 200 - 637.193µs"
time="2025-08-02 10:47:29" level=info msg="GET /api/groups/config-options - 200 - 391.455µs"
time="2025-08-02 10:47:29" level=info msg="GET /api/groups - 200 - 751.652µs"
time="2025-08-02 10:47:30" level=info msg="GET /api/keys?group_id=2&page=1&page_size=12 - 200 - 7.989858ms"
time="2025-08-02 10:47:30" level=info msg="GET /api/groups/2/stats - 200 - 8.634045ms"
time="2025-08-02 10:47:33" level=info msg="GET /api/groups/1/stats - 200 - 8.735569ms"
time="2025-08-02 10:47:33" level=info msg="GET /api/keys?group_id=1&page=1&page_size=12 - 200 - 9.846452ms"
time="2025-08-02 10:47:35" level=info msg="GET /api/groups/2/stats - 200 - 5.256176ms"
time="2025-08-02 10:47:35" level=info msg="GET /api/keys?group_id=2&page=1&page_size=12 - 200 - 6.749166ms"
time="2025-08-02 11:16:09" level=info msg="GET / - 200 - 585.354µs"
time="2025-08-02 11:28:56" level=info msg="CronChecker: Group 'gemini' has no invalid keys to check."
time="2025-08-02 11:38:56" level=info msg="CronChecker: Group 'targon' has no invalid keys to check."
time="2025-08-02 11:58:08" level=info msg="GET / - 200 - 562.279µs"
time="2025-08-02 11:58:17" level=info msg="GET /assets/logo-BV-_qOqO.png - 200 - 2.839373127s"
time="2025-08-02 11:58:43" level=info msg="PRI * - 200 - 69.763µs"
time="2025-08-02 12:04:21" level=info msg="GET /favicon.ico - 200 - 548.613µs"
time="2025-08-02 12:04:25" level=info msg="PRI * - 200 - 74.923µs"
time="2025-08-02 12:04:44" level=info msg="GET /.well-known/security.txt - 200 - 305.722µs"
time="2025-08-02 12:13:15" level=info msg="GET /.env - 200 - 607.155µs"
time="2025-08-02 12:28:56" level=info msg="CronChecker: Group 'gemini' has no invalid keys to check."
time="2025-08-02 12:38:34" level=info msg="GET / - 200 - 326.761µs"
time="2025-08-02 12:43:56" level=info msg="CronChecker: Group 'targon' has no invalid keys to check."
time="2025-08-02 13:27:06" level=info msg="GET /.env - 200 - 676.057µs"
time="2025-08-02 13:28:56" level=info msg="CronChecker: Group 'gemini' has no invalid keys to check."
time="2025-08-02 13:43:56" level=info msg="CronChecker: Group 'targon' has no invalid keys to check."
time="2025-08-02 14:29:41" level=info msg="GET / - 200 - 1.460568ms"
time="2025-08-02 14:29:41" level=info msg="GET /assets/logo-BV-_qOqO.png - 200 - 1.882922ms"
time="2025-08-02 14:33:56" level=info msg="CronChecker: Group 'gemini' has no invalid keys to check."
time="2025-08-02 14:48:56" level=info msg="CronChecker: Group 'targon' has no invalid keys to check."
time="2025-08-02 15:00:15" level=info msg="GET / - 200 - 548.563µs"
time="2025-08-02 15:38:56" level=info msg="CronChecker: Group 'gemini' has no invalid keys to check."
time="2025-08-02 15:53:56" level=info msg="CronChecker: Group 'targon' has no invalid keys to check."
time="2025-08-02 16:43:56" level=info msg="CronChecker: Group 'gemini' has no invalid keys to check."
time="2025-08-02 16:53:56" level=info msg="CronChecker: Group 'targon' has no invalid keys to check."
time="2025-08-02 17:43:56" level=info msg="CronChecker: Group 'gemini' has no invalid keys to check."
time="2025-08-02 17:58:56" level=info msg="CronChecker: Group 'targon' has no invalid keys to check."
time="2025-08-02 18:02:11" level=info msg="GET / - 200 - 1.284004ms"
time="2025-08-02 18:48:56" level=info msg="CronChecker: Group 'gemini' has no invalid keys to check."
time="2025-08-02 19:03:56" level=info msg="CronChecker: Group 'targon' has no invalid keys to check."
time="2025-08-02 19:53:56" level=info msg="CronChecker: Group 'gemini' has no invalid keys to check."
time="2025-08-02 20:08:56" level=info msg="CronChecker: Group 'targon' has no invalid keys to check."
time="2025-08-02 20:53:56" level=info msg="CronChecker: Group 'gemini' has no invalid keys to check."
time="2025-08-02 21:08:56" level=info msg="CronChecker: Group 'targon' has no invalid keys to check."
time="2025-08-02 21:58:56" level=info msg="CronChecker: Group 'gemini' has no invalid keys to check."
time="2025-08-02 22:13:56" level=info msg="CronChecker: Group 'targon' has no invalid keys to check."
time="2025-08-02 23:03:56" level=info msg="CronChecker: Group 'gemini' has no invalid keys to check."
time="2025-08-02 23:18:56" level=info msg="CronChecker: Group 'targon' has no invalid keys to check."
time="2025-08-03 00:08:56" level=info msg="CronChecker: Group 'gemini' has no invalid keys to check."
time="2025-08-03 00:18:56" level=info msg="CronChecker: Group 'targon' has no invalid keys to check."
time="2025-08-03 01:08:56" level=info msg="CronChecker: Group 'gemini' has no invalid keys to check."
time="2025-08-03 01:14:01" level=info msg="POST /evm - 200 - 59.603µs"
time="2025-08-03 01:23:56" level=info msg="CronChecker: Group 'targon' has no invalid keys to check."
time="2025-08-03 02:13:56" level=info msg="CronChecker: Group 'gemini' has no invalid keys to check."
time="2025-08-03 02:28:56" level=info msg="CronChecker: Group 'targon' has no invalid keys to check."
time="2025-08-03 03:18:56" level=info msg="CronChecker: Group 'gemini' has no invalid keys to check."
time="2025-08-03 03:28:56" level=info msg="CronChecker: Group 'targon' has no invalid keys to check."
time="2025-08-03 04:18:56" level=info msg="CronChecker: Group 'gemini' has no invalid keys to check."
time="2025-08-03 04:33:56" level=info msg="CronChecker: Group 'targon' has no invalid keys to check."
time="2025-08-03 05:23:56" level=info msg="CronChecker: Group 'gemini' has no invalid keys to check."
time="2025-08-03 05:38:56" level=info msg="CronChecker: Group 'targon' has no invalid keys to check."
time="2025-08-03 06:28:56" level=info msg="CronChecker: Group 'gemini' has no invalid keys to check."
time="2025-08-03 06:43:56" level=info msg="CronChecker: Group 'targon' has no invalid keys to check."
time="2025-08-03 07:28:56" level=info msg="CronChecker: Group 'gemini' has no invalid keys to check."
time="2025-08-03 07:43:56" level=info msg="CronChecker: Group 'targon' has no invalid keys to check."
time="2025-08-03 08:33:56" level=info msg="CronChecker: Group 'gemini' has no invalid keys to check."
time="2025-08-03 08:48:56" level=info msg="CronChecker: Group 'targon' has no invalid keys to check."
time="2025-08-03 09:38:56" level=info msg="CronChecker: Group 'gemini' has no invalid keys to check."
time="2025-08-03 09:53:56" level=info msg="CronChecker: Group 'targon' has no invalid keys to check."
time="2025-08-03 10:43:56" level=info msg="CronChecker: Group 'gemini' has no invalid keys to check."
time="2025-08-03 10:53:56" level=info msg="CronChecker: Group 'targon' has no invalid keys to check."
time="2025-08-03 11:48:27" level=info msg="GET / - 200 - 2.145582ms"
time="2025-08-03 11:48:56" level=info msg="CronChecker: Group 'gemini' has no invalid keys to check."
time="2025-08-03 11:50:41" level=info msg="GET / - 200 - 3.008945ms"
time="2025-08-03 11:58:56" level=info msg="CronChecker: Group 'targon' has no invalid keys to check."
time="2025-08-03 12:53:03" level=info msg="GET / - 200 - 584.844µs"
time="2025-08-03 12:53:07" level=info msg="GET /assets/logo-BV-_qOqO.png - 200 - 1.177524755s"
time="2025-08-03 12:53:17" level=info msg="GET /favicon.ico - 200 - 181.005µs"
time="2025-08-03 12:53:19" level=info msg="PRI * - 200 - 80.264µs"
time="2025-08-03 12:53:30" level=info msg="GET / - 200 - 919.752µs"
time="2025-08-03 12:53:56" level=info msg="CronChecker: Group 'gemini' has no invalid keys to check."
time="2025-08-03 12:55:16" level=info msg="GET /favicon.ico - 200 - 452.652µs"
time="2025-08-03 12:55:19" level=info msg="PRI * - 200 - 89.06µs"
time="2025-08-03 12:55:39" level=info msg="GET /robots.txt - 200 - 218.215µs"
time="2025-08-03 13:03:56" level=info msg="CronChecker: Group 'targon' has no invalid keys to check."
time="2025-08-03 13:58:56" level=info msg="CronChecker: Group 'gemini' has no invalid keys to check."
time="2025-08-03 14:08:56" level=info msg="CronChecker: Group 'targon' has no invalid keys to check."
time="2025-08-03 14:33:26" level=info msg="GET / - 200 - 177.398µs"
time="2025-08-03 14:58:56" level=info msg="CronChecker: Group 'gemini' has no invalid keys to check."
time="2025-08-03 15:08:56" level=info msg="CronChecker: Group 'targon' has no invalid keys to check."
time="2025-08-03 16:03:56" level=info msg="CronChecker: Group 'gemini' has no invalid keys to check."
time="2025-08-03 16:13:56" level=info msg="CronChecker: Group 'targon' has no invalid keys to check."
time="2025-08-03 17:08:56" level=info msg="CronChecker: Group 'gemini' has no invalid keys to check."
time="2025-08-03 17:18:56" level=info msg="CronChecker: Group 'targon' has no invalid keys to check."
time="2025-08-03 18:08:56" level=info msg="CronChecker: Group 'gemini' has no invalid keys to check."
time="2025-08-03 18:18:56" level=info msg="CronChecker: Group 'targon' has no invalid keys to check."
time="2025-08-03 19:08:56" level=info msg="CronChecker: Group 'gemini' has no invalid keys to check."
time="2025-08-03 19:23:56" level=info msg="CronChecker: Group 'targon' has no invalid keys to check."
time="2025-08-03 20:13:56" level=info msg="CronChecker: Group 'gemini' has no invalid keys to check."
time="2025-08-03 20:28:56" level=info msg="CronChecker: Group 'targon' has no invalid keys to check."
time="2025-08-03 21:18:56" level=info msg="CronChecker: Group 'gemini' has no invalid keys to check."
time="2025-08-03 21:33:56" level=info msg="CronChecker: Group 'targon' has no invalid keys to check."
time="2025-08-03 22:18:56" level=info msg="CronChecker: Group 'gemini' has no invalid keys to check."
time="2025-08-03 22:38:56" level=info msg="CronChecker: Group 'targon' has no invalid keys to check."
time="2025-08-03 23:23:56" level=info msg="CronChecker: Group 'gemini' has no invalid keys to check."
time="2025-08-03 23:43:56" level=info msg="CronChecker: Group 'targon' has no invalid keys to check."
time="2025-08-04 00:28:56" level=info msg="CronChecker: Group 'gemini' has no invalid keys to check."
time="2025-08-04 00:48:56" level=info msg="CronChecker: Group 'targon' has no invalid keys to check."
time="2025-08-04 01:28:56" level=info msg="CronChecker: Group 'gemini' has no invalid keys to check."
time="2025-08-04 01:48:56" level=info msg="CronChecker: Group 'targon' has no invalid keys to check."
time="2025-08-04 01:53:49" level=info msg="GET /.git/ - 200 - 2.898052ms"
time="2025-08-04 02:22:19" level=info msg="GET / - 200 - 323.024µs"
time="2025-08-04 02:33:56" level=info msg="CronChecker: Group 'gemini' has no invalid keys to check."
time="2025-08-04 02:53:56" level=info msg="CronChecker: Group 'targon' has no invalid keys to check."
time="2025-08-04 03:38:56" level=info msg="CronChecker: Group 'gemini' has no invalid keys to check."
time="2025-08-04 03:58:56" level=info msg="CronChecker: Group 'targon' has no invalid keys to check."
time="2025-08-04 04:35:32" level=info msg="GET / - 200 - 558.413µs"
time="2025-08-04 04:43:56" level=info msg="CronChecker: Group 'gemini' has no invalid keys to check."
time="2025-08-04 04:58:56" level=info msg="CronChecker: Group 'targon' has no invalid keys to check."
time="2025-08-04 05:43:56" level=info msg="CronChecker: Group 'gemini' has no invalid keys to check."
time="2025-08-04 06:03:56" level=info msg="CronChecker: Group 'targon' has no invalid keys to check."
time="2025-08-04 06:48:56" level=info msg="CronChecker: Group 'gemini' has no invalid keys to check."
time="2025-08-04 07:08:56" level=info msg="CronChecker: Group 'targon' has no invalid keys to check."
time="2025-08-04 07:53:56" level=info msg="CronChecker: Group 'gemini' has no invalid keys to check."
time="2025-08-04 08:13:56" level=info msg="CronChecker: Group 'targon' has no invalid keys to check."
time="2025-08-04 08:53:56" level=info msg="CronChecker: Group 'gemini' has no invalid keys to check."
time="2025-08-04 09:13:56" level=info msg="CronChecker: Group 'targon' has no invalid keys to check."
time="2025-08-04 09:58:56" level=info msg="CronChecker: Group 'gemini' has no invalid keys to check."
time="2025-08-04 10:13:56" level=info msg="CronChecker: Group 'targon' has no invalid keys to check."
time="2025-08-04 11:03:56" level=info msg="CronChecker: Group 'gemini' has no invalid keys to check."
time="2025-08-04 11:12:51" level=info msg="GET / - 200 - 1.032285ms"
time="2025-08-04 11:14:50" level=info msg="GET / - 200 - 3.122422ms"
time="2025-08-04 11:18:56" level=info msg="CronChecker: Group 'targon' has no invalid keys to check."
time="2025-08-04 11:58:26" level=info msg="GET / - 200 - 270.584µs"
time="2025-08-04 11:58:49" level=info msg="GET /assets/logo-bv-_qoqo.png - 200 - 72.778µs"
time="2025-08-04 12:08:56" level=info msg="CronChecker: Group 'gemini' has no invalid keys to check."
time="2025-08-04 12:19:57" level=info msg="GET / - 200 - 509.279µs"
time="2025-08-04 12:23:56" level=info msg="CronChecker: Group 'targon' has no invalid keys to check."
time="2025-08-04 12:39:42" level=info msg="GET / - 200 - 490.635µs"
time="2025-08-04 13:08:56" level=info msg="CronChecker: Group 'gemini' has no invalid keys to check."
time="2025-08-04 13:28:56" level=info msg="CronChecker: Group 'targon' has no invalid keys to check."
time="2025-08-04 13:57:16" level=info msg="GET / - 200 - 272.188µs"
time="2025-08-04 13:57:20" level=info msg="GET /assets/logo-BV-_qOqO.png - 200 - 462.453332ms"
time="2025-08-04 13:57:42" level=info msg="GET /favicon.ico - 200 - 198.548µs"
time="2025-08-04 13:58:02" level=info msg="PRI * - 200 - 71.376µs"
time="2025-08-04 13:58:26" level=info msg="GET / - 200 - 346.148µs"
time="2025-08-04 13:59:49" level=info msg="GET /favicon.ico - 200 - 225.539µs"
time="2025-08-04 13:59:49" level=info msg="PRI * - 200 - 76.285µs"
time="2025-08-04 14:12:24" level=info msg="Shutting down server..."
time="2025-08-04 14:12:24" level=info msg="HTTP server has been shut down."
time="2025-08-04 14:12:24" level=info msg="cache syncer stopped." syncer=groups
time="2025-08-04 14:12:24" level=info msg="LogCleanupService stopped gracefully."
time="2025-08-04 14:12:24" level=info msg="CronChecker stopped gracefully."
time="2025-08-04 14:12:24" level=info msg="RequestLogService stopped gracefully."
time="2025-08-04 14:12:24" level=info msg="cache syncer stopped." syncer=system_settings
time="2025-08-04 14:12:24" level=info msg="All background services stopped."
time="2025-08-04 14:12:24" level=info msg="Server exited gracefully"
time="2025-08-04 14:32:14" level=info msg="Starting as Master Node."
time="2025-08-04 14:32:14" level=info msg="Database auto-migration completed."
time="2025-08-04 14:32:14" level=info msg="System settings initialized in DB."
time="2025-08-04 14:32:14" level=info
time="2025-08-04 14:32:14" level=info msg="========= System Settings ========="
time="2025-08-04 14:32:14" level=info msg="  --- Basic Settings ---"
time="2025-08-04 14:32:14" level=info msg="    App URL: https://load.ainima.de"
time="2025-08-04 14:32:14" level=info msg="    Request Log Retention: 7 days"
time="2025-08-04 14:32:14" level=info msg="    Request Log Write Interval: 1 minutes"
time="2025-08-04 14:32:14" level=info msg="  --- Request Behavior ---"
time="2025-08-04 14:32:14" level=info msg="    Request Timeout: 600 seconds"
time="2025-08-04 14:32:14" level=info msg="    Connect Timeout: 15 seconds"
time="2025-08-04 14:32:14" level=info msg="    Response Header Timeout: 600 seconds"
time="2025-08-04 14:32:14" level=info msg="    Idle Connection Timeout: 120 seconds"
time="2025-08-04 14:32:14" level=info msg="    Max Idle Connections: 100"
time="2025-08-04 14:32:14" level=info msg="    Max Idle Connections Per Host: 50"
time="2025-08-04 14:32:14" level=info msg="  --- Key & Group Behavior ---"
time="2025-08-04 14:32:14" level=info msg="    Max Retries: 3"
time="2025-08-04 14:32:14" level=info msg="    Blacklist Threshold: 3"
time="2025-08-04 14:32:14" level=info msg="    Key Validation Interval: 60 minutes"
time="2025-08-04 14:32:14" level=info msg="===================================="
time="2025-08-04 14:32:14" level=info
time="2025-08-04 14:32:14" level=info msg="cache reloaded successfully" syncer=system_settings
time="2025-08-04 14:32:14" level=info msg="Updating active key lists for all groups..."
time="2025-08-04 14:32:14" level=info
time="2025-08-04 14:32:14" level=info msg="======= Server Configuration ======="
time="2025-08-04 14:32:14" level=info msg="  --- Server ---"
time="2025-08-04 14:32:14" level=info msg="    Listen Address: 0.0.0.0:3001"
time="2025-08-04 14:32:14" level=info msg="    Graceful Shutdown Timeout: 10 seconds"
time="2025-08-04 14:32:14" level=info msg="    Read Timeout: 60 seconds"
time="2025-08-04 14:32:14" level=info msg="    Write Timeout: 600 seconds"
time="2025-08-04 14:32:14" level=info msg="    Idle Timeout: 120 seconds"
time="2025-08-04 14:32:14" level=info msg="  --- Performance ---"
time="2025-08-04 14:32:14" level=info msg="    Max Concurrent Requests: 100"
time="2025-08-04 14:32:14" level=info msg="  --- Security ---"
time="2025-08-04 14:32:14" level=info msg="    Authentication: enabled (key loaded)"
time="2025-08-04 14:32:14" level=info msg="    CORS: enabled (Origins: *)"
time="2025-08-04 14:32:14" level=info msg="  --- Logging ---"
time="2025-08-04 14:32:14" level=info msg="    Log Level: info"
time="2025-08-04 14:32:14" level=info msg="    Log Format: text"
time="2025-08-04 14:32:14" level=info msg="    File Logging: true"
time="2025-08-04 14:32:14" level=info msg="    Log File Path: ./data/logs/app.log"
time="2025-08-04 14:32:14" level=info msg="  --- Dependencies ---"
time="2025-08-04 14:32:14" level=info msg="    Database: configured"
time="2025-08-04 14:32:14" level=info msg="    Redis: configured"
time="2025-08-04 14:32:14" level=info msg="===================================="
time="2025-08-04 14:32:14" level=info
time="2025-08-04 14:32:14" level=info msg="cache reloaded successfully" syncer=groups
time="2025-08-04 14:32:14" level=info msg="GPT-Load proxy server started successfully on Version: v1.0.17"
time="2025-08-04 14:32:14" level=info msg="Server address: http://0.0.0.0:3001"
time="2025-08-04 14:32:14" level=info
time="2025-08-04 14:32:14" level=info msg="CronChecker: Group 'gemini' has no invalid keys to check."
time="2025-08-04 14:32:14" level=info msg="CronChecker: Group 'targon' has no invalid keys to check."
time="2025-08-04 14:38:36" level=info msg="GET / - 200 - 744.627µs"
time="2025-08-04 14:38:36" level=info msg="GET /assets/index-Gswog8ov.css - 200 - 509.118µs"
time="2025-08-04 14:38:36" level=info msg="GET /assets/index-SPLiOoPZ.js - 200 - 62.717535ms"
time="2025-08-04 14:38:39" level=info msg="GET /assets/Dashboard-ZqIXRWdq.js - 200 - 789.652µs"
time="2025-08-04 14:38:39" level=info msg="GET /assets/display-DuIJKIJ5.js - 200 - 278.58µs"
time="2025-08-04 14:38:39" level=info msg="GET /assets/Dashboard-DHOcOEH2.css - 200 - 1.850541ms"
time="2025-08-04 14:38:40" level=info msg="GET /api/tasks/status - 200 - 507.636µs"
time="2025-08-04 14:38:40" level=info msg="GET /api/groups/list - 200 - 2.478104ms"
time="2025-08-04 14:38:40" level=info msg="GET /api/dashboard/chart - 200 - 1.637856ms"
time="2025-08-04 14:38:40" level=info msg="GET /api/dashboard/stats - 200 - 30.703862ms"
time="2025-08-04 14:38:40" level=info msg="GET /assets/logo-BV-_qOqO.png - 200 - 1.232655ms"
time="2025-08-04 14:38:42" level=info msg="GET /assets/ProxyKeysInput-CoLmi6p2.css - 200 - 567.309µs"
time="2025-08-04 14:38:42" level=info msg="GET /assets/Keys-CCps4UaT.css - 200 - 1.007136ms"
time="2025-08-04 14:38:42" level=info msg="GET /assets/Keys-Cvs7q-Qm.js - 200 - 2.430083ms"
time="2025-08-04 14:38:42" level=info msg="GET /assets/ProxyKeysInput-qloCXNPS.js - 200 - 481.586µs"
time="2025-08-04 14:38:42" level=info msg="GET /assets/Search-oHPKo0PA.js - 200 - 547.162µs"
time="2025-08-04 14:38:43" level=info msg="GET /api/groups/config-options - 200 - 446.71µs"
time="2025-08-04 14:38:43" level=info msg="GET /api/groups - 200 - 678.401µs"
time="2025-08-04 14:38:43" level=info msg="GET /api/groups/2/stats - 200 - 22.165227ms"
time="2025-08-04 14:38:43" level=info msg="GET /api/keys?group_id=2&page=1&page_size=12 - 200 - 24.184359ms"
time="2025-08-04 14:38:45" level=info msg="GET /api/groups/1/stats - 200 - 7.060552ms"
time="2025-08-04 14:38:45" level=info msg="GET /api/keys?group_id=1&page=1&page_size=12 - 200 - 10.822821ms"
time="2025-08-04 14:38:50" level=info msg="POST /api/keys/test-multiple - 200 - 1.671941809s"
time="2025-08-04 14:38:50" level=info msg="GET /api/keys?group_id=1&page=1&page_size=12 - 200 - 6.984538ms"
time="2025-08-04 14:38:50" level=info msg="GET /api/groups/1/stats - 200 - 10.068154ms"
time="2025-08-04 14:38:55" level=info msg="GET /api/keys?group_id=2&page=1&page_size=12 - 200 - 6.186719ms"
time="2025-08-04 14:38:55" level=info msg="GET /api/groups/2/stats - 200 - 6.557656ms"
time="2025-08-04 14:38:57" level=info msg="GET /api/groups/1/stats - 200 - 6.114723ms"
time="2025-08-04 14:38:57" level=info msg="GET /api/keys?group_id=1&page=1&page_size=12 - 200 - 9.259135ms"
time="2025-08-04 14:39:14" level=info msg="GET /api/groups/2/stats - 200 - 4.373181ms"
time="2025-08-04 14:39:14" level=info msg="GET /api/keys?group_id=2&page=1&page_size=12 - 200 - 6.87538ms"
time="2025-08-04 14:39:16" level=info msg="GET /api/groups/1/stats - 200 - 9.567933ms"
time="2025-08-04 14:39:16" level=info msg="GET /api/keys?group_id=1&page=1&page_size=12 - 200 - 9.914923ms"
time="2025-08-04 14:40:50" level=info msg="Shutting down server..."
time="2025-08-04 14:40:50" level=info msg="HTTP server has been shut down."
time="2025-08-04 14:40:50" level=info msg="cache syncer stopped." syncer=system_settings
time="2025-08-04 14:40:50" level=info msg="cache syncer stopped." syncer=groups
time="2025-08-04 14:40:50" level=info msg="CronChecker stopped gracefully."
time="2025-08-04 14:40:50" level=info msg="LogCleanupService stopped gracefully."
time="2025-08-04 14:40:50" level=info msg="RequestLogService stopped gracefully."
time="2025-08-04 14:40:50" level=info msg="All background services stopped."
time="2025-08-04 14:40:50" level=info msg="Server exited gracefully"
time="2025-08-04 14:40:51" level=info msg="Starting as Master Node."
time="2025-08-04 14:40:51" level=info msg="Database auto-migration completed."
time="2025-08-04 14:40:51" level=info msg="System settings initialized in DB."
time="2025-08-04 14:40:51" level=info
time="2025-08-04 14:40:51" level=info msg="========= System Settings ========="
time="2025-08-04 14:40:51" level=info msg="  --- Basic Settings ---"
time="2025-08-04 14:40:51" level=info msg="    App URL: https://load.ainima.de"
time="2025-08-04 14:40:51" level=info msg="    Request Log Retention: 7 days"
time="2025-08-04 14:40:51" level=info msg="    Request Log Write Interval: 1 minutes"
time="2025-08-04 14:40:51" level=info msg="  --- Request Behavior ---"
time="2025-08-04 14:40:51" level=info msg="    Request Timeout: 600 seconds"
time="2025-08-04 14:40:51" level=info msg="    Connect Timeout: 15 seconds"
time="2025-08-04 14:40:51" level=info msg="    Response Header Timeout: 600 seconds"
time="2025-08-04 14:40:51" level=info msg="    Idle Connection Timeout: 120 seconds"
time="2025-08-04 14:40:51" level=info msg="    Max Idle Connections: 100"
time="2025-08-04 14:40:51" level=info msg="    Max Idle Connections Per Host: 50"
time="2025-08-04 14:40:51" level=info msg="  --- Key & Group Behavior ---"
time="2025-08-04 14:40:51" level=info msg="    Max Retries: 3"
time="2025-08-04 14:40:51" level=info msg="    Blacklist Threshold: 3"
time="2025-08-04 14:40:51" level=info msg="    Key Validation Interval: 60 minutes"
time="2025-08-04 14:40:51" level=info msg="===================================="
time="2025-08-04 14:40:51" level=info
time="2025-08-04 14:40:51" level=info msg="cache reloaded successfully" syncer=system_settings
time="2025-08-04 14:40:51" level=info
time="2025-08-04 14:40:51" level=info msg="======= Server Configuration ======="
time="2025-08-04 14:40:51" level=info msg="  --- Server ---"
time="2025-08-04 14:40:51" level=info msg="    Listen Address: 0.0.0.0:3001"
time="2025-08-04 14:40:51" level=info msg="    Graceful Shutdown Timeout: 10 seconds"
time="2025-08-04 14:40:51" level=info msg="    Read Timeout: 60 seconds"
time="2025-08-04 14:40:51" level=info msg="    Write Timeout: 600 seconds"
time="2025-08-04 14:40:51" level=info msg="    Idle Timeout: 120 seconds"
time="2025-08-04 14:40:51" level=info msg="  --- Performance ---"
time="2025-08-04 14:40:51" level=info msg="    Max Concurrent Requests: 100"
time="2025-08-04 14:40:51" level=info msg="  --- Security ---"
time="2025-08-04 14:40:51" level=info msg="    Authentication: enabled (key loaded)"
time="2025-08-04 14:40:51" level=info msg="    CORS: enabled (Origins: *)"
time="2025-08-04 14:40:51" level=info msg="  --- Logging ---"
time="2025-08-04 14:40:51" level=info msg="    Log Level: info"
time="2025-08-04 14:40:51" level=info msg="    Log Format: text"
time="2025-08-04 14:40:51" level=info msg="    File Logging: true"
time="2025-08-04 14:40:51" level=info msg="    Log File Path: ./data/logs/app.log"
time="2025-08-04 14:40:51" level=info msg="  --- Dependencies ---"
time="2025-08-04 14:40:51" level=info msg="    Database: configured"
time="2025-08-04 14:40:51" level=info msg="    Redis: configured"
time="2025-08-04 14:40:51" level=info msg="===================================="
time="2025-08-04 14:40:51" level=info
time="2025-08-04 14:40:51" level=info msg="cache reloaded successfully" syncer=groups
time="2025-08-04 14:40:51" level=info msg="GPT-Load proxy server started successfully on Version: v1.0.18"
time="2025-08-04 14:40:51" level=info msg="Server address: http://0.0.0.0:3001"
time="2025-08-04 14:40:51" level=info
time="2025-08-04 14:42:32" level=info msg="GET /assets/Settings-bVufNCH9.js - 200 - 512.073µs"
time="2025-08-04 14:42:46" level=info msg="GET /keys?groupId=1 - 200 - 2.588095ms"
time="2025-08-04 14:42:47" level=info msg="GET /assets/index-BoejvLdB.css - 200 - 906.113µs"
time="2025-08-04 14:42:47" level=info msg="GET /assets/index-Bsu5oAg6.js - 200 - 63.529671ms"
time="2025-08-04 14:42:49" level=info msg="GET /assets/Search-CWmTw_e5.js - 200 - 435.378µs"
time="2025-08-04 14:42:49" level=info msg="GET /assets/Keys-TvQrChfu.css - 200 - 753.904µs"
time="2025-08-04 14:42:49" level=info msg="GET /assets/ProxyKeysInput-BSuO8Kst.js - 200 - 449.845µs"
time="2025-08-04 14:42:49" level=info msg="GET /assets/Keys-pxHFf5dG.js - 200 - 2.26003ms"
time="2025-08-04 14:42:50" level=info msg="GET /api/groups/config-options - 200 - 431.101µs"
time="2025-08-04 14:42:50" level=info msg="GET /api/tasks/status - 200 - 483.148µs"
time="2025-08-04 14:42:50" level=info msg="GET /api/groups - 200 - 1.882853ms"
time="2025-08-04 14:42:50" level=info msg="GET /api/groups/1/stats - 200 - 20.886629ms"
time="2025-08-04 14:42:50" level=info msg="GET /api/keys?group_id=1&page=1&page_size=12 - 200 - 22.726268ms"
time="2025-08-04 14:42:52" level=info msg="GET /assets/Settings-DfDSODzu.js - 200 - 504.26µs"
time="2025-08-04 14:42:52" level=info msg="GET /api/settings - 200 - 233.033µs"
time="2025-08-04 14:43:02" level=info msg="GET /api/groups/config-options - 200 - 212.354µs"
time="2025-08-04 14:43:02" level=info msg="GET /api/groups - 200 - 676.347µs"
time="2025-08-04 14:43:02" level=info msg="GET /api/groups/2/stats - 200 - 8.155066ms"
time="2025-08-04 14:43:02" level=info msg="GET /api/keys?group_id=2&page=1&page_size=12 - 200 - 8.920091ms"
time="2025-08-04 14:44:03" level=warning msg="POST /proxy/gemini/v1/chat/completions - 404 - 152.587557ms"
time="2025-08-04 14:44:29" level=warning msg="POST /proxy/gemini/v1/v1/chat/completions - 404 - 112.765822ms"
time="2025-08-04 14:44:51" level=info msg="Successfully flushed 2 request logs."
time="2025-08-04 14:45:04" level=info msg="POST /proxy/gemini/v1beta/models/gemini-2.5-pro:generateContent - 200 - 6.519715831s"
time="2025-08-04 14:45:51" level=info msg="Successfully flushed 1 request logs."
time="2025-08-04 14:46:29" level=info msg="POST /proxy/gemini/v1beta/models/gemini-2.5-flash:generateContent - 200 - 367.843017ms"
time="2025-08-04 14:46:51" level=info msg="Successfully flushed 1 request logs."
time="2025-08-04 14:47:08" level=info msg="POST /proxy/gemini/v1beta/models/gemini-2.5-flash-preview-05-20:generateContent - 200 - 499.863206ms"
time="2025-08-04 14:47:51" level=info msg="Successfully flushed 1 request logs."
time="2025-08-04 14:48:11" level=warning msg="POST /proxy/gemini/v1beta/models/gemini-2.0-flash-exp:generateContent - 400 - 225.978512ms"
time="2025-08-04 14:48:29" level=info msg="POST /proxy/gemini/v1beta/models/gemini-2.5-pro:generateContent - 200 - 11.858238167s"
time="2025-08-04 14:48:51" level=info msg="Successfully flushed 2 request logs."
time="2025-08-04 14:48:52" level=warning msg="POST /proxy/gemini/v1beta/models/gemini-2.5-flash-exp:generateContent - 404 - 205.380011ms"
time="2025-08-04 14:49:05" level=info msg="POST /proxy/gemini/v1beta/models/gemini-2.5-flash-preview-05-20:generateContent - 200 - 548.318329ms"
time="2025-08-04 14:49:11" level=info msg="GET /assets/Logs-jx6kyE3D.css - 200 - 1.123468ms"
time="2025-08-04 14:49:11" level=info msg="GET /assets/Logs-DOB48F16.js - 200 - 1.087961ms"
time="2025-08-04 14:49:12" level=info msg="GET /api/logs?page=1&page_size=15 - 200 - 1.108209ms"
time="2025-08-04 14:49:51" level=info msg="Successfully flushed 2 request logs."
time="2025-08-04 14:51:07" level=info msg="GET / - 200 - 894.191µs"
time="2025-08-04 14:51:08" level=info msg="GET /assets/Dashboard-CnqTJPiu.css - 200 - 1.028898ms"
time="2025-08-04 14:51:08" level=info msg="GET /assets/Dashboard-C9hBWr32.js - 200 - 955.779µs"
time="2025-08-04 14:51:09" level=info msg="GET /api/groups/list - 200 - 478.42µs"
time="2025-08-04 14:51:09" level=info msg="GET /api/tasks/status - 200 - 324.308µs"
time="2025-08-04 14:51:09" level=info msg="GET /api/dashboard/chart - 200 - 1.500325ms"
time="2025-08-04 14:51:09" level=info msg="GET /api/dashboard/stats - 200 - 17.21174ms"
time="2025-08-04 14:51:11" level=info msg="GET /api/settings - 200 - 330.569µs"
time="2025-08-04 14:51:16" level=info msg="GET /api/groups/list - 200 - 592.387µs"
time="2025-08-04 14:51:16" level=info msg="GET /api/dashboard/chart - 200 - 580.044µs"
time="2025-08-04 14:51:16" level=info msg="GET /api/dashboard/stats - 200 - 5.958208ms"
time="2025-08-04 15:01:08" level=info msg="GET / - 200 - 696.846µs"
time="2025-08-04 15:17:17" level=info msg="Shutting down server..."
time="2025-08-04 15:17:17" level=info msg="HTTP server has been shut down."
time="2025-08-04 15:17:17" level=info msg="LogCleanupService stopped gracefully."
time="2025-08-04 15:17:17" level=info msg="CronChecker stopped gracefully."
time="2025-08-04 15:17:17" level=info msg="cache syncer stopped." syncer=system_settings
time="2025-08-04 15:17:17" level=info msg="cache syncer stopped." syncer=groups
time="2025-08-04 15:17:17" level=info msg="RequestLogService stopped gracefully."
time="2025-08-04 15:17:17" level=info msg="All background services stopped."
time="2025-08-04 15:17:17" level=info msg="Server exited gracefully"
time="2025-08-04 15:17:17" level=info msg="Starting as Master Node."
time="2025-08-04 15:17:17" level=info msg="Database auto-migration completed."
time="2025-08-04 15:17:17" level=info msg="System settings initialized in DB."
time="2025-08-04 15:17:17" level=info
time="2025-08-04 15:17:17" level=info msg="========= System Settings ========="
time="2025-08-04 15:17:17" level=info msg="  --- Basic Settings ---"
time="2025-08-04 15:17:17" level=info msg="    App URL: https://load.ainima.de"
time="2025-08-04 15:17:17" level=info msg="    Request Log Retention: 7 days"
time="2025-08-04 15:17:17" level=info msg="    Request Log Write Interval: 1 minutes"
time="2025-08-04 15:17:17" level=info msg="  --- Request Behavior ---"
time="2025-08-04 15:17:17" level=info msg="    Request Timeout: 600 seconds"
time="2025-08-04 15:17:17" level=info msg="    Connect Timeout: 15 seconds"
time="2025-08-04 15:17:17" level=info msg="    Response Header Timeout: 600 seconds"
time="2025-08-04 15:17:17" level=info msg="    Idle Connection Timeout: 120 seconds"
time="2025-08-04 15:17:17" level=info msg="    Max Idle Connections: 100"
time="2025-08-04 15:17:17" level=info msg="    Max Idle Connections Per Host: 50"
time="2025-08-04 15:17:17" level=info msg="  --- Key & Group Behavior ---"
time="2025-08-04 15:17:17" level=info msg="    Max Retries: 3"
time="2025-08-04 15:17:17" level=info msg="    Blacklist Threshold: 3"
time="2025-08-04 15:17:17" level=info msg="    Key Validation Interval: 60 minutes"
time="2025-08-04 15:17:17" level=info msg="===================================="
time="2025-08-04 15:17:17" level=info
time="2025-08-04 15:17:17" level=info msg="cache reloaded successfully" syncer=system_settings
time="2025-08-04 15:17:17" level=info
time="2025-08-04 15:17:17" level=info msg="======= Server Configuration ======="
time="2025-08-04 15:17:17" level=info msg="  --- Server ---"
time="2025-08-04 15:17:17" level=info msg="    Listen Address: 0.0.0.0:3001"
time="2025-08-04 15:17:17" level=info msg="    Graceful Shutdown Timeout: 10 seconds"
time="2025-08-04 15:17:17" level=info msg="    Read Timeout: 60 seconds"
time="2025-08-04 15:17:17" level=info msg="    Write Timeout: 600 seconds"
time="2025-08-04 15:17:17" level=info msg="    Idle Timeout: 120 seconds"
time="2025-08-04 15:17:17" level=info msg="  --- Performance ---"
time="2025-08-04 15:17:17" level=info msg="    Max Concurrent Requests: 100"
time="2025-08-04 15:17:17" level=info msg="  --- Security ---"
time="2025-08-04 15:17:17" level=info msg="    Authentication: enabled (key loaded)"
time="2025-08-04 15:17:17" level=info msg="    CORS: enabled (Origins: *)"
time="2025-08-04 15:17:17" level=info msg="  --- Logging ---"
time="2025-08-04 15:17:17" level=info msg="    Log Level: info"
time="2025-08-04 15:17:17" level=info msg="    Log Format: text"
time="2025-08-04 15:17:17" level=info msg="    File Logging: true"
time="2025-08-04 15:17:17" level=info msg="    Log File Path: ./data/logs/app.log"
time="2025-08-04 15:17:17" level=info msg="  --- Dependencies ---"
time="2025-08-04 15:17:17" level=info msg="    Database: configured"
time="2025-08-04 15:17:17" level=info msg="    Redis: configured"
time="2025-08-04 15:17:17" level=info msg="===================================="
time="2025-08-04 15:17:17" level=info
time="2025-08-04 15:17:17" level=info msg="cache reloaded successfully" syncer=groups
time="2025-08-04 15:17:17" level=info msg="GPT-Load proxy server started successfully on Version: v1.0.18"
time="2025-08-04 15:17:17" level=info msg="Server address: http://0.0.0.0:3001"
time="2025-08-04 15:17:17" level=info
time="2025-08-04 15:19:12" level=info msg="GET / - 200 - 593.169µs"
time="2025-08-04 15:19:13" level=info msg="GET /assets/logo-BV-_qOqO.png - 200 - 70.389888ms"
time="2025-08-04 15:25:52" level=info msg="POST /proxy/gemini/v1beta/models/gemini-2.5-pro:generateContent - 200 - 9.553089296s"
time="2025-08-04 15:25:53" level=info msg="POST /proxy/gemini/v1beta/models/gemini-2.5-flash:generateContent - 200 - 382.298059ms"
time="2025-08-04 15:25:57" level=info msg="POST /proxy/gemini/v1beta/models/gemini-2.5-flash-preview-05-20:generateContent - 200 - 3.555662989s"
time="2025-08-04 15:26:17" level=info msg="Successfully flushed 3 request logs."
time="2025-08-04 15:32:17" level=info msg="CronChecker: Group 'gemini' has no invalid keys to check."
time="2025-08-04 15:32:17" level=info msg="CronChecker: Group 'targon' has no invalid keys to check."
time="2025-08-04 15:59:14" level=info msg="GET / - 200 - 382.258µs"
time="2025-08-04 15:59:14" level=info msg="GET /assets/logo-BV-_qOqO.png - 200 - 1.011707ms"
time="2025-08-04 16:32:04" level=info msg="GET / - 200 - 625.68µs"
time="2025-08-04 16:32:06" level=info msg="GET /assets/Dashboard-CnqTJPiu.css - 200 - 420.43µs"
time="2025-08-04 16:32:06" level=info msg="GET /assets/Dashboard-C9hBWr32.js - 200 - 1.056711ms"
time="2025-08-04 16:32:06" level=info msg="GET /api/tasks/status - 200 - 644.557µs"
time="2025-08-04 16:32:06" level=info msg="GET /api/groups/list - 200 - 1.070538ms"
time="2025-08-04 16:32:06" level=info msg="GET /api/dashboard/stats - 200 - 29.237315ms"
time="2025-08-04 16:32:07" level=info msg="GET /api/dashboard/chart - 200 - 643.825µs"
time="2025-08-04 16:32:53" level=info msg="GET /assets/Keys-pxHFf5dG.js - 200 - 2.860105ms"
time="2025-08-04 16:32:53" level=info msg="GET /assets/Search-CWmTw_e5.js - 200 - 604.762µs"
time="2025-08-04 16:32:53" level=info msg="GET /assets/Keys-TvQrChfu.css - 200 - 1.138787ms"
time="2025-08-04 16:32:55" level=info msg="GET /api/groups - 200 - 860.759µs"
time="2025-08-04 16:32:55" level=info msg="GET /api/groups/config-options - 200 - 483.791µs"
time="2025-08-04 16:32:55" level=info msg="GET /api/keys?group_id=2&page=1&page_size=12 - 200 - 7.980399ms"
time="2025-08-04 16:32:55" level=info msg="GET /api/groups/2/stats - 200 - 10.562973ms"
time="2025-08-04 16:32:57" level=info msg="GET /api/keys?group_id=1&page=1&page_size=12 - 200 - 9.325318ms"
time="2025-08-04 16:32:57" level=info msg="GET /api/groups/1/stats - 200 - 10.259737ms"
time="2025-08-04 16:32:58" level=info msg="GET /api/keys?group_id=2&page=1&page_size=12 - 200 - 5.532019ms"
time="2025-08-04 16:32:58" level=info msg="GET /api/groups/2/stats - 200 - 6.892759ms"
time="2025-08-04 16:33:06" level=info msg="POST /api/keys/add-async - 200 - 72.044797ms"
time="2025-08-04 16:33:06" level=info msg="GET /api/tasks/status - 200 - 353.542µs"
time="2025-08-04 16:33:08" level=info msg="GET /api/tasks/status - 200 - 554.114µs"
time="2025-08-04 16:33:09" level=info msg="GET /api/groups/2/stats - 200 - 7.490657ms"
time="2025-08-04 16:33:09" level=info msg="GET /api/keys?group_id=2&page=1&page_size=12 - 200 - 9.704479ms"
time="2025-08-04 16:33:17" level=info msg="POST /api/keys/validate-group - 200 - 34.605702ms"
time="2025-08-04 16:33:17" level=info msg="Starting manual validation" group=gemini status=all
time="2025-08-04 16:33:17" level=info msg="GET /api/tasks/status - 200 - 273.32µs"
time="2025-08-04 16:33:18" level=info msg="GET /api/tasks/status - 200 - 372.378µs"
time="2025-08-04 16:33:19" level=error msg="Failed to handle key success" error="failed to update key in DB: database is locked (5) (SQLITE_BUSY)" keyID=46
time="2025-08-04 16:33:20" level=error msg="Failed to handle key success" error="failed to update key in DB: database is locked (5) (SQLITE_BUSY)" keyID=68
time="2025-08-04 16:33:20" level=info msg="GET /api/tasks/status - 200 - 545.739µs"
time="2025-08-04 16:33:21" level=error msg="Failed to handle key success" error="failed to update key in DB: database is locked (5) (SQLITE_BUSY)" keyID=85
time="2025-08-04 16:33:21" level=info msg="GET /api/tasks/status - 200 - 1.425634ms"
time="2025-08-04 16:33:22" level=error msg="Failed to handle key success" error="failed to update key in DB: database is locked (5) (SQLITE_BUSY)" keyID=113
time="2025-08-04 16:33:23" level=error msg="Failed to handle key success" error="failed to update key in DB: database is locked (5) (SQLITE_BUSY)" keyID=132
time="2025-08-04 16:33:23" level=info msg="GET /api/tasks/status - 200 - 393.89µs"
time="2025-08-04 16:33:25" level=info msg="GET /api/tasks/status - 200 - 412.655µs"
time="2025-08-04 16:33:26" level=error msg="Failed to handle key success" error="failed to update key in DB: database is locked (5) (SQLITE_BUSY)" keyID=203
time="2025-08-04 16:33:27" level=error msg="Failed to handle key success" error="failed to update key in DB: database is locked (5) (SQLITE_BUSY)" keyID=218
time="2025-08-04 16:33:27" level=error msg="Failed to handle key success" error="failed to update key in DB: database is locked (5) (SQLITE_BUSY)" keyID=231
time="2025-08-04 16:33:27" level=info msg="GET /api/tasks/status - 200 - 244.174µs"
time="2025-08-04 16:33:27" level=error msg="Failed to handle key success" error="failed to update key in DB: database is locked (5) (SQLITE_BUSY)" keyID=241
time="2025-08-04 16:33:29" level=info msg="GET /api/tasks/status - 200 - 438.565µs"
time="2025-08-04 16:33:31" level=error msg="Failed to handle key success" error="failed to update key in DB: database is locked (5) (SQLITE_BUSY)" keyID=307
time="2025-08-04 16:33:31" level=info msg="GET /api/tasks/status - 200 - 300.884µs"
time="2025-08-04 16:33:31" level=error msg="Failed to handle key success" error="failed to update key in DB: database is locked (5) (SQLITE_BUSY)" keyID=313
time="2025-08-04 16:33:32" level=error msg="Failed to handle key success" error="failed to update key in DB: database is locked (5) (SQLITE_BUSY)" keyID=310
time="2025-08-04 16:33:33" level=info msg="GET /api/tasks/status - 200 - 397.286µs"
time="2025-08-04 16:33:34" level=error msg="Failed to handle key success" error="failed to update key in DB: database is locked (5) (SQLITE_BUSY)" keyID=374
time="2025-08-04 16:33:35" level=error msg="Failed to handle key success" error="failed to update key in DB: database is locked (5) (SQLITE_BUSY)" keyID=382
time="2025-08-04 16:33:35" level=info msg="GET /api/tasks/status - 200 - 443.936µs"
time="2025-08-04 16:33:37" level=info msg="GET /api/tasks/status - 200 - 340.799µs"
time="2025-08-04 16:33:37" level=error msg="Failed to handle key success" error="failed to update key in DB: database is locked (5) (SQLITE_BUSY)" keyID=442
time="2025-08-04 16:33:38" level=error msg="Failed to handle key success" error="failed to update key in DB: database is locked (5) (SQLITE_BUSY)" keyID=458
time="2025-08-04 16:33:38" level=error msg="Failed to handle key success" error="failed to update key in DB: database is locked (5) (SQLITE_BUSY)" keyID=455
time="2025-08-04 16:33:39" level=info msg="GET /api/tasks/status - 200 - 334.807µs"
time="2025-08-04 16:33:41" level=error msg="Failed to handle key success" error="failed to update key in DB: database is locked (5) (SQLITE_BUSY)" keyID=518
time="2025-08-04 16:33:41" level=info msg="GET /api/tasks/status - 200 - 257.581µs"
time="2025-08-04 16:33:42" level=error msg="Failed to handle key success" error="failed to update key in DB: database is locked (5) (SQLITE_BUSY)" keyID=528
time="2025-08-04 16:33:42" level=error msg="Failed to handle key success" error="failed to update key in DB: database is locked (5) (SQLITE_BUSY)" keyID=539
time="2025-08-04 16:33:42" level=error msg="Failed to handle key success" error="failed to update key in DB: database is locked (5) (SQLITE_BUSY)" keyID=544
time="2025-08-04 16:33:43" level=info msg="GET /api/tasks/status - 200 - 362.169µs"
time="2025-08-04 16:33:44" level=error msg="Failed to handle key success" error="failed to update key in DB: database is locked (5) (SQLITE_BUSY)" keyID=585
time="2025-08-04 16:33:45" level=info msg="GET /api/tasks/status - 200 - 381.235µs"
time="2025-08-04 16:33:46" level=error msg="Failed to handle key success" error="failed to update key in DB: database is locked (5) (SQLITE_BUSY)" keyID=620
time="2025-08-04 16:33:47" level=info msg="GET /api/tasks/status - 200 - 351.6µs"
time="2025-08-04 16:33:49" level=info msg="GET /api/tasks/status - 200 - 341.52µs"
time="2025-08-04 16:33:51" level=info msg="GET /api/tasks/status - 200 - 493.891µs"
time="2025-08-04 16:33:53" level=info msg="GET /api/tasks/status - 200 - 256.277µs"
time="2025-08-04 16:33:55" level=info msg="GET /api/tasks/status - 200 - 230.017µs"
time="2025-08-04 16:33:57" level=info msg="GET /api/tasks/status - 200 - 322.313µs"
time="2025-08-04 16:33:59" level=info msg="GET /api/tasks/status - 200 - 537.913µs"
time="2025-08-04 16:34:01" level=info msg="GET /api/tasks/status - 200 - 379.943µs"
time="2025-08-04 16:34:02" level=error msg="Failed to handle key failure" error="failed to update key stats in DB: database is locked (5) (SQLITE_BUSY)" keyID=13848
time="2025-08-04 16:34:03" level=info msg="GET /api/tasks/status - 200 - 467.05µs"
time="2025-08-04 16:34:05" level=info msg="GET /api/tasks/status - 200 - 420.11µs"
time="2025-08-04 16:34:07" level=info msg="GET /api/tasks/status - 200 - 337.693µs"
time="2025-08-04 16:34:09" level=info msg="GET /api/tasks/status - 200 - 344.967µs"
time="2025-08-04 16:34:11" level=info msg="GET /api/tasks/status - 200 - 350.387µs"
time="2025-08-04 16:34:13" level=info msg="GET /api/tasks/status - 200 - 396.265µs"
time="2025-08-04 16:34:15" level=info msg="GET /api/tasks/status - 200 - 456.298µs"
time="2025-08-04 16:34:17" level=info msg="GET /api/tasks/status - 200 - 388.359µs"
time="2025-08-04 16:34:19" level=info msg="GET /api/tasks/status - 200 - 526.802µs"
time="2025-08-04 16:34:21" level=info msg="GET /api/tasks/status - 200 - 367.519µs"
time="2025-08-04 16:34:23" level=info msg="GET /api/tasks/status - 200 - 355.767µs"
time="2025-08-04 16:34:25" level=info msg="GET /api/tasks/status - 200 - 445.808µs"
time="2025-08-04 16:34:27" level=info msg="GET /api/tasks/status - 200 - 408.418µs"
time="2025-08-04 16:34:29" level=info msg="GET /api/tasks/status - 200 - 302.545µs"
time="2025-08-04 16:34:31" level=info msg="GET /api/tasks/status - 200 - 243.724µs"
time="2025-08-04 16:34:33" level=info msg="GET /api/tasks/status - 200 - 341.31µs"
time="2025-08-04 16:34:35" level=info msg="GET /api/tasks/status - 200 - 353.463µs"
time="2025-08-04 16:34:38" level=info msg="GET /api/tasks/status - 200 - 497.227µs"
time="2025-08-04 16:34:40" level=info msg="GET /api/tasks/status - 200 - 276.166µs"
time="2025-08-04 16:34:42" level=info msg="GET /api/tasks/status - 200 - 308.848µs"
time="2025-08-04 16:34:44" level=info msg="GET /api/tasks/status - 200 - 229.047µs"
time="2025-08-04 16:34:46" level=info msg="GET /api/tasks/status - 200 - 249.024µs"
time="2025-08-04 16:34:49" level=info msg="GET /api/tasks/status - 200 - 653.705µs"
time="2025-08-04 16:34:50" level=info msg="GET /api/tasks/status - 200 - 351.209µs"
time="2025-08-04 16:34:53" level=info msg="GET /api/tasks/status - 200 - 285.163µs"
time="2025-08-04 16:34:55" level=info msg="GET /api/tasks/status - 200 - 359.204µs"
time="2025-08-04 16:34:57" level=info msg="GET /api/tasks/status - 200 - 351.339µs"
time="2025-08-04 16:34:59" level=info msg="GET /api/tasks/status - 200 - 405.882µs"
time="2025-08-04 16:35:01" level=info msg="GET /api/tasks/status - 200 - 349.204µs"
time="2025-08-04 16:35:03" level=info msg="GET /api/tasks/status - 200 - 542.763µs"
time="2025-08-04 16:35:05" level=info msg="GET /api/tasks/status - 200 - 358.723µs"
time="2025-08-04 16:35:06" level=error msg="Failed to handle key failure" error="failed to update key stats in DB: database is locked (5) (SQLITE_BUSY)" keyID=15445
time="2025-08-04 16:35:06" level=error msg="Failed to handle key failure" error="failed to update key stats in DB: database is locked (5) (SQLITE_BUSY)" keyID=15460
time="2025-08-04 16:35:06" level=error msg="Failed to handle key failure" error="failed to update key stats in DB: database is locked (5) (SQLITE_BUSY)" keyID=15464
time="2025-08-04 16:35:06" level=error msg="Failed to handle key failure" error="failed to update key stats in DB: database is locked (5) (SQLITE_BUSY)" keyID=15466
time="2025-08-04 16:35:06" level=error msg="Failed to handle key failure" error="failed to update key stats in DB: database is locked (5) (SQLITE_BUSY)" keyID=15463
time="2025-08-04 16:35:06" level=info msg="Manual validation finished for group gemini: {TotalKeys:2642 ValidKeys:2305 InvalidKeys:337}"
time="2025-08-04 16:35:07" level=info msg="GET /api/tasks/status - 200 - 356.659µs"
time="2025-08-04 16:35:08" level=info msg="GET /api/groups/2/stats - 200 - 10.835492ms"
time="2025-08-04 16:35:08" level=info msg="GET /api/keys?group_id=2&page=1&page_size=12 - 200 - 11.447617ms"
time="2025-08-04 16:37:17" level=info msg="CronChecker: Group 'gemini' has no invalid keys to check."
time="2025-08-04 16:37:17" level=info msg="CronChecker: Group 'targon' has no invalid keys to check."
time="2025-08-04 16:39:57" level=info msg="GET /api/settings - 200 - 275.084µs"
time="2025-08-04 16:40:04" level=info msg="GET /api/groups/config-options - 200 - 320.239µs"
time="2025-08-04 16:40:04" level=info msg="GET /api/groups - 200 - 979.283µs"
time="2025-08-04 16:40:04" level=info msg="GET /api/groups/2/stats - 200 - 10.818694ms"
time="2025-08-04 16:40:04" level=info msg="GET /api/keys?group_id=2&page=1&page_size=12 - 200 - 14.653441ms"
time="2025-08-04 16:40:14" level=info msg="POST /api/keys/validate-group - 200 - 37.676238ms"
time="2025-08-04 16:40:14" level=info msg="Starting manual validation" group=gemini status=active
time="2025-08-04 16:40:14" level=info msg="GET /api/tasks/status - 200 - 510.612µs"
time="2025-08-04 16:40:16" level=info msg="GET /api/tasks/status - 200 - 502.747µs"
time="2025-08-04 16:40:17" level=info msg="GET /api/tasks/status - 200 - 412.534µs"
time="2025-08-04 16:40:19" level=info msg="GET /api/tasks/status - 200 - 416.782µs"
time="2025-08-04 16:40:21" level=info msg="GET /api/tasks/status - 200 - 283.108µs"
time="2025-08-04 16:40:23" level=info msg="GET /api/tasks/status - 200 - 377.869µs"
time="2025-08-04 16:40:25" level=info msg="GET /api/tasks/status - 200 - 272.027µs"
time="2025-08-04 16:40:27" level=info msg="GET /api/tasks/status - 200 - 636.812µs"
time="2025-08-04 16:40:29" level=info msg="GET /api/tasks/status - 200 - 426.03µs"
time="2025-08-04 16:40:31" level=info msg="GET /api/tasks/status - 200 - 514.99µs"
time="2025-08-04 16:40:33" level=info msg="GET /api/tasks/status - 200 - 456.869µs"
time="2025-08-04 16:40:35" level=info msg="GET /api/tasks/status - 200 - 318.237µs"
time="2025-08-04 16:40:36" level=info msg="GET /api/tasks/status - 200 - 334.126µs"
time="2025-08-04 16:40:38" level=info msg="GET /api/tasks/status - 200 - 293.86µs"
time="2025-08-04 16:40:39" level=info msg="GET /api/tasks/status - 200 - 342.882µs"
time="2025-08-04 16:40:41" level=info msg="GET /api/tasks/status - 200 - 408.347µs"
time="2025-08-04 16:40:43" level=info msg="GET /api/tasks/status - 200 - 292.657µs"
time="2025-08-04 16:40:45" level=info msg="GET /api/tasks/status - 200 - 383.38µs"
time="2025-08-04 16:40:47" level=info msg="GET /api/tasks/status - 200 - 354.695µs"
time="2025-08-04 16:40:49" level=info msg="GET /api/tasks/status - 200 - 325.38µs"
time="2025-08-04 16:40:51" level=info msg="GET /api/tasks/status - 200 - 344.366µs"
time="2025-08-04 16:40:53" level=info msg="GET /api/tasks/status - 200 - 258.551µs"
time="2025-08-04 16:40:55" level=info msg="GET /api/tasks/status - 200 - 353.663µs"
time="2025-08-04 16:40:57" level=info msg="GET /api/tasks/status - 200 - 401.023µs"
time="2025-08-04 16:40:59" level=info msg="GET /api/tasks/status - 200 - 234.586µs"
time="2025-08-04 16:41:01" level=info msg="GET /api/tasks/status - 200 - 386.335µs"
time="2025-08-04 16:41:01" level=error msg="Failed to handle key failure" error="failed to update key stats in DB: database is locked (5) (SQLITE_BUSY)" keyID=13931
time="2025-08-04 16:41:03" level=info msg="GET /api/tasks/status - 200 - 457.741µs"
time="2025-08-04 16:41:05" level=info msg="GET /api/tasks/status - 200 - 439.436µs"
time="2025-08-04 16:41:07" level=info msg="GET /api/tasks/status - 200 - 493.329µs"
time="2025-08-04 16:41:09" level=error msg="Failed to handle key success" error="failed to update key in DB: database is locked (5) (SQLITE_BUSY)" keyID=14104
time="2025-08-04 16:41:09" level=error msg="Failed to handle key success" error="failed to update key in DB: database is locked (5) (SQLITE_BUSY)" keyID=14108
time="2025-08-04 16:41:09" level=info msg="GET /api/tasks/status - 200 - 304.63µs"
time="2025-08-04 16:41:10" level=error msg="Failed to handle key success" error="failed to update key in DB: database is locked (5) (SQLITE_BUSY)" keyID=14134
time="2025-08-04 16:41:10" level=error msg="Failed to handle key success" error="failed to update key in DB: database is locked (5) (SQLITE_BUSY)" keyID=14146
time="2025-08-04 16:41:12" level=error msg="Failed to handle key success" error="failed to update key in DB: database is locked (5) (SQLITE_BUSY)" keyID=14180
time="2025-08-04 16:41:14" level=error msg="Failed to handle key success" error="failed to update key in DB: database is locked (5) (SQLITE_BUSY)" keyID=14245
time="2025-08-04 16:41:15" level=error msg="Failed to handle key success" error="failed to update key in DB: database is locked (5) (SQLITE_BUSY)" keyID=14249
time="2025-08-04 16:41:15" level=error msg="Failed to handle key success" error="failed to update key in DB: database is locked (5) (SQLITE_BUSY)" keyID=14255
time="2025-08-04 16:41:15" level=error msg="Failed to handle key success" error="failed to update key in DB: database is locked (5) (SQLITE_BUSY)" keyID=14262
time="2025-08-04 16:41:15" level=error msg="Failed to handle key success" error="failed to update key in DB: database is locked (5) (SQLITE_BUSY)" keyID=14279
time="2025-08-04 16:41:16" level=error msg="Failed to handle key success" error="failed to update key in DB: database is locked (5) (SQLITE_BUSY)" keyID=14294
time="2025-08-04 16:41:18" level=error msg="Failed to handle key success" error="failed to update key in DB: database is locked (5) (SQLITE_BUSY)" keyID=14329
time="2025-08-04 16:41:18" level=error msg="Failed to handle key success" error="failed to update key in DB: database is locked (5) (SQLITE_BUSY)" keyID=14331
time="2025-08-04 16:41:20" level=error msg="Failed to handle key success" error="failed to update key in DB: database is locked (5) (SQLITE_BUSY)" keyID=14391
time="2025-08-04 16:41:22" level=error msg="Failed to handle key failure" error="failed to update key stats in DB: database is locked (5) (SQLITE_BUSY)" keyID=14444
time="2025-08-04 16:41:22" level=error msg="Failed to handle key success" error="failed to update key in DB: database is locked (5) (SQLITE_BUSY)" keyID=14439
time="2025-08-04 16:41:23" level=error msg="Failed to handle key success" error="failed to update key in DB: database is locked (5) (SQLITE_BUSY)" keyID=14474
time="2025-08-04 16:41:24" level=error msg="Failed to handle key success" error="failed to update key in DB: database is locked (5) (SQLITE_BUSY)" keyID=14478
time="2025-08-04 16:41:25" level=error msg="Failed to handle key success" error="failed to update key in DB: database is locked (5) (SQLITE_BUSY)" keyID=14499
time="2025-08-04 16:41:25" level=error msg="Failed to handle key failure" error="failed to update key stats in DB: database is locked (5) (SQLITE_BUSY)" keyID=14534
time="2025-08-04 16:41:26" level=error msg="Failed to handle key success" error="failed to update key in DB: database is locked (5) (SQLITE_BUSY)" keyID=14559
time="2025-08-04 16:41:26" level=error msg="Failed to handle key failure" error="failed to update key stats in DB: database is locked (5) (SQLITE_BUSY)" keyID=14570
time="2025-08-04 16:41:26" level=error msg="Failed to handle key failure" error="failed to update key stats in DB: database is locked (5) (SQLITE_BUSY)" keyID=14573
time="2025-08-04 16:41:27" level=error msg="Failed to handle key success" error="failed to update key in DB: database is locked (5) (SQLITE_BUSY)" keyID=14576
time="2025-08-04 16:41:27" level=error msg="Failed to handle key success" error="failed to update key in DB: database is locked (5) (SQLITE_BUSY)" keyID=14574
time="2025-08-04 16:41:29" level=error msg="Failed to handle key failure" error="failed to update key stats in DB: database is locked (5) (SQLITE_BUSY)" keyID=14634
time="2025-08-04 16:41:29" level=error msg="Failed to handle key success" error="failed to update key in DB: database is locked (5) (SQLITE_BUSY)" keyID=14636
time="2025-08-04 16:41:31" level=error msg="Failed to handle key success" error="failed to update key in DB: database is locked (5) (SQLITE_BUSY)" keyID=14689
time="2025-08-04 16:41:33" level=error msg="Failed to handle key success" error="failed to update key in DB: database is locked (5) (SQLITE_BUSY)" keyID=14724
time="2025-08-04 16:41:33" level=error msg="Failed to handle key success" error="failed to update key in DB: database is locked (5) (SQLITE_BUSY)" keyID=14735
time="2025-08-04 16:41:33" level=error msg="Failed to handle key failure" error="failed to update key stats in DB: database is locked (5) (SQLITE_BUSY)" keyID=14743
time="2025-08-04 16:41:34" level=error msg="Failed to handle key success" error="failed to update key in DB: database is locked (5) (SQLITE_BUSY)" keyID=14747
time="2025-08-04 16:41:35" level=error msg="Failed to handle key success" error="failed to update key in DB: database is locked (5) (SQLITE_BUSY)" keyID=14775
time="2025-08-04 16:41:36" level=error msg="Failed to handle key success" error="failed to update key in DB: database is locked (5) (SQLITE_BUSY)" keyID=14801
time="2025-08-04 16:41:37" level=error msg="Failed to handle key success" error="failed to update key in DB: database is locked (5) (SQLITE_BUSY)" keyID=14841
time="2025-08-04 16:41:38" level=error msg="Failed to handle key failure" error="failed to update key stats in DB: database is locked (5) (SQLITE_BUSY)" keyID=14858
time="2025-08-04 16:41:38" level=error msg="Failed to handle key success" error="failed to update key in DB: database is locked (5) (SQLITE_BUSY)" keyID=14867
time="2025-08-04 16:41:39" level=error msg="Failed to handle key failure" error="failed to update key stats in DB: database is locked (5) (SQLITE_BUSY)" keyID=14893
time="2025-08-04 16:41:39" level=error msg="Failed to handle key success" error="failed to update key in DB: database is locked (5) (SQLITE_BUSY)" keyID=14877
time="2025-08-04 16:41:40" level=error msg="Failed to handle key success" error="failed to update key in DB: database is locked (5) (SQLITE_BUSY)" keyID=14918
time="2025-08-04 16:41:41" level=error msg="Failed to handle key success" error="failed to update key in DB: database is locked (5) (SQLITE_BUSY)" keyID=14944
time="2025-08-04 16:41:42" level=error msg="Failed to handle key success" error="failed to update key in DB: database is locked (5) (SQLITE_BUSY)" keyID=14952
time="2025-08-04 16:41:44" level=error msg="Failed to handle key success" error="failed to update key in DB: database is locked (5) (SQLITE_BUSY)" keyID=15009
time="2025-08-04 16:41:44" level=error msg="Failed to handle key success" error="failed to update key in DB: database is locked (5) (SQLITE_BUSY)" keyID=15010
time="2025-08-04 16:41:44" level=info msg="GET /api/keys?group_id=2&page=1&page_size=12&status=invalid - 200 - 25.479112ms"
time="2025-08-04 16:41:45" level=error msg="Failed to handle key success" error="failed to update key in DB: database is locked (5) (SQLITE_BUSY)" keyID=15034
time="2025-08-04 16:41:45" level=error msg="Failed to handle key success" error="failed to update key in DB: database is locked (5) (SQLITE_BUSY)" keyID=15047
time="2025-08-04 16:41:45" level=error msg="Failed to handle key success" error="failed to update key in DB: database is locked (5) (SQLITE_BUSY)" keyID=15052
time="2025-08-04 16:41:45" level=error msg="Failed to handle key failure" error="failed to update key stats in DB: database is locked (5) (SQLITE_BUSY)" keyID=15069
time="2025-08-04 16:41:46" level=error msg="Failed to handle key success" error="failed to update key in DB: database is locked (5) (SQLITE_BUSY)" keyID=15066
time="2025-08-04 16:41:46" level=error msg="Failed to handle key failure" error="failed to update key stats in DB: database is locked (5) (SQLITE_BUSY)" keyID=15083
time="2025-08-04 16:41:46" level=info msg="GET /api/keys?group_id=2&page=1&page_size=12&status=active - 200 - 8.399753ms"
time="2025-08-04 16:41:46" level=error msg="Failed to handle key failure" error="failed to update key stats in DB: database is locked (5) (SQLITE_BUSY)" keyID=15096
time="2025-08-04 16:41:47" level=error msg="Failed to handle key success" error="failed to update key in DB: database is locked (5) (SQLITE_BUSY)" keyID=15101
time="2025-08-04 16:41:48" level=info msg="GET /api/keys?group_id=2&page=1&page_size=12 - 200 - 8.883434ms"
time="2025-08-04 16:41:48" level=error msg="Failed to handle key success" error="failed to update key in DB: database is locked (5) (SQLITE_BUSY)" keyID=15132
time="2025-08-04 16:41:49" level=error msg="Failed to handle key success" error="failed to update key in DB: database is locked (5) (SQLITE_BUSY)" keyID=15146
time="2025-08-04 16:41:51" level=error msg="Failed to handle key success" error="failed to update key in DB: database is locked (5) (SQLITE_BUSY)" keyID=15218
time="2025-08-04 16:41:52" level=error msg="Failed to handle key success" error="failed to update key in DB: database is locked (5) (SQLITE_BUSY)" keyID=15223
time="2025-08-04 16:41:52" level=error msg="Failed to handle key success" error="failed to update key in DB: database is locked (5) (SQLITE_BUSY)" keyID=15241
time="2025-08-04 16:41:53" level=error msg="Failed to handle key success" error="failed to update key in DB: database is locked (5) (SQLITE_BUSY)" keyID=15247
time="2025-08-04 16:41:55" level=info msg="POST /api/keys/validate-group - 200 - 31.851618ms"
time="2025-08-04 16:41:55" level=info msg="Starting manual validation" group=gemini status=all
time="2025-08-04 16:41:55" level=info msg="GET /api/tasks/status - 200 - 343.203µs"
time="2025-08-04 16:41:55" level=error msg="Failed to handle key success" error="failed to update key in DB: database is locked (5) (SQLITE_BUSY)" keyID=16
time="2025-08-04 16:41:55" level=error msg="Failed to handle key success" error="failed to update key in DB: database is locked (5) (SQLITE_BUSY)" keyID=15304
time="2025-08-04 16:41:56" level=error msg="Failed to handle key success" error="failed to update key in DB: database is locked (5) (SQLITE_BUSY)" keyID=15321
time="2025-08-04 16:41:56" level=error msg="Failed to handle key success" error="failed to update key in DB: database is locked (5) (SQLITE_BUSY)" keyID=15319
time="2025-08-04 16:41:56" level=error msg="Failed to handle key failure" error="failed to update key stats in DB: database is locked (5) (SQLITE_BUSY)" keyID=15339
time="2025-08-04 16:41:56" level=error msg="Failed to handle key success" error="failed to update key in DB: database is locked (5) (SQLITE_BUSY)" keyID=41
time="2025-08-04 16:41:56" level=info msg="GET /api/tasks/status - 200 - 348.322µs"
time="2025-08-04 16:41:57" level=error msg="Failed to handle key success" error="failed to update key in DB: database is locked (5) (SQLITE_BUSY)" keyID=15349
time="2025-08-04 16:41:57" level=error msg="Failed to handle key success" error="failed to update key in DB: database is locked (5) (SQLITE_BUSY)" keyID=15350
time="2025-08-04 16:41:58" level=error msg="Failed to handle key success" error="failed to update key in DB: database is locked (5) (SQLITE_BUSY)" keyID=63
time="2025-08-04 16:41:58" level=info msg="GET /api/tasks/status - 200 - 619.007µs"
time="2025-08-04 16:41:58" level=error msg="Failed to handle key success" error="failed to update key in DB: database is locked (5) (SQLITE_BUSY)" keyID=70
time="2025-08-04 16:41:58" level=error msg="Failed to handle key success" error="failed to update key in DB: database is locked (5) (SQLITE_BUSY)" keyID=74
time="2025-08-04 16:41:58" level=error msg="Failed to handle key success" error="failed to update key in DB: database is locked (5) (SQLITE_BUSY)" keyID=72
time="2025-08-04 16:41:58" level=error msg="Failed to handle key success" error="failed to update key in DB: database is locked (5) (SQLITE_BUSY)" keyID=46
time="2025-08-04 16:41:58" level=error msg="Failed to handle key success" error="failed to update key in DB: database is locked (5) (SQLITE_BUSY)" keyID=15381
time="2025-08-04 16:41:58" level=error msg="Failed to handle key success" error="failed to update key in DB: database is locked (5) (SQLITE_BUSY)" keyID=15380
time="2025-08-04 16:41:59" level=error msg="Failed to handle key success" error="failed to update key in DB: database is locked (5) (SQLITE_BUSY)" keyID=15392
time="2025-08-04 16:41:59" level=info msg="GET /api/tasks/status - 200 - 298.869µs"
time="2025-08-04 16:41:59" level=error msg="Failed to handle key success" error="failed to update key in DB: database is locked (5) (SQLITE_BUSY)" keyID=58
time="2025-08-04 16:41:59" level=error msg="Failed to handle key failure" error="failed to update key stats in DB: database is locked (5) (SQLITE_BUSY)" keyID=15407
time="2025-08-04 16:41:59" level=error msg="Failed to handle key success" error="failed to update key in DB: database is locked (5) (SQLITE_BUSY)" keyID=15401
time="2025-08-04 16:41:59" level=error msg="Failed to handle key success" error="failed to update key in DB: database is locked (5) (SQLITE_BUSY)" keyID=15402
time="2025-08-04 16:41:59" level=error msg="Failed to handle key success" error="failed to update key in DB: database is locked (5) (SQLITE_BUSY)" keyID=103
time="2025-08-04 16:42:00" level=error msg="Failed to handle key success" error="failed to update key in DB: database is locked (5) (SQLITE_BUSY)" keyID=15411
time="2025-08-04 16:42:00" level=error msg="Failed to handle key success" error="failed to update key in DB: database is locked (5) (SQLITE_BUSY)" keyID=115
time="2025-08-04 16:42:00" level=error msg="Failed to handle key success" error="failed to update key in DB: database is locked (5) (SQLITE_BUSY)" keyID=121
time="2025-08-04 16:42:00" level=error msg="Failed to handle key success" error="failed to update key in DB: database is locked (5) (SQLITE_BUSY)" keyID=15420
time="2025-08-04 16:42:00" level=error msg="Failed to handle key success" error="failed to update key in DB: database is locked (5) (SQLITE_BUSY)" keyID=15426
time="2025-08-04 16:42:00" level=error msg="Failed to handle key failure" error="failed to update key stats in DB: database is locked (5) (SQLITE_BUSY)" keyID=15443
time="2025-08-04 16:42:00" level=error msg="Failed to handle key failure" error="failed to update key stats in DB: database is locked (5) (SQLITE_BUSY)" keyID=15460
time="2025-08-04 16:42:00" level=error msg="Failed to handle key failure" error="failed to update key stats in DB: database is locked (5) (SQLITE_BUSY)" keyID=15464
time="2025-08-04 16:42:00" level=error msg="Failed to handle key success" error="failed to update key in DB: database is locked (5) (SQLITE_BUSY)" keyID=15431
time="2025-08-04 16:42:00" level=error msg="Failed to handle key success" error="failed to update key in DB: database is locked (5) (SQLITE_BUSY)" keyID=129
time="2025-08-04 16:42:00" level=error msg="Failed to handle key success" error="failed to update key in DB: database is locked (5) (SQLITE_BUSY)" keyID=15432
time="2025-08-04 16:42:01" level=info msg="Manual validation finished for group gemini: {TotalKeys:2642 ValidKeys:2305 InvalidKeys:337}"
time="2025-08-04 16:42:01" level=info msg="GET /api/tasks/status - 200 - 327.995µs"
time="2025-08-04 16:42:01" level=info msg="GET /api/keys?group_id=2&page=1&page_size=12 - 200 - 7.983241ms"
time="2025-08-04 16:42:02" level=error msg="Failed to handle key success" error="failed to update key in DB: database is locked (5) (SQLITE_BUSY)" keyID=157
time="2025-08-04 16:42:02" level=info msg="GET /api/groups/2/stats - 200 - 6.103644ms"
time="2025-08-04 16:42:03" level=error msg="Failed to handle key success" error="failed to update key in DB: database is locked (5) (SQLITE_BUSY)" keyID=178
time="2025-08-04 16:42:03" level=error msg="Failed to handle key success" error="failed to update key in DB: database is locked (5) (SQLITE_BUSY)" keyID=187
time="2025-08-04 16:42:04" level=error msg="Failed to handle key success" error="failed to update key in DB: database is locked (5) (SQLITE_BUSY)" keyID=203
time="2025-08-04 16:42:05" level=error msg="Failed to handle key success" error="failed to update key in DB: database is locked (5) (SQLITE_BUSY)" keyID=240
time="2025-08-04 16:42:05" level=info msg="GET /api/keys?group_id=2&page=1&page_size=12&status=invalid - 200 - 5.445522ms"
time="2025-08-04 16:42:07" level=info msg="GET /api/keys?group_id=2&page=1&page_size=12 - 200 - 6.760964ms"
time="2025-08-04 16:42:08" level=error msg="Failed to handle key success" error="failed to update key in DB: database is locked (5) (SQLITE_BUSY)" keyID=300
time="2025-08-04 16:42:08" level=error msg="Failed to handle key success" error="failed to update key in DB: database is locked (5) (SQLITE_BUSY)" keyID=305
time="2025-08-04 16:42:08" level=error msg="Failed to handle key success" error="failed to update key in DB: database is locked (5) (SQLITE_BUSY)" keyID=307
time="2025-08-04 16:42:08" level=error msg="Failed to handle key success" error="failed to update key in DB: database is locked (5) (SQLITE_BUSY)" keyID=312
time="2025-08-04 16:42:09" level=info msg="Starting manual validation" group=gemini status=all
time="2025-08-04 16:42:09" level=info msg="POST /api/keys/validate-group - 200 - 32.43028ms"
time="2025-08-04 16:42:09" level=info msg="GET /api/tasks/status - 200 - 346.77µs"
time="2025-08-04 16:42:11" level=info msg="GET /api/tasks/status - 200 - 296.364µs"
time="2025-08-04 16:42:11" level=error msg="Failed to handle key success" error="failed to update key in DB: database is locked (5) (SQLITE_BUSY)" keyID=371
time="2025-08-04 16:42:12" level=error msg="Failed to handle key success" error="failed to update key in DB: database is locked (5) (SQLITE_BUSY)" keyID=393
time="2025-08-04 16:42:12" level=error msg="Failed to handle key success" error="failed to update key in DB: database is locked (5) (SQLITE_BUSY)" keyID=394
time="2025-08-04 16:42:13" level=error msg="Failed to handle key success" error="failed to update key in DB: database is locked (5) (SQLITE_BUSY)" keyID=403
time="2025-08-04 16:42:13" level=info msg="GET /api/tasks/status - 200 - 309.669µs"
time="2025-08-04 16:42:15" level=info msg="GET /api/tasks/status - 200 - 339.687µs"
time="2025-08-04 16:42:17" level=info msg="GET /api/tasks/status - 200 - 177.718µs"
time="2025-08-04 16:42:19" level=error msg="Failed to handle key success" error="failed to update key in DB: database is locked (5) (SQLITE_BUSY)" keyID=540
time="2025-08-04 16:42:19" level=info msg="GET /api/tasks/status - 200 - 358.913µs"
time="2025-08-04 16:42:20" level=error msg="Failed to handle key success" error="failed to update key in DB: database is locked (5) (SQLITE_BUSY)" keyID=565
time="2025-08-04 16:42:20" level=error msg="Failed to handle key success" error="failed to update key in DB: database is locked (5) (SQLITE_BUSY)" keyID=575
time="2025-08-04 16:42:20" level=error msg="Failed to handle key success" error="failed to update key in DB: database is locked (5) (SQLITE_BUSY)" keyID=576
time="2025-08-04 16:42:21" level=error msg="Failed to handle key success" error="failed to update key in DB: database is locked (5) (SQLITE_BUSY)" keyID=583
time="2025-08-04 16:42:21" level=info msg="GET /api/tasks/status - 200 - 331.13µs"
time="2025-08-04 16:42:21" level=error msg="Failed to handle key success" error="failed to update key in DB: database is locked (5) (SQLITE_BUSY)" keyID=604
time="2025-08-04 16:42:21" level=error msg="Failed to handle key success" error="failed to update key in DB: database is locked (5) (SQLITE_BUSY)" keyID=603
time="2025-08-04 16:42:23" level=error msg="Failed to handle key success" error="failed to update key in DB: database is locked (5) (SQLITE_BUSY)" keyID=633
time="2025-08-04 16:42:23" level=info msg="GET /api/tasks/status - 200 - 354.565µs"
time="2025-08-04 16:42:23" level=error msg="Failed to handle key success" error="failed to update key in DB: database is locked (5) (SQLITE_BUSY)" keyID=13461
time="2025-08-04 16:42:23" level=error msg="Failed to handle key success" error="failed to update key in DB: database is locked (5) (SQLITE_BUSY)" keyID=13466
time="2025-08-04 16:42:24" level=error msg="Failed to handle key success" error="failed to update key in DB: database is locked (5) (SQLITE_BUSY)" keyID=13470
time="2025-08-04 16:42:24" level=error msg="Failed to handle key success" error="failed to update key in DB: database is locked (5) (SQLITE_BUSY)" keyID=13478
time="2025-08-04 16:42:25" level=info msg="GET /api/tasks/status - 200 - 275.554µs"
time="2025-08-04 16:42:27" level=info msg="GET /api/tasks/status - 200 - 268.531µs"
time="2025-08-04 16:42:27" level=error msg="Failed to handle key success" error="failed to update key in DB: database is locked (5) (SQLITE_BUSY)" keyID=13541
time="2025-08-04 16:42:28" level=error msg="Failed to handle key success" error="failed to update key in DB: database is locked (5) (SQLITE_BUSY)" keyID=13578
time="2025-08-04 16:42:29" level=error msg="Failed to handle key success" error="failed to update key in DB: database is locked (5) (SQLITE_BUSY)" keyID=13595
time="2025-08-04 16:42:29" level=info msg="GET /api/tasks/status - 200 - 441.48µs"
time="2025-08-04 16:42:30" level=error msg="Failed to handle key success" error="failed to update key in DB: database is locked (5) (SQLITE_BUSY)" keyID=13618
time="2025-08-04 16:42:30" level=error msg="Failed to handle key success" error="failed to update key in DB: database is locked (5) (SQLITE_BUSY)" keyID=13636
time="2025-08-04 16:42:31" level=info msg="GET /api/tasks/status - 200 - 355.296µs"
time="2025-08-04 16:42:31" level=error msg="Failed to handle key success" error="failed to update key in DB: database is locked (5) (SQLITE_BUSY)" keyID=13663
time="2025-08-04 16:42:32" level=error msg="Failed to handle key success" error="failed to update key in DB: database is locked (5) (SQLITE_BUSY)" keyID=13682
time="2025-08-04 16:42:33" level=error msg="Failed to handle key success" error="failed to update key in DB: database is locked (5) (SQLITE_BUSY)" keyID=13696
time="2025-08-04 16:42:33" level=info msg="GET /api/tasks/status - 200 - 311.303µs"
time="2025-08-04 16:42:33" level=error msg="Failed to handle key success" error="failed to update key in DB: database is locked (5) (SQLITE_BUSY)" keyID=13717
time="2025-08-04 16:42:35" level=info msg="GET /api/tasks/status - 200 - 265.244µs"
time="2025-08-04 16:42:36" level=error msg="Failed to handle key success" error="failed to update key in DB: database is locked (5) (SQLITE_BUSY)" keyID=13781
time="2025-08-04 16:42:36" level=error msg="Failed to handle key success" error="failed to update key in DB: database is locked (5) (SQLITE_BUSY)" keyID=13797
time="2025-08-04 16:42:37" level=error msg="Failed to handle key success" error="failed to update key in DB: database is locked (5) (SQLITE_BUSY)" keyID=13807
time="2025-08-04 16:42:37" level=info msg="GET /api/tasks/status - 200 - 323.065µs"
time="2025-08-04 16:42:37" level=error msg="Failed to handle key success" error="failed to update key in DB: database is locked (5) (SQLITE_BUSY)" keyID=13818
time="2025-08-04 16:42:38" level=error msg="Failed to handle key failure" error="failed to update key stats in DB: database is locked (5) (SQLITE_BUSY)" keyID=13848
time="2025-08-04 16:42:38" level=error msg="Failed to handle key success" error="failed to update key in DB: database is locked (5) (SQLITE_BUSY)" keyID=13841
time="2025-08-04 16:42:39" level=info msg="GET /api/tasks/status - 200 - 306.613µs"
time="2025-08-04 16:42:40" level=error msg="Failed to handle key success" error="failed to update key in DB: database is locked (5) (SQLITE_BUSY)" keyID=13876
time="2025-08-04 16:42:40" level=error msg="Failed to handle key success" error="failed to update key in DB: database is locked (5) (SQLITE_BUSY)" keyID=13870
time="2025-08-04 16:42:40" level=error msg="Failed to handle key success" error="failed to update key in DB: database is locked (5) (SQLITE_BUSY)" keyID=13884
time="2025-08-04 16:42:41" level=error msg="Failed to handle key failure" error="failed to update key stats in DB: database is locked (5) (SQLITE_BUSY)" keyID=13553
time="2025-08-04 16:42:41" level=info msg="GET /api/tasks/status - 200 - 332.984µs"
time="2025-08-04 16:42:42" level=error msg="Failed to handle key success" error="failed to update key in DB: database is locked (5) (SQLITE_BUSY)" keyID=13924
time="2025-08-04 16:42:42" level=error msg="Failed to handle key success" error="failed to update key in DB: database is locked (5) (SQLITE_BUSY)" keyID=13926
time="2025-08-04 16:42:43" level=info msg="GET /api/tasks/status - 200 - 290.553µs"
time="2025-08-04 16:42:44" level=error msg="Failed to handle key success" error="failed to update key in DB: database is locked (5) (SQLITE_BUSY)" keyID=13975
time="2025-08-04 16:42:44" level=error msg="Failed to handle key failure" error="failed to update key stats in DB: database is locked (5) (SQLITE_BUSY)" keyID=13984
time="2025-08-04 16:42:45" level=error msg="Failed to handle key success" error="failed to update key in DB: database is locked (5) (SQLITE_BUSY)" keyID=13988
time="2025-08-04 16:42:45" level=error msg="Failed to handle key success" error="failed to update key in DB: database is locked (5) (SQLITE_BUSY)" keyID=13991
time="2025-08-04 16:42:45" level=info msg="GET /api/tasks/status - 200 - 338.053µs"
time="2025-08-04 16:42:46" level=error msg="Failed to handle key success" error="failed to update key in DB: database is locked (5) (SQLITE_BUSY)" keyID=14015
time="2025-08-04 16:42:46" level=error msg="Failed to handle key success" error="failed to update key in DB: database is locked (5) (SQLITE_BUSY)" keyID=13663
time="2025-08-04 16:42:47" level=info msg="GET /api/tasks/status - 200 - 397.105µs"
time="2025-08-04 16:42:48" level=error msg="Failed to handle key success" error="failed to update key in DB: database is locked (5) (SQLITE_BUSY)" keyID=14070
time="2025-08-04 16:42:48" level=error msg="Failed to handle key success" error="failed to update key in DB: database is locked (5) (SQLITE_BUSY)" keyID=14076
time="2025-08-04 16:42:49" level=error msg="Failed to handle key success" error="failed to update key in DB: database is locked (5) (SQLITE_BUSY)" keyID=14102
time="2025-08-04 16:42:49" level=info msg="GET /api/tasks/status - 200 - 396.173µs"
time="2025-08-04 16:42:49" level=error msg="Failed to handle key success" error="failed to update key in DB: database is locked (5) (SQLITE_BUSY)" keyID=14111
time="2025-08-04 16:42:49" level=error msg="Failed to handle key success" error="failed to update key in DB: database is locked (5) (SQLITE_BUSY)" keyID=14115
time="2025-08-04 16:42:51" level=info msg="GET /api/tasks/status - 200 - 478.842µs"
time="2025-08-04 16:42:53" level=info msg="GET /api/tasks/status - 200 - 332.953µs"
time="2025-08-04 16:42:54" level=info msg="GET /api/tasks/status - 200 - 193.168µs"
time="2025-08-04 16:42:55" level=info msg="GET /api/tasks/status - 200 - 306.062µs"
time="2025-08-04 16:42:56" level=error msg="Failed to handle key failure" error="failed to update key stats in DB: database is locked (5) (SQLITE_BUSY)" keyID=14273
time="2025-08-04 16:42:57" level=info msg="GET /api/tasks/status - 200 - 225.499µs"
time="2025-08-04 16:42:58" level=info msg="GET /api/tasks/status - 200 - 580.685µs"
time="2025-08-04 16:43:00" level=info msg="GET /api/tasks/status - 200 - 223.585µs"
time="2025-08-04 16:43:02" level=info msg="GET /api/tasks/status - 200 - 437.903µs"
time="2025-08-04 16:43:04" level=info msg="GET /api/tasks/status - 200 - 267.74µs"
time="2025-08-04 16:43:05" level=warning msg="Key has reached blacklist threshold, disabling." keyID=14171 threshold=3
time="2025-08-04 16:43:05" level=warning msg="Key has reached blacklist threshold, disabling." keyID=14175 threshold=3
time="2025-08-04 16:43:06" level=warning msg="Key has reached blacklist threshold, disabling." keyID=14192 threshold=3
time="2025-08-04 16:43:06" level=warning msg="Key has reached blacklist threshold, disabling." keyID=14195 threshold=3
time="2025-08-04 16:43:06" level=warning msg="Key has reached blacklist threshold, disabling." keyID=14202 threshold=3
time="2025-08-04 16:43:06" level=info msg="GET /api/tasks/status - 200 - 309.379µs"
time="2025-08-04 16:43:07" level=warning msg="Key has reached blacklist threshold, disabling." keyID=14223 threshold=3
time="2025-08-04 16:43:07" level=warning msg="Key has reached blacklist threshold, disabling." keyID=14226 threshold=3
time="2025-08-04 16:43:07" level=warning msg="Key has reached blacklist threshold, disabling." keyID=14240 threshold=3
time="2025-08-04 16:43:07" level=warning msg="Key has reached blacklist threshold, disabling." keyID=14243 threshold=3
time="2025-08-04 16:43:08" level=warning msg="Key has reached blacklist threshold, disabling." keyID=14263 threshold=3
time="2025-08-04 16:43:09" level=info msg="GET /api/tasks/status - 200 - 453.514µs"
time="2025-08-04 16:43:09" level=warning msg="Key has reached blacklist threshold, disabling." keyID=14270 threshold=3
time="2025-08-04 16:43:10" level=error msg="Failed to handle key failure" error="failed to update key stats in DB: database is locked (5) (SQLITE_BUSY)" keyID=14290
time="2025-08-04 16:43:10" level=info msg="GET /api/tasks/status - 200 - 254.936µs"
time="2025-08-04 16:43:10" level=warning msg="Key has reached blacklist threshold, disabling." keyID=14302 threshold=3
time="2025-08-04 16:43:12" level=info msg="GET /api/tasks/status - 200 - 488.771µs"
time="2025-08-04 16:43:13" level=warning msg="Key has reached blacklist threshold, disabling." keyID=14369 threshold=3
time="2025-08-04 16:43:13" level=warning msg="Key has reached blacklist threshold, disabling." keyID=14371 threshold=3
time="2025-08-04 16:43:14" level=warning msg="Key has reached blacklist threshold, disabling." keyID=14378 threshold=3
time="2025-08-04 16:43:14" level=warning msg="Key has reached blacklist threshold, disabling." keyID=14380 threshold=3
time="2025-08-04 16:43:14" level=warning msg="Key has reached blacklist threshold, disabling." keyID=14384 threshold=3
time="2025-08-04 16:43:14" level=warning msg="Key has reached blacklist threshold, disabling." keyID=14390 threshold=3
time="2025-08-04 16:43:14" level=info msg="GET /api/tasks/status - 200 - 312.705µs"
time="2025-08-04 16:43:14" level=warning msg="Key has reached blacklist threshold, disabling." keyID=14399 threshold=3
time="2025-08-04 16:43:15" level=warning msg="Key has reached blacklist threshold, disabling." keyID=14414 threshold=3
time="2025-08-04 16:43:15" level=warning msg="Key has reached blacklist threshold, disabling." keyID=14425 threshold=3
time="2025-08-04 16:43:16" level=warning msg="Key has reached blacklist threshold, disabling." keyID=14443 threshold=3
time="2025-08-04 16:43:16" level=info msg="GET /api/tasks/status - 200 - 451.519µs"
time="2025-08-04 16:43:16" level=warning msg="Key has reached blacklist threshold, disabling." keyID=14454 threshold=3
time="2025-08-04 16:43:17" level=warning msg="Key has reached blacklist threshold, disabling." keyID=14457 threshold=3
time="2025-08-04 16:43:17" level=warning msg="Key has reached blacklist threshold, disabling." keyID=14466 threshold=3
time="2025-08-04 16:43:17" level=warning msg="Key has reached blacklist threshold, disabling." keyID=14468 threshold=3
time="2025-08-04 16:43:18" level=warning msg="Key has reached blacklist threshold, disabling." keyID=14482 threshold=3
time="2025-08-04 16:43:18" level=warning msg="Key has reached blacklist threshold, disabling." keyID=14484 threshold=3
time="2025-08-04 16:43:18" level=warning msg="Key has reached blacklist threshold, disabling." keyID=14490 threshold=3
time="2025-08-04 16:43:18" level=warning msg="Key has reached blacklist threshold, disabling." keyID=14497 threshold=3
time="2025-08-04 16:43:18" level=warning msg="Key has reached blacklist threshold, disabling." keyID=14502 threshold=3
time="2025-08-04 16:43:18" level=warning msg="Key has reached blacklist threshold, disabling." keyID=14507 threshold=3
time="2025-08-04 16:43:18" level=warning msg="Key has reached blacklist threshold, disabling." keyID=14508 threshold=3
time="2025-08-04 16:43:19" level=warning msg="Key has reached blacklist threshold, disabling." keyID=14512 threshold=3
time="2025-08-04 16:43:19" level=info msg="GET /api/tasks/status - 200 - 300.292µs"
time="2025-08-04 16:43:19" level=warning msg="Key has reached blacklist threshold, disabling." keyID=14524 threshold=3
time="2025-08-04 16:43:20" level=warning msg="Key has reached blacklist threshold, disabling." keyID=14528 threshold=3
time="2025-08-04 16:43:20" level=warning msg="Key has reached blacklist threshold, disabling." keyID=14529 threshold=3
time="2025-08-04 16:43:20" level=warning msg="Key has reached blacklist threshold, disabling." keyID=14538 threshold=3
time="2025-08-04 16:43:20" level=warning msg="Key has reached blacklist threshold, disabling." keyID=14542 threshold=3
time="2025-08-04 16:43:20" level=warning msg="Key has reached blacklist threshold, disabling." keyID=14543 threshold=3
time="2025-08-04 16:43:20" level=warning msg="Key has reached blacklist threshold, disabling." keyID=14547 threshold=3
time="2025-08-04 16:43:21" level=warning msg="Key has reached blacklist threshold, disabling." keyID=14561 threshold=3
time="2025-08-04 16:43:21" level=warning msg="Key has reached blacklist threshold, disabling." keyID=14565 threshold=3
time="2025-08-04 16:43:21" level=warning msg="Key has reached blacklist threshold, disabling." keyID=14566 threshold=3
time="2025-08-04 16:43:21" level=warning msg="Key has reached blacklist threshold, disabling." keyID=14567 threshold=3
time="2025-08-04 16:43:21" level=info msg="GET /api/tasks/status - 200 - 298.758µs"
time="2025-08-04 16:43:21" level=error msg="Failed to handle key failure" error="failed to update key stats in DB: database is locked (5) (SQLITE_BUSY)" keyID=14919
time="2025-08-04 16:43:21" level=warning msg="Key has reached blacklist threshold, disabling." keyID=14575 threshold=3
time="2025-08-04 16:43:21" level=warning msg="Key has reached blacklist threshold, disabling." keyID=14580 threshold=3
time="2025-08-04 16:43:21" level=warning msg="Key has reached blacklist threshold, disabling." keyID=14583 threshold=3
time="2025-08-04 16:43:22" level=warning msg="Key has reached blacklist threshold, disabling." keyID=14582 threshold=3
time="2025-08-04 16:43:22" level=warning msg="Key has reached blacklist threshold, disabling." keyID=14588 threshold=3
time="2025-08-04 16:43:22" level=warning msg="Key has reached blacklist threshold, disabling." keyID=14587 threshold=3
time="2025-08-04 16:43:22" level=warning msg="Key has reached blacklist threshold, disabling." keyID=14589 threshold=3
time="2025-08-04 16:43:22" level=warning msg="Key has reached blacklist threshold, disabling." keyID=14610 threshold=3
time="2025-08-04 16:43:23" level=warning msg="Key has reached blacklist threshold, disabling." keyID=14615 threshold=3
time="2025-08-04 16:43:23" level=warning msg="Key has reached blacklist threshold, disabling." keyID=14616 threshold=3
time="2025-08-04 16:43:23" level=error msg="Failed to handle key failure" error="failed to update key stats in DB: database is locked (5) (SQLITE_BUSY)" keyID=14966
time="2025-08-04 16:43:23" level=warning msg="Key has reached blacklist threshold, disabling." keyID=14622 threshold=3
time="2025-08-04 16:43:23" level=info msg="GET /api/tasks/status - 200 - 343.523µs"
time="2025-08-04 16:43:23" level=warning msg="Key has reached blacklist threshold, disabling." keyID=14629 threshold=3
time="2025-08-04 16:43:24" level=warning msg="Key has reached blacklist threshold, disabling." keyID=14639 threshold=3
time="2025-08-04 16:43:24" level=warning msg="Key has reached blacklist threshold, disabling." keyID=14651 threshold=3
time="2025-08-04 16:43:24" level=warning msg="Key has reached blacklist threshold, disabling." keyID=14655 threshold=3
time="2025-08-04 16:43:25" level=warning msg="Key has reached blacklist threshold, disabling." keyID=14661 threshold=3
time="2025-08-04 16:43:25" level=error msg="Failed to handle key failure" error="failed to update key stats in DB: database is locked (5) (SQLITE_BUSY)" keyID=15005
time="2025-08-04 16:43:25" level=info msg="GET /api/tasks/status - 200 - 309.81µs"
time="2025-08-04 16:43:26" level=error msg="Failed to handle key failure" error="failed to update key stats in DB: database is locked (5) (SQLITE_BUSY)" keyID=14682
time="2025-08-04 16:43:26" level=warning msg="Key has reached blacklist threshold, disabling." keyID=14684 threshold=3
time="2025-08-04 16:43:26" level=warning msg="Key has reached blacklist threshold, disabling." keyID=14693 threshold=3
time="2025-08-04 16:43:26" level=warning msg="Key has reached blacklist threshold, disabling." keyID=14697 threshold=3
time="2025-08-04 16:43:26" level=warning msg="Key has reached blacklist threshold, disabling." keyID=14698 threshold=3
time="2025-08-04 16:43:26" level=warning msg="Key has reached blacklist threshold, disabling." keyID=14705 threshold=3
time="2025-08-04 16:43:27" level=warning msg="Key has reached blacklist threshold, disabling." keyID=14714 threshold=3
time="2025-08-04 16:43:27" level=warning msg="Key has reached blacklist threshold, disabling." keyID=14715 threshold=3
time="2025-08-04 16:43:27" level=warning msg="Key has reached blacklist threshold, disabling." keyID=14717 threshold=3
time="2025-08-04 16:43:27" level=warning msg="Key has reached blacklist threshold, disabling." keyID=14726 threshold=3
time="2025-08-04 16:43:27" level=warning msg="Key has reached blacklist threshold, disabling." keyID=14727 threshold=3
time="2025-08-04 16:43:27" level=info msg="GET /api/tasks/status - 200 - 343.263µs"
time="2025-08-04 16:43:28" level=warning msg="Key has reached blacklist threshold, disabling." keyID=14744 threshold=3
time="2025-08-04 16:43:29" level=info msg="GET /api/tasks/status - 200 - 360.426µs"
time="2025-08-04 16:43:30" level=warning msg="Key has reached blacklist threshold, disabling." keyID=14776 threshold=3
time="2025-08-04 16:43:30" level=warning msg="Key has reached blacklist threshold, disabling." keyID=14778 threshold=3
time="2025-08-04 16:43:31" level=warning msg="Key has reached blacklist threshold, disabling." keyID=14798 threshold=3
time="2025-08-04 16:43:31" level=warning msg="Key has reached blacklist threshold, disabling." keyID=14809 threshold=3
time="2025-08-04 16:43:31" level=warning msg="Key has reached blacklist threshold, disabling." keyID=14811 threshold=3
time="2025-08-04 16:43:31" level=info msg="GET /api/tasks/status - 200 - 355.367µs"
time="2025-08-04 16:43:31" level=warning msg="Key has reached blacklist threshold, disabling." keyID=14816 threshold=3
time="2025-08-04 16:43:31" level=warning msg="Key has reached blacklist threshold, disabling." keyID=14819 threshold=3
time="2025-08-04 16:43:32" level=warning msg="Key has reached blacklist threshold, disabling." keyID=14823 threshold=3
time="2025-08-04 16:43:32" level=warning msg="Key has reached blacklist threshold, disabling." keyID=14825 threshold=3
time="2025-08-04 16:43:32" level=warning msg="Key has reached blacklist threshold, disabling." keyID=14845 threshold=3
time="2025-08-04 16:43:32" level=warning msg="Key has reached blacklist threshold, disabling." keyID=14850 threshold=3
time="2025-08-04 16:43:33" level=warning msg="Key has reached blacklist threshold, disabling." keyID=14859 threshold=3
time="2025-08-04 16:43:33" level=warning msg="Key has reached blacklist threshold, disabling." keyID=14863 threshold=3
time="2025-08-04 16:43:33" level=warning msg="Key has reached blacklist threshold, disabling." keyID=14865 threshold=3
time="2025-08-04 16:43:33" level=warning msg="Key has reached blacklist threshold, disabling." keyID=14866 threshold=3
time="2025-08-04 16:43:33" level=info msg="GET /api/tasks/status - 200 - 388.919µs"
time="2025-08-04 16:43:33" level=warning msg="Key has reached blacklist threshold, disabling." keyID=14869 threshold=3
time="2025-08-04 16:43:34" level=warning msg="Key has reached blacklist threshold, disabling." keyID=14883 threshold=3
time="2025-08-04 16:43:34" level=warning msg="Key has reached blacklist threshold, disabling." keyID=14884 threshold=3
time="2025-08-04 16:43:34" level=warning msg="Key has reached blacklist threshold, disabling." keyID=14888 threshold=3
time="2025-08-04 16:43:35" level=error msg="Failed to handle key failure" error="failed to update key stats in DB: database is locked (5) (SQLITE_BUSY)" keyID=14911
time="2025-08-04 16:43:35" level=warning msg="Key has reached blacklist threshold, disabling." keyID=14916 threshold=3
time="2025-08-04 16:43:35" level=warning msg="Key has reached blacklist threshold, disabling." keyID=14917 threshold=3
time="2025-08-04 16:43:35" level=warning msg="Key has reached blacklist threshold, disabling." keyID=14921 threshold=3
time="2025-08-04 16:43:35" level=info msg="GET /api/tasks/status - 200 - 372.9µs"
time="2025-08-04 16:43:35" level=warning msg="Key has reached blacklist threshold, disabling." keyID=14930 threshold=3
time="2025-08-04 16:43:36" level=warning msg="Key has reached blacklist threshold, disabling." keyID=14936 threshold=3
time="2025-08-04 16:43:36" level=warning msg="Key has reached blacklist threshold, disabling." keyID=14938 threshold=3
time="2025-08-04 16:43:36" level=warning msg="Key has reached blacklist threshold, disabling." keyID=14942 threshold=3
time="2025-08-04 16:43:36" level=warning msg="Key has reached blacklist threshold, disabling." keyID=14946 threshold=3
time="2025-08-04 16:43:37" level=warning msg="Key has reached blacklist threshold, disabling." keyID=14962 threshold=3
time="2025-08-04 16:43:37" level=info msg="GET /api/tasks/status - 200 - 298.628µs"
time="2025-08-04 16:43:38" level=warning msg="Key has reached blacklist threshold, disabling." keyID=14982 threshold=3
time="2025-08-04 16:43:38" level=warning msg="Key has reached blacklist threshold, disabling." keyID=14993 threshold=3
time="2025-08-04 16:43:39" level=warning msg="Key has reached blacklist threshold, disabling." keyID=15001 threshold=3
time="2025-08-04 16:43:39" level=warning msg="Key has reached blacklist threshold, disabling." keyID=15013 threshold=3
time="2025-08-04 16:43:39" level=warning msg="Key has reached blacklist threshold, disabling." keyID=15016 threshold=3
time="2025-08-04 16:43:39" level=warning msg="Key has reached blacklist threshold, disabling." keyID=15017 threshold=3
time="2025-08-04 16:43:39" level=warning msg="Key has reached blacklist threshold, disabling." keyID=15021 threshold=3
time="2025-08-04 16:43:39" level=warning msg="Key has reached blacklist threshold, disabling." keyID=15028 threshold=3
time="2025-08-04 16:43:40" level=warning msg="Key has reached blacklist threshold, disabling." keyID=15030 threshold=3
time="2025-08-04 16:43:40" level=warning msg="Key has reached blacklist threshold, disabling." keyID=15031 threshold=3
time="2025-08-04 16:43:40" level=warning msg="Key has reached blacklist threshold, disabling." keyID=15038 threshold=3
time="2025-08-04 16:43:40" level=warning msg="Key has reached blacklist threshold, disabling." keyID=15043 threshold=3
time="2025-08-04 16:43:40" level=warning msg="Key has reached blacklist threshold, disabling." keyID=15044 threshold=3
time="2025-08-04 16:43:40" level=info msg="GET /api/tasks/status - 200 - 388.439µs"
time="2025-08-04 16:43:41" level=warning msg="Key has reached blacklist threshold, disabling." keyID=15059 threshold=3
time="2025-08-04 16:43:41" level=warning msg="Key has reached blacklist threshold, disabling." keyID=15072 threshold=3
time="2025-08-04 16:43:41" level=error msg="Failed to handle key failure" error="failed to update key stats in DB: database is locked (5) (SQLITE_BUSY)" keyID=15078
time="2025-08-04 16:43:41" level=warning msg="Key has reached blacklist threshold, disabling." keyID=15079 threshold=3
time="2025-08-04 16:43:42" level=warning msg="Key has reached blacklist threshold, disabling." keyID=15082 threshold=3
time="2025-08-04 16:43:42" level=warning msg="Key has reached blacklist threshold, disabling." keyID=15088 threshold=3
time="2025-08-04 16:43:42" level=warning msg="Key has reached blacklist threshold, disabling." keyID=15091 threshold=3
time="2025-08-04 16:43:42" level=warning msg="Key has reached blacklist threshold, disabling." keyID=15090 threshold=3
time="2025-08-04 16:43:42" level=error msg="Failed to handle key failure" error="failed to update key stats in DB: database is locked (5) (SQLITE_BUSY)" keyID=15093
time="2025-08-04 16:43:42" level=error msg="Failed to handle key failure" error="failed to update key stats in DB: database is locked (5) (SQLITE_BUSY)" keyID=15443
time="2025-08-04 16:43:42" level=error msg="Failed to handle key failure" error="failed to update key stats in DB: database is locked (5) (SQLITE_BUSY)" keyID=15454
time="2025-08-04 16:43:42" level=error msg="Failed to handle key failure" error="failed to update key stats in DB: database is locked (5) (SQLITE_BUSY)" keyID=15096
time="2025-08-04 16:43:42" level=error msg="Failed to handle key failure" error="failed to update key stats in DB: database is locked (5) (SQLITE_BUSY)" keyID=15460
time="2025-08-04 16:43:42" level=error msg="Failed to handle key failure" error="failed to update key stats in DB: database is locked (5) (SQLITE_BUSY)" keyID=15466
time="2025-08-04 16:43:42" level=info msg="GET /api/tasks/status - 200 - 324.277µs"
time="2025-08-04 16:43:42" level=warning msg="Key has reached blacklist threshold, disabling." keyID=15106 threshold=3
time="2025-08-04 16:43:43" level=warning msg="Key has reached blacklist threshold, disabling." keyID=15114 threshold=3
time="2025-08-04 16:43:43" level=info msg="Manual validation finished for group gemini: {TotalKeys:2642 ValidKeys:2305 InvalidKeys:337}"
time="2025-08-04 16:43:43" level=warning msg="Key has reached blacklist threshold, disabling." keyID=15120 threshold=3
time="2025-08-04 16:43:43" level=warning msg="Key has reached blacklist threshold, disabling." keyID=15123 threshold=3
time="2025-08-04 16:43:44" level=warning msg="Key has reached blacklist threshold, disabling." keyID=15131 threshold=3
time="2025-08-04 16:43:44" level=warning msg="Key has reached blacklist threshold, disabling." keyID=15134 threshold=3
time="2025-08-04 16:43:44" level=info msg="GET /api/tasks/status - 200 - 488.769µs"
time="2025-08-04 16:43:44" level=warning msg="Key has reached blacklist threshold, disabling." keyID=15139 threshold=3
time="2025-08-04 16:43:44" level=warning msg="Key has reached blacklist threshold, disabling." keyID=15142 threshold=3
time="2025-08-04 16:43:45" level=info msg="GET /api/groups/2/stats - 200 - 6.87392ms"
time="2025-08-04 16:43:45" level=info msg="GET /api/keys?group_id=2&page=1&page_size=12 - 200 - 9.509256ms"
time="2025-08-04 16:43:45" level=warning msg="Key has reached blacklist threshold, disabling." keyID=15150 threshold=3
time="2025-08-04 16:43:45" level=warning msg="Key has reached blacklist threshold, disabling." keyID=15153 threshold=3
time="2025-08-04 16:43:45" level=warning msg="Key has reached blacklist threshold, disabling." keyID=15154 threshold=3
time="2025-08-04 16:43:45" level=warning msg="Key has reached blacklist threshold, disabling." keyID=15158 threshold=3
time="2025-08-04 16:43:45" level=warning msg="Key has reached blacklist threshold, disabling." keyID=15163 threshold=3
time="2025-08-04 16:43:45" level=warning msg="Key has reached blacklist threshold, disabling." keyID=15167 threshold=3
time="2025-08-04 16:43:45" level=warning msg="Key has reached blacklist threshold, disabling." keyID=15170 threshold=3
time="2025-08-04 16:43:46" level=warning msg="Key has reached blacklist threshold, disabling." keyID=15176 threshold=3
time="2025-08-04 16:43:46" level=warning msg="Key has reached blacklist threshold, disabling." keyID=15177 threshold=3
time="2025-08-04 16:43:46" level=warning msg="Key has reached blacklist threshold, disabling." keyID=15178 threshold=3
time="2025-08-04 16:43:46" level=warning msg="Key has reached blacklist threshold, disabling." keyID=15189 threshold=3
time="2025-08-04 16:43:46" level=warning msg="Key has reached blacklist threshold, disabling." keyID=15191 threshold=3
time="2025-08-04 16:43:46" level=warning msg="Key has reached blacklist threshold, disabling." keyID=15195 threshold=3
time="2025-08-04 16:43:46" level=warning msg="Key has reached blacklist threshold, disabling." keyID=15196 threshold=3
time="2025-08-04 16:43:46" level=warning msg="Key has reached blacklist threshold, disabling." keyID=15201 threshold=3
time="2025-08-04 16:43:46" level=warning msg="Key has reached blacklist threshold, disabling." keyID=15203 threshold=3
time="2025-08-04 16:43:46" level=warning msg="Key has reached blacklist threshold, disabling." keyID=15207 threshold=3
time="2025-08-04 16:43:47" level=warning msg="Key has reached blacklist threshold, disabling." keyID=15212 threshold=3
time="2025-08-04 16:43:47" level=warning msg="Key has reached blacklist threshold, disabling." keyID=15215 threshold=3
time="2025-08-04 16:43:47" level=warning msg="Key has reached blacklist threshold, disabling." keyID=15220 threshold=3
time="2025-08-04 16:43:47" level=warning msg="Key has reached blacklist threshold, disabling." keyID=15221 threshold=3
time="2025-08-04 16:43:47" level=warning msg="Key has reached blacklist threshold, disabling." keyID=15224 threshold=3
time="2025-08-04 16:43:48" level=warning msg="Key has reached blacklist threshold, disabling." keyID=15243 threshold=3
time="2025-08-04 16:43:48" level=warning msg="Key has reached blacklist threshold, disabling." keyID=15254 threshold=3
time="2025-08-04 16:43:48" level=warning msg="Key has reached blacklist threshold, disabling." keyID=15270 threshold=3
time="2025-08-04 16:43:49" level=warning msg="Key has reached blacklist threshold, disabling." keyID=15280 threshold=3
time="2025-08-04 16:43:49" level=warning msg="Key has reached blacklist threshold, disabling." keyID=15299 threshold=3
time="2025-08-04 16:43:49" level=warning msg="Key has reached blacklist threshold, disabling." keyID=15303 threshold=3
time="2025-08-04 16:43:50" level=warning msg="Key has reached blacklist threshold, disabling." keyID=15311 threshold=3
time="2025-08-04 16:43:50" level=warning msg="Key has reached blacklist threshold, disabling." keyID=15317 threshold=3
time="2025-08-04 16:43:50" level=warning msg="Key has reached blacklist threshold, disabling." keyID=15323 threshold=3
time="2025-08-04 16:43:50" level=warning msg="Key has reached blacklist threshold, disabling." keyID=15326 threshold=3
time="2025-08-04 16:43:50" level=warning msg="Key has reached blacklist threshold, disabling." keyID=15334 threshold=3
time="2025-08-04 16:43:50" level=warning msg="Key has reached blacklist threshold, disabling." keyID=15338 threshold=3
time="2025-08-04 16:43:51" level=warning msg="Key has reached blacklist threshold, disabling." keyID=15345 threshold=3
time="2025-08-04 16:43:51" level=warning msg="Key has reached blacklist threshold, disabling." keyID=15368 threshold=3
time="2025-08-04 16:43:52" level=warning msg="Key has reached blacklist threshold, disabling." keyID=15383 threshold=3
time="2025-08-04 16:43:53" level=warning msg="Key has reached blacklist threshold, disabling." keyID=15405 threshold=3
time="2025-08-04 16:43:53" level=warning msg="Key has reached blacklist threshold, disabling." keyID=15419 threshold=3
time="2025-08-04 16:43:53" level=warning msg="Key has reached blacklist threshold, disabling." keyID=15422 threshold=3
time="2025-08-04 16:43:53" level=warning msg="Key has reached blacklist threshold, disabling." keyID=15424 threshold=3
time="2025-08-04 16:43:53" level=warning msg="Key has reached blacklist threshold, disabling." keyID=15428 threshold=3
time="2025-08-04 16:43:53" level=warning msg="Key has reached blacklist threshold, disabling." keyID=15430 threshold=3
time="2025-08-04 16:43:54" level=warning msg="Key has reached blacklist threshold, disabling." keyID=15435 threshold=3
time="2025-08-04 16:43:54" level=warning msg="Key has reached blacklist threshold, disabling." keyID=15437 threshold=3
time="2025-08-04 16:43:54" level=warning msg="Key has reached blacklist threshold, disabling." keyID=15438 threshold=3
time="2025-08-04 16:43:54" level=warning msg="Key has reached blacklist threshold, disabling." keyID=15439 threshold=3
time="2025-08-04 16:43:54" level=warning msg="Key has reached blacklist threshold, disabling." keyID=15441 threshold=3
time="2025-08-04 16:43:54" level=error msg="Failed to handle key failure" error="failed to update key stats in DB: database is locked (5) (SQLITE_BUSY)" keyID=15440
time="2025-08-04 16:43:54" level=warning msg="Key has reached blacklist threshold, disabling." keyID=15442 threshold=3
time="2025-08-04 16:43:54" level=warning msg="Key has reached blacklist threshold, disabling." keyID=15444 threshold=3
time="2025-08-04 16:43:54" level=warning msg="Key has reached blacklist threshold, disabling." keyID=15445 threshold=3
time="2025-08-04 16:43:54" level=warning msg="Key has reached blacklist threshold, disabling." keyID=15446 threshold=3
time="2025-08-04 16:43:54" level=warning msg="Key has reached blacklist threshold, disabling." keyID=15447 threshold=3
time="2025-08-04 16:43:54" level=warning msg="Key has reached blacklist threshold, disabling." keyID=15449 threshold=3
time="2025-08-04 16:43:54" level=error msg="Failed to handle key failure" error="failed to update key stats in DB: database is locked (5) (SQLITE_BUSY)" keyID=15450
time="2025-08-04 16:43:54" level=warning msg="Key has reached blacklist threshold, disabling." keyID=15448 threshold=3
time="2025-08-04 16:43:54" level=warning msg="Key has reached blacklist threshold, disabling." keyID=15451 threshold=3
time="2025-08-04 16:43:54" level=error msg="Failed to handle key failure" error="failed to update key stats in DB: database is locked (5) (SQLITE_BUSY)" keyID=15452
time="2025-08-04 16:43:54" level=warning msg="Key has reached blacklist threshold, disabling." keyID=15453 threshold=3
time="2025-08-04 16:43:54" level=error msg="Failed to handle key failure" error="failed to update key stats in DB: database is locked (5) (SQLITE_BUSY)" keyID=15454
time="2025-08-04 16:43:54" level=error msg="Failed to handle key failure" error="failed to update key stats in DB: database is locked (5) (SQLITE_BUSY)" keyID=15457
time="2025-08-04 16:43:54" level=warning msg="Key has reached blacklist threshold, disabling." keyID=15455 threshold=3
time="2025-08-04 16:43:54" level=warning msg="Key has reached blacklist threshold, disabling." keyID=15456 threshold=3
time="2025-08-04 16:43:54" level=warning msg="Key has reached blacklist threshold, disabling." keyID=15458 threshold=3
time="2025-08-04 16:43:54" level=warning msg="Key has reached blacklist threshold, disabling." keyID=15461 threshold=3
time="2025-08-04 16:43:54" level=warning msg="Key has reached blacklist threshold, disabling." keyID=15459 threshold=3
time="2025-08-04 16:43:54" level=warning msg="Key has reached blacklist threshold, disabling." keyID=15463 threshold=3
time="2025-08-04 16:43:54" level=warning msg="Key has reached blacklist threshold, disabling." keyID=15462 threshold=3
time="2025-08-04 16:43:54" level=error msg="Failed to handle key failure" error="failed to update key stats in DB: database is locked (5) (SQLITE_BUSY)" keyID=15464
time="2025-08-04 16:43:54" level=warning msg="Key has reached blacklist threshold, disabling." keyID=15465 threshold=3
time="2025-08-04 16:43:54" level=warning msg="Key has reached blacklist threshold, disabling." keyID=15467 threshold=3
time="2025-08-04 16:43:54" level=info msg="Manual validation finished for group gemini: {TotalKeys:2642 ValidKeys:2305 InvalidKeys:337}"
time="2025-08-04 16:45:29" level=info msg="GET /api/keys?group_id=2&page=1&page_size=12&status=invalid - 200 - 6.024475ms"
time="2025-08-04 16:45:35" level=info msg="GET /api/keys?group_id=2&page=1&page_size=12 - 200 - 6.624636ms"
time="2025-08-04 17:42:17" level=info msg="CronChecker: Group 'targon' has no invalid keys to check."
time="2025-08-04 17:42:18" level=info msg="CronChecker: Group 'gemini' validation finished. Total checked: 194, became valid: 0. Duration: 1.081566048s."
time="2025-08-04 18:19:58" level=warning msg="POST /proxy/gemini/v1beta/models/gemini-2.5-pro:streamGenerateContent?alt=sse - 403 - 91.845206ms"
time="2025-08-04 18:20:12" level=info msg="GET / - 200 - 595.997µs"
time="2025-08-04 18:20:13" level=info msg="GET /api/dashboard/chart - 200 - 1.160043ms"
time="2025-08-04 18:20:13" level=info msg="GET /api/tasks/status - 200 - 439.959µs"
time="2025-08-04 18:20:13" level=info msg="GET /api/groups/list - 200 - 1.145264ms"
time="2025-08-04 18:20:13" level=info msg="GET /api/dashboard/stats - 200 - 10.979542ms"
time="2025-08-04 18:20:16" level=info msg="GET /assets/Logs-DOB48F16.js - 200 - 2.835498ms"
time="2025-08-04 18:20:16" level=info msg="GET /assets/Logs-jx6kyE3D.css - 200 - 224.117µs"
time="2025-08-04 18:20:17" level=info msg="GET /api/logs?page=1&page_size=15 - 200 - 816.266µs"
time="2025-08-04 18:20:17" level=info msg="Successfully flushed 1 request logs."
time="2025-08-04 18:20:27" level=info msg="GET /logs - 200 - 282.248µs"
time="2025-08-04 18:20:28" level=info msg="GET /api/tasks/status - 200 - 417.936µs"
time="2025-08-04 18:20:28" level=info msg="GET /api/logs?page=1&page_size=15 - 200 - 1.229916ms"
time="2025-08-04 18:21:02" level=info msg="GET /api/groups/config-options - 200 - 276.988µs"
time="2025-08-04 18:21:02" level=info msg="GET /api/groups - 200 - 781.761µs"
time="2025-08-04 18:21:03" level=info msg="GET /api/keys?group_id=2&page=1&page_size=12 - 200 - 44.386094ms"
time="2025-08-04 18:21:03" level=info msg="GET /api/groups/2/stats - 200 - 45.393826ms"
time="2025-08-04 18:21:14" level=info msg="POST /api/keys/clear-all-invalid - 200 - 59.577347ms"
time="2025-08-04 18:21:15" level=info msg="GET /api/keys?group_id=2&page=1&page_size=12 - 200 - 5.262586ms"
time="2025-08-04 18:21:15" level=info msg="GET /api/groups/2/stats - 200 - 4.55154ms"
time="2025-08-04 18:21:19" level=info msg="GET /api/keys?group_id=1&page=1&page_size=12 - 200 - 9.462293ms"
time="2025-08-04 18:21:19" level=info msg="GET /api/groups/1/stats - 200 - 9.808223ms"
time="2025-08-04 18:21:20" level=info msg="GET /api/keys?group_id=2&page=1&page_size=12 - 200 - 7.802197ms"
time="2025-08-04 18:21:20" level=info msg="GET /api/groups/2/stats - 200 - 9.309041ms"
time="2025-08-04 18:21:25" level=info msg="POST /api/keys/validate-group - 200 - 31.743649ms"
time="2025-08-04 18:21:25" level=info msg="Starting manual validation" group=gemini status=all
time="2025-08-04 18:21:25" level=info msg="GET /api/tasks/status - 200 - 238.354µs"
time="2025-08-04 18:21:26" level=info msg="GET /api/tasks/status - 200 - 374.104µs"
time="2025-08-04 18:21:28" level=warning msg="Key has reached blacklist threshold, disabling." keyID=80 threshold=3
time="2025-08-04 18:21:28" level=info msg="GET /api/tasks/status - 200 - 470.787µs"
time="2025-08-04 18:21:30" level=info msg="GET /api/tasks/status - 200 - 244.977µs"
time="2025-08-04 18:21:32" level=info msg="GET /api/tasks/status - 200 - 278.862µs"
time="2025-08-04 18:21:34" level=info msg="GET /api/tasks/status - 200 - 238.405µs"
time="2025-08-04 18:21:35" level=info msg="GET /api/tasks/status - 200 - 554.928µs"
time="2025-08-04 18:21:36" level=info msg="GET /api/tasks/status - 200 - 298.109µs"
time="2025-08-04 18:21:38" level=info msg="GET /api/tasks/status - 200 - 431.372µs"
time="2025-08-04 18:21:38" level=warning msg="Key has reached blacklist threshold, disabling." keyID=333 threshold=3
time="2025-08-04 18:21:39" level=info msg="GET /api/tasks/status - 200 - 195.664µs"
time="2025-08-04 18:21:40" level=info msg="GET /api/tasks/status - 200 - 789.375µs"
time="2025-08-04 18:21:42" level=info msg="GET /api/tasks/status - 200 - 393.631µs"
time="2025-08-04 18:21:43" level=info msg="GET /api/tasks/status - 200 - 416.784µs"
time="2025-08-04 18:21:44" level=info msg="GET /api/tasks/status - 200 - 264.624µs"
time="2025-08-04 18:21:46" level=warning msg="Key has reached blacklist threshold, disabling." keyID=436 threshold=3
time="2025-08-04 18:21:46" level=info msg="GET /api/tasks/status - 200 - 574.796µs"
time="2025-08-04 18:21:47" level=info msg="GET /api/tasks/status - 200 - 345.108µs"
time="2025-08-04 18:21:48" level=info msg="GET /api/tasks/status - 200 - 404.331µs"
time="2025-08-04 18:21:50" level=info msg="GET /api/tasks/status - 200 - 427.785µs"
time="2025-08-04 18:21:51" level=info msg="GET /api/tasks/status - 200 - 316.845µs"
time="2025-08-04 18:21:52" level=info msg="GET /api/tasks/status - 200 - 446.191µs"
time="2025-08-04 18:21:54" level=info msg="GET /api/tasks/status - 200 - 324.539µs"
time="2025-08-04 18:21:55" level=warning msg="Key has reached blacklist threshold, disabling." keyID=551 threshold=3
time="2025-08-04 18:21:55" level=info msg="GET /api/tasks/status - 200 - 316.182µs"
time="2025-08-04 18:21:56" level=info msg="GET /api/tasks/status - 200 - 342.062µs"
time="2025-08-04 18:21:58" level=info msg="GET /api/tasks/status - 200 - 351.169µs"
time="2025-08-04 18:21:59" level=warning msg="Key has reached blacklist threshold, disabling." keyID=618 threshold=3
time="2025-08-04 18:21:59" level=info msg="GET /api/tasks/status - 200 - 337.693µs"
time="2025-08-04 18:22:01" level=warning msg="Key has reached blacklist threshold, disabling." keyID=13463 threshold=3
time="2025-08-04 18:22:01" level=warning msg="Key has reached blacklist threshold, disabling." keyID=13464 threshold=3
time="2025-08-04 18:22:01" level=warning msg="Key has reached blacklist threshold, disabling." keyID=13468 threshold=3
time="2025-08-04 18:22:01" level=info msg="GET /api/tasks/status - 200 - 325.76µs"
time="2025-08-04 18:22:02" level=warning msg="Key has reached blacklist threshold, disabling." keyID=13481 threshold=3
time="2025-08-04 18:22:02" level=warning msg="Key has reached blacklist threshold, disabling." keyID=13486 threshold=3
time="2025-08-04 18:22:02" level=warning msg="Key has reached blacklist threshold, disabling." keyID=13492 threshold=3
time="2025-08-04 18:22:03" level=warning msg="Key has reached blacklist threshold, disabling." keyID=13504 threshold=3
time="2025-08-04 18:22:03" level=warning msg="Key has reached blacklist threshold, disabling." keyID=13508 threshold=3
time="2025-08-04 18:22:03" level=info msg="GET /api/tasks/status - 200 - 193.851µs"
time="2025-08-04 18:22:04" level=warning msg="Key has reached blacklist threshold, disabling." keyID=13526 threshold=3
time="2025-08-04 18:22:04" level=warning msg="Key has reached blacklist threshold, disabling." keyID=13533 threshold=3
time="2025-08-04 18:22:04" level=warning msg="Key has reached blacklist threshold, disabling." keyID=13536 threshold=3
time="2025-08-04 18:22:05" level=info msg="GET /api/tasks/status - 200 - 290.474µs"
time="2025-08-04 18:22:05" level=warning msg="Key has reached blacklist threshold, disabling." keyID=13551 threshold=3
time="2025-08-04 18:22:05" level=warning msg="Key has reached blacklist threshold, disabling." keyID=13554 threshold=3
time="2025-08-04 18:22:05" level=warning msg="Key has reached blacklist threshold, disabling." keyID=13560 threshold=3
time="2025-08-04 18:22:05" level=warning msg="Key has reached blacklist threshold, disabling." keyID=13561 threshold=3
time="2025-08-04 18:22:05" level=warning msg="Key has reached blacklist threshold, disabling." keyID=13570 threshold=3
time="2025-08-04 18:22:06" level=warning msg="Key has reached blacklist threshold, disabling." keyID=13579 threshold=3
time="2025-08-04 18:22:06" level=info msg="GET /api/logs?page=1&page_size=15 - 200 - 1.069919ms"
time="2025-08-04 18:22:06" level=info msg="GET /api/tasks/status - 200 - 317.836µs"
time="2025-08-04 18:22:07" level=warning msg="Key has reached blacklist threshold, disabling." keyID=13598 threshold=3
time="2025-08-04 18:22:07" level=warning msg="Key has reached blacklist threshold, disabling." keyID=13599 threshold=3
time="2025-08-04 18:22:07" level=warning msg="Key has reached blacklist threshold, disabling." keyID=13605 threshold=3
time="2025-08-04 18:22:07" level=warning msg="Key has reached blacklist threshold, disabling." keyID=13609 threshold=3
time="2025-08-04 18:22:07" level=warning msg="Key has reached blacklist threshold, disabling." keyID=13619 threshold=3
time="2025-08-04 18:22:07" level=warning msg="Key has reached blacklist threshold, disabling." keyID=13623 threshold=3
time="2025-08-04 18:22:07" level=info msg="GET /api/tasks/status - 200 - 311.543µs"
time="2025-08-04 18:22:08" level=warning msg="Key has reached blacklist threshold, disabling." keyID=13631 threshold=3
time="2025-08-04 18:22:08" level=warning msg="Key has reached blacklist threshold, disabling." keyID=13633 threshold=3
time="2025-08-04 18:22:08" level=warning msg="Key has reached blacklist threshold, disabling." keyID=13634 threshold=3
time="2025-08-04 18:22:08" level=warning msg="Key has reached blacklist threshold, disabling." keyID=13635 threshold=3
time="2025-08-04 18:22:08" level=warning msg="Key has reached blacklist threshold, disabling." keyID=13637 threshold=3
time="2025-08-04 18:22:08" level=warning msg="Key has reached blacklist threshold, disabling." keyID=13644 threshold=3
time="2025-08-04 18:22:08" level=warning msg="Key has reached blacklist threshold, disabling." keyID=13647 threshold=3
time="2025-08-04 18:22:08" level=warning msg="Key has reached blacklist threshold, disabling." keyID=13649 threshold=3
time="2025-08-04 18:22:08" level=warning msg="Key has reached blacklist threshold, disabling." keyID=13654 threshold=3
time="2025-08-04 18:22:08" level=warning msg="Key has reached blacklist threshold, disabling." keyID=13658 threshold=3
time="2025-08-04 18:22:09" level=info msg="GET /api/tasks/status - 200 - 362.671µs"
time="2025-08-04 18:22:09" level=warning msg="Key has reached blacklist threshold, disabling." keyID=13680 threshold=3
time="2025-08-04 18:22:09" level=warning msg="Key has reached blacklist threshold, disabling." keyID=13679 threshold=3
time="2025-08-04 18:22:09" level=warning msg="Key has reached blacklist threshold, disabling." keyID=13684 threshold=3
time="2025-08-04 18:22:10" level=warning msg="Key has reached blacklist threshold, disabling." keyID=13693 threshold=3
time="2025-08-04 18:22:10" level=info msg="GET /api/tasks/status - 200 - 352.502µs"
time="2025-08-04 18:22:10" level=warning msg="Key has reached blacklist threshold, disabling." keyID=13706 threshold=3
time="2025-08-04 18:22:10" level=warning msg="Key has reached blacklist threshold, disabling." keyID=13709 threshold=3
time="2025-08-04 18:22:10" level=warning msg="Key has reached blacklist threshold, disabling." keyID=13710 threshold=3
time="2025-08-04 18:22:10" level=warning msg="Key has reached blacklist threshold, disabling." keyID=13712 threshold=3
time="2025-08-04 18:22:11" level=warning msg="Key has reached blacklist threshold, disabling." keyID=13720 threshold=3
time="2025-08-04 18:22:11" level=warning msg="Key has reached blacklist threshold, disabling." keyID=13729 threshold=3
time="2025-08-04 18:22:11" level=warning msg="Key has reached blacklist threshold, disabling." keyID=13735 threshold=3
time="2025-08-04 18:22:11" level=warning msg="Key has reached blacklist threshold, disabling." keyID=13737 threshold=3
time="2025-08-04 18:22:11" level=warning msg="Key has reached blacklist threshold, disabling." keyID=13739 threshold=3
time="2025-08-04 18:22:11" level=warning msg="Key has reached blacklist threshold, disabling." keyID=13741 threshold=3
time="2025-08-04 18:22:11" level=info msg="GET /api/tasks/status - 200 - 411.204µs"
time="2025-08-04 18:22:12" level=warning msg="Key has reached blacklist threshold, disabling." keyID=13753 threshold=3
time="2025-08-04 18:22:12" level=warning msg="Key has reached blacklist threshold, disabling." keyID=13761 threshold=3
time="2025-08-04 18:22:12" level=warning msg="Key has reached blacklist threshold, disabling." keyID=13764 threshold=3
time="2025-08-04 18:22:12" level=warning msg="Key has reached blacklist threshold, disabling." keyID=13765 threshold=3
time="2025-08-04 18:22:12" level=warning msg="Key has reached blacklist threshold, disabling." keyID=13766 threshold=3
time="2025-08-04 18:22:13" level=warning msg="Key has reached blacklist threshold, disabling." keyID=13779 threshold=3
time="2025-08-04 18:22:13" level=info msg="GET /api/tasks/status - 200 - 300.914µs"
time="2025-08-04 18:22:13" level=warning msg="Key has reached blacklist threshold, disabling." keyID=13782 threshold=3
time="2025-08-04 18:22:13" level=warning msg="Key has reached blacklist threshold, disabling." keyID=13786 threshold=3
time="2025-08-04 18:22:13" level=warning msg="Key has reached blacklist threshold, disabling." keyID=13792 threshold=3
time="2025-08-04 18:22:14" level=warning msg="Key has reached blacklist threshold, disabling." keyID=13799 threshold=3
time="2025-08-04 18:22:14" level=warning msg="Key has reached blacklist threshold, disabling." keyID=13803 threshold=3
time="2025-08-04 18:22:14" level=warning msg="Key has reached blacklist threshold, disabling." keyID=13809 threshold=3
time="2025-08-04 18:22:14" level=warning msg="Key has reached blacklist threshold, disabling." keyID=13817 threshold=3
time="2025-08-04 18:22:14" level=info msg="GET /api/tasks/status - 200 - 619.391µs"
time="2025-08-04 18:22:14" level=warning msg="Key has reached blacklist threshold, disabling." keyID=13821 threshold=3
time="2025-08-04 18:22:14" level=warning msg="Key has reached blacklist threshold, disabling." keyID=13823 threshold=3
time="2025-08-04 18:22:14" level=warning msg="Key has reached blacklist threshold, disabling." keyID=13826 threshold=3
time="2025-08-04 18:22:14" level=warning msg="Key has reached blacklist threshold, disabling." keyID=13828 threshold=3
time="2025-08-04 18:22:14" level=warning msg="Key has reached blacklist threshold, disabling." keyID=13831 threshold=3
time="2025-08-04 18:22:15" level=warning msg="Key has reached blacklist threshold, disabling." keyID=13849 threshold=3
time="2025-08-04 18:22:15" level=info msg="GET /api/tasks/status - 200 - 238.435µs"
time="2025-08-04 18:22:16" level=warning msg="Key has reached blacklist threshold, disabling." keyID=13865 threshold=3
time="2025-08-04 18:22:16" level=warning msg="Key has reached blacklist threshold, disabling." keyID=13873 threshold=3
time="2025-08-04 18:22:17" level=info msg="GET /api/tasks/status - 200 - 349.116µs"
time="2025-08-04 18:22:17" level=warning msg="Key has reached blacklist threshold, disabling." keyID=13903 threshold=3
time="2025-08-04 18:22:17" level=warning msg="Key has reached blacklist threshold, disabling." keyID=13906 threshold=3
time="2025-08-04 18:22:17" level=warning msg="Key has reached blacklist threshold, disabling." keyID=13910 threshold=3
time="2025-08-04 18:22:18" level=warning msg="Key has reached blacklist threshold, disabling." keyID=13912 threshold=3
time="2025-08-04 18:22:18" level=warning msg="Key has reached blacklist threshold, disabling." keyID=13916 threshold=3
time="2025-08-04 18:22:18" level=warning msg="Key has reached blacklist threshold, disabling." keyID=13928 threshold=3
time="2025-08-04 18:22:18" level=info msg="GET /api/tasks/status - 200 - 416.584µs"
time="2025-08-04 18:22:18" level=warning msg="Key has reached blacklist threshold, disabling." keyID=13930 threshold=3
time="2025-08-04 18:22:18" level=warning msg="Key has reached blacklist threshold, disabling." keyID=13931 threshold=3
time="2025-08-04 18:22:18" level=warning msg="Key has reached blacklist threshold, disabling." keyID=13937 threshold=3
time="2025-08-04 18:22:19" level=warning msg="Key has reached blacklist threshold, disabling." keyID=13943 threshold=3
time="2025-08-04 18:22:19" level=warning msg="Key has reached blacklist threshold, disabling." keyID=13950 threshold=3
time="2025-08-04 18:22:19" level=error msg="Failed to handle key failure" error="failed to update key stats in DB: database is locked (5) (SQLITE_BUSY)" keyID=13951
time="2025-08-04 18:22:19" level=warning msg="Key has reached blacklist threshold, disabling." keyID=13960 threshold=3
time="2025-08-04 18:22:20" level=info msg="GET /api/tasks/status - 200 - 464.686µs"
time="2025-08-04 18:22:20" level=warning msg="Key has reached blacklist threshold, disabling." keyID=13965 threshold=3
time="2025-08-04 18:22:20" level=warning msg="Key has reached blacklist threshold, disabling." keyID=13970 threshold=3
time="2025-08-04 18:22:20" level=warning msg="Key has reached blacklist threshold, disabling." keyID=13980 threshold=3
time="2025-08-04 18:22:21" level=warning msg="Key has reached blacklist threshold, disabling." keyID=13992 threshold=3
time="2025-08-04 18:22:21" level=info msg="GET /api/tasks/status - 200 - 1.525487ms"
time="2025-08-04 18:22:21" level=warning msg="Key has reached blacklist threshold, disabling." keyID=14009 threshold=3
time="2025-08-04 18:22:22" level=warning msg="Key has reached blacklist threshold, disabling." keyID=14018 threshold=3
time="2025-08-04 18:22:22" level=warning msg="Key has reached blacklist threshold, disabling." keyID=14019 threshold=3
time="2025-08-04 18:22:22" level=warning msg="Key has reached blacklist threshold, disabling." keyID=14033 threshold=3
time="2025-08-04 18:22:22" level=info msg="GET /api/tasks/status - 200 - 352.221µs"
time="2025-08-04 18:22:22" level=warning msg="Key has reached blacklist threshold, disabling." keyID=14036 threshold=3
time="2025-08-04 18:22:23" level=warning msg="Key has reached blacklist threshold, disabling." keyID=14045 threshold=3
time="2025-08-04 18:22:23" level=warning msg="Key has reached blacklist threshold, disabling." keyID=14048 threshold=3
time="2025-08-04 18:22:23" level=warning msg="Key has reached blacklist threshold, disabling." keyID=14062 threshold=3
time="2025-08-04 18:22:24" level=info msg="GET /api/tasks/status - 200 - 538.176µs"
time="2025-08-04 18:22:24" level=warning msg="Key has reached blacklist threshold, disabling." keyID=14069 threshold=3
time="2025-08-04 18:22:24" level=warning msg="Key has reached blacklist threshold, disabling." keyID=14072 threshold=3
time="2025-08-04 18:22:24" level=warning msg="Key has reached blacklist threshold, disabling." keyID=14079 threshold=3
time="2025-08-04 18:22:24" level=warning msg="Key has reached blacklist threshold, disabling." keyID=14085 threshold=3
time="2025-08-04 18:22:25" level=info msg="GET /api/tasks/status - 200 - 424.639µs"
time="2025-08-04 18:22:25" level=warning msg="Key has reached blacklist threshold, disabling." keyID=14110 threshold=3
time="2025-08-04 18:22:25" level=warning msg="Key has reached blacklist threshold, disabling." keyID=14116 threshold=3
time="2025-08-04 18:22:26" level=warning msg="Key has reached blacklist threshold, disabling." keyID=14122 threshold=3
time="2025-08-04 18:22:26" level=warning msg="Key has reached blacklist threshold, disabling." keyID=14123 threshold=3
time="2025-08-04 18:22:26" level=warning msg="Key has reached blacklist threshold, disabling." keyID=14133 threshold=3
time="2025-08-04 18:22:26" level=warning msg="Key has reached blacklist threshold, disabling." keyID=14137 threshold=3
time="2025-08-04 18:22:26" level=warning msg="Key has reached blacklist threshold, disabling." keyID=14142 threshold=3
time="2025-08-04 18:22:26" level=info msg="GET /api/tasks/status - 200 - 381.658µs"
time="2025-08-04 18:22:28" level=info msg="GET /api/tasks/status - 200 - 475.928µs"
time="2025-08-04 18:22:29" level=info msg="GET /api/tasks/status - 200 - 434.418µs"
time="2025-08-04 18:22:31" level=warning msg="Key has reached blacklist threshold, disabling." keyID=14273 threshold=3
time="2025-08-04 18:22:31" level=info msg="GET /api/tasks/status - 200 - 503.209µs"
time="2025-08-04 18:22:32" level=warning msg="Key has reached blacklist threshold, disabling." keyID=14290 threshold=3
time="2025-08-04 18:22:33" level=info msg="GET /api/tasks/status - 200 - 411.124µs"
time="2025-08-04 18:22:34" level=info msg="GET /api/tasks/status - 200 - 321.313µs"
time="2025-08-04 18:22:34" level=info msg="GET /logs - 200 - 585.577µs"
time="2025-08-04 18:22:35" level=info msg="GET /api/tasks/status - 200 - 316.573µs"
time="2025-08-04 18:22:35" level=info msg="GET /api/logs?page=1&page_size=15 - 200 - 938.499µs"
time="2025-08-04 18:22:37" level=info msg="GET /api/tasks/status - 200 - 213.848µs"
time="2025-08-04 18:22:38" level=info msg="GET /api/tasks/status - 200 - 462.782µs"
time="2025-08-04 18:22:38" level=warning msg="Key has reached blacklist threshold, disabling." keyID=14444 threshold=3
time="2025-08-04 18:22:39" level=info msg="GET /api/tasks/status - 200 - 335.069µs"
time="2025-08-04 18:22:41" level=info msg="GET /api/tasks/status - 200 - 424.529µs"
time="2025-08-04 18:22:41" level=warning msg="Key has reached blacklist threshold, disabling." keyID=14534 threshold=3
time="2025-08-04 18:22:42" level=info msg="GET /api/tasks/status - 200 - 301.254µs"
time="2025-08-04 18:22:42" level=warning msg="Key has reached blacklist threshold, disabling." keyID=14570 threshold=3
time="2025-08-04 18:22:42" level=warning msg="Key has reached blacklist threshold, disabling." keyID=14573 threshold=3
time="2025-08-04 18:22:43" level=info msg="GET /logs - 200 - 588.381µs"
time="2025-08-04 18:22:43" level=info msg="GET /api/tasks/status - 200 - 265.597µs"
time="2025-08-04 18:22:44" level=info msg="GET /api/tasks/status - 200 - 410.983µs"
time="2025-08-04 18:22:44" level=info msg="GET /api/logs?page=1&page_size=15 - 200 - 1.480743ms"
time="2025-08-04 18:22:45" level=warning msg="Key has reached blacklist threshold, disabling." keyID=14634 threshold=3
time="2025-08-04 18:22:45" level=info msg="GET /api/tasks/status - 200 - 358.644µs"
time="2025-08-04 18:22:46" level=warning msg="Key has reached blacklist threshold, disabling." keyID=14682 threshold=3
time="2025-08-04 18:22:46" level=info msg="GET /api/tasks/status - 200 - 326.633µs"
time="2025-08-04 18:22:48" level=info msg="GET /api/tasks/status - 200 - 474.585µs"
time="2025-08-04 18:22:49" level=warning msg="Key has reached blacklist threshold, disabling." keyID=14743 threshold=3
time="2025-08-04 18:22:50" level=info msg="GET /api/tasks/status - 200 - 580.217µs"
time="2025-08-04 18:22:52" level=info msg="GET /api/tasks/status - 200 - 378.031µs"
time="2025-08-04 18:22:53" level=warning msg="Key has reached blacklist threshold, disabling." keyID=14858 threshold=3
time="2025-08-04 18:22:54" level=info msg="GET /api/tasks/status - 200 - 851.082µs"
time="2025-08-04 18:22:54" level=warning msg="Key has reached blacklist threshold, disabling." keyID=14893 threshold=3
time="2025-08-04 18:22:55" level=warning msg="Key has reached blacklist threshold, disabling." keyID=14911 threshold=3
time="2025-08-04 18:22:55" level=warning msg="Key has reached blacklist threshold, disabling." keyID=14919 threshold=3
time="2025-08-04 18:22:57" level=warning msg="Key has reached blacklist threshold, disabling." keyID=14966 threshold=3
time="2025-08-04 18:22:58" level=warning msg="Key has reached blacklist threshold, disabling." keyID=15005 threshold=3
time="2025-08-04 18:23:01" level=warning msg="Key has reached blacklist threshold, disabling." keyID=15069 threshold=3
time="2025-08-04 18:23:01" level=warning msg="Key has reached blacklist threshold, disabling." keyID=15078 threshold=3
time="2025-08-04 18:23:01" level=warning msg="Key has reached blacklist threshold, disabling." keyID=15083 threshold=3
time="2025-08-04 18:23:01" level=warning msg="Key has reached blacklist threshold, disabling." keyID=15093 threshold=3
time="2025-08-04 18:23:10" level=warning msg="Key has reached blacklist threshold, disabling." keyID=15339 threshold=3
time="2025-08-04 18:23:13" level=warning msg="Key has reached blacklist threshold, disabling." keyID=15407 threshold=3
time="2025-08-04 18:23:14" level=warning msg="Key has reached blacklist threshold, disabling." keyID=15440 threshold=3
time="2025-08-04 18:23:14" level=warning msg="Key has reached blacklist threshold, disabling." keyID=15450 threshold=3
time="2025-08-04 18:23:14" level=warning msg="Key has reached blacklist threshold, disabling." keyID=15457 threshold=3
time="2025-08-04 18:23:14" level=error msg="Failed to handle key failure" error="failed to update key stats in DB: database is locked (5) (SQLITE_BUSY)" keyID=15452
time="2025-08-04 18:23:14" level=warning msg="Key has reached blacklist threshold, disabling." keyID=15466 threshold=3
time="2025-08-04 18:23:14" level=info msg="Manual validation finished for group gemini: {TotalKeys:2448 ValidKeys:2304 InvalidKeys:144}"
time="2025-08-04 18:23:20" level=warning msg="POST /proxy/gemini/v1beta/models/gemini-2.5-pro:generateContent - 403 - 67.517804ms"
time="2025-08-04 18:23:25" level=warning msg="POST /proxy/gemini/v1beta/models/gemini-2.5-pro:generateContent - 403 - 68.555069ms"
time="2025-08-04 18:23:27" level=warning msg="POST /proxy/gemini/v1beta/models/gemini-2.5-flash:generateContent - 403 - 92.141556ms"
time="2025-08-04 18:23:28" level=warning msg="POST /proxy/gemini/v1beta/models/gemini-2.5-flash-preview-05-20:generateContent - 403 - 73.651442ms"
time="2025-08-04 18:23:36" level=info msg="GET /logs - 200 - 1.74168ms"
time="2025-08-04 18:23:37" level=info msg="GET /api/tasks/status - 200 - 252.481µs"
time="2025-08-04 18:23:39" level=warning msg="POST /proxy/gemini/v1beta/models/gemini-2.5-pro:generateContent - 403 - 71.355853ms"
time="2025-08-04 18:23:39" level=warning msg="POST /proxy/gemini/v1beta/models/gemini-2.5-flash:generateContent - 403 - 72.080934ms"
time="2025-08-04 18:23:39" level=warning msg="POST /proxy/gemini/v1beta/models/gemini-2.5-flash-preview-05-20:generateContent - 403 - 69.588753ms"
time="2025-08-04 18:23:45" level=info msg="GET /api/groups/config-options - 200 - 208.908µs"
time="2025-08-04 18:23:45" level=info msg="GET /api/groups - 200 - 1.480622ms"
time="2025-08-04 18:23:45" level=info msg="GET /api/groups/2/stats - 200 - 11.334343ms"
time="2025-08-04 18:23:45" level=info msg="GET /api/keys?group_id=2&page=1&page_size=12 - 200 - 14.568537ms"
time="2025-08-04 18:23:49" level=info msg="POST /api/keys/validate-group - 200 - 5.032352ms"
time="2025-08-04 18:23:49" level=info msg="Starting manual validation" group=gemini status=invalid
time="2025-08-04 18:23:49" level=info msg="GET /api/tasks/status - 200 - 283.47µs"
time="2025-08-04 18:23:49" level=info msg="Manual validation finished for group gemini: {TotalKeys:133 ValidKeys:0 InvalidKeys:133}"
time="2025-08-04 18:23:50" level=info msg="GET /api/tasks/status - 200 - 339.777µs"
time="2025-08-04 18:23:51" level=info msg="GET /api/groups/2/stats - 200 - 4.936248ms"
time="2025-08-04 18:23:51" level=info msg="GET /api/keys?group_id=2&page=1&page_size=12 - 200 - 6.573989ms"
time="2025-08-04 18:23:56" level=info msg="POST /api/keys/clear-all-invalid - 200 - 46.698101ms"
time="2025-08-04 18:23:57" level=info msg="GET /api/keys?group_id=2&page=1&page_size=12 - 200 - 7.833129ms"
time="2025-08-04 18:23:57" level=info msg="GET /api/groups/2/stats - 200 - 7.40337ms"
time="2025-08-04 18:24:00" level=warning msg="POST /proxy/gemini/v1beta/models/gemini-2.5-pro:generateContent - 403 - 68.318736ms"
time="2025-08-04 18:24:17" level=info msg="Successfully flushed 8 request logs."
time="2025-08-04 18:24:18" level=info msg="GET /api/settings - 200 - 292.588µs"
time="2025-08-04 18:24:31" level=warning msg="POST /proxy/gemini/v1beta/models/gemini-2.5-pro:generateContent - 403 - 74.000202ms"
time="2025-08-04 18:24:31" level=warning msg="POST /proxy/gemini/v1beta/models/gemini-2.5-flash:generateContent - 403 - 73.796143ms"
time="2025-08-04 18:24:31" level=warning msg="POST /proxy/gemini/v1beta/models/gemini-2.5-flash-preview-05-20:generateContent - 403 - 74.670479ms"
time="2025-08-04 18:24:49" level=info msg="GET /api/groups/config-options - 200 - 121.892µs"
time="2025-08-04 18:24:49" level=info msg="GET /api/groups - 200 - 872.712µs"
time="2025-08-04 18:24:50" level=info msg="GET /api/keys?group_id=2&page=1&page_size=12 - 200 - 8.621499ms"
time="2025-08-04 18:24:50" level=info msg="GET /api/groups/2/stats - 200 - 10.264159ms"
time="2025-08-04 18:24:51" level=info msg="GET /api/groups/list - 200 - 414.771µs"
time="2025-08-04 18:24:51" level=info msg="GET /api/dashboard/chart - 200 - 709.373µs"
time="2025-08-04 18:24:51" level=info msg="GET /api/dashboard/stats - 200 - 9.585787ms"
time="2025-08-04 18:25:17" level=info msg="Successfully flushed 3 request logs."
time="2025-08-04 18:27:09" level=info msg="GET / - 200 - 436.432µs"
time="2025-08-04 18:27:11" level=info msg="GET /api/tasks/status - 200 - 460.658µs"
time="2025-08-04 18:27:11" level=info msg="GET /api/dashboard/chart - 200 - 708.139µs"
time="2025-08-04 18:27:11" level=info msg="GET /api/groups/list - 200 - 3.140654ms"
time="2025-08-04 18:27:11" level=info msg="GET /api/dashboard/stats - 200 - 10.3626ms"
time="2025-08-04 18:27:13" level=info msg="GET /api/groups/config-options - 200 - 143.623µs"
time="2025-08-04 18:27:13" level=info msg="GET /api/groups - 200 - 581.237µs"
time="2025-08-04 18:27:14" level=info msg="GET /api/groups/2/stats - 200 - 8.801376ms"
time="2025-08-04 18:27:14" level=info msg="GET /api/keys?group_id=2&page=1&page_size=12 - 200 - 8.33519ms"
time="2025-08-04 18:27:19" level=info msg="GET /api/channel-types - 200 - 59.773µs"
time="2025-08-04 18:27:19" level=info msg="GET /api/groups/config-options - 200 - 232.173µs"
time="2025-08-04 18:29:44" level=info msg="Shutting down server..."
time="2025-08-04 18:29:44" level=info msg="HTTP server has been shut down."
time="2025-08-04 18:29:44" level=info msg="cache syncer stopped." syncer=system_settings
time="2025-08-04 18:29:44" level=info msg="CronChecker stopped gracefully."
time="2025-08-04 18:29:44" level=info msg="cache syncer stopped." syncer=groups
time="2025-08-04 18:29:44" level=info msg="RequestLogService stopped gracefully."
time="2025-08-04 18:29:44" level=info msg="LogCleanupService stopped gracefully."
time="2025-08-04 18:29:44" level=info msg="All background services stopped."
time="2025-08-04 18:29:44" level=info msg="Server exited gracefully"
time="2025-08-04 18:29:45" level=info msg="Starting as Master Node."
time="2025-08-04 18:29:45" level=info msg="Database auto-migration completed."
time="2025-08-04 18:29:45" level=info msg="System settings initialized in DB."
time="2025-08-04 18:29:45" level=info
time="2025-08-04 18:29:45" level=info msg="========= System Settings ========="
time="2025-08-04 18:29:45" level=info msg="  --- Basic Settings ---"
time="2025-08-04 18:29:45" level=info msg="    App URL: https://load.ainima.de"
time="2025-08-04 18:29:45" level=info msg="    Request Log Retention: 7 days"
time="2025-08-04 18:29:45" level=info msg="    Request Log Write Interval: 1 minutes"
time="2025-08-04 18:29:45" level=info msg="  --- Request Behavior ---"
time="2025-08-04 18:29:45" level=info msg="    Request Timeout: 600 seconds"
time="2025-08-04 18:29:45" level=info msg="    Connect Timeout: 15 seconds"
time="2025-08-04 18:29:45" level=info msg="    Response Header Timeout: 600 seconds"
time="2025-08-04 18:29:45" level=info msg="    Idle Connection Timeout: 120 seconds"
time="2025-08-04 18:29:45" level=info msg="    Max Idle Connections: 100"
time="2025-08-04 18:29:45" level=info msg="    Max Idle Connections Per Host: 50"
time="2025-08-04 18:29:45" level=info msg="  --- Key & Group Behavior ---"
time="2025-08-04 18:29:45" level=info msg="    Max Retries: 3"
time="2025-08-04 18:29:45" level=info msg="    Blacklist Threshold: 3"
time="2025-08-04 18:29:45" level=info msg="    Key Validation Interval: 60 minutes"
time="2025-08-04 18:29:45" level=info msg="===================================="
time="2025-08-04 18:29:45" level=info
time="2025-08-04 18:29:45" level=info msg="cache reloaded successfully" syncer=system_settings
time="2025-08-04 18:29:45" level=info msg="Updating active key lists for all groups..."
time="2025-08-04 18:29:45" level=info
time="2025-08-04 18:29:45" level=info msg="======= Server Configuration ======="
time="2025-08-04 18:29:45" level=info msg="  --- Server ---"
time="2025-08-04 18:29:45" level=info msg="    Listen Address: 0.0.0.0:3001"
time="2025-08-04 18:29:45" level=info msg="    Graceful Shutdown Timeout: 10 seconds"
time="2025-08-04 18:29:45" level=info msg="    Read Timeout: 60 seconds"
time="2025-08-04 18:29:45" level=info msg="    Write Timeout: 600 seconds"
time="2025-08-04 18:29:45" level=info msg="    Idle Timeout: 120 seconds"
time="2025-08-04 18:29:45" level=info msg="  --- Performance ---"
time="2025-08-04 18:29:45" level=info msg="    Max Concurrent Requests: 100"
time="2025-08-04 18:29:45" level=info msg="  --- Security ---"
time="2025-08-04 18:29:45" level=info msg="    Authentication: enabled (key loaded)"
time="2025-08-04 18:29:45" level=info msg="    CORS: enabled (Origins: *)"
time="2025-08-04 18:29:45" level=info msg="  --- Logging ---"
time="2025-08-04 18:29:45" level=info msg="    Log Level: info"
time="2025-08-04 18:29:45" level=info msg="    Log Format: text"
time="2025-08-04 18:29:45" level=info msg="    File Logging: true"
time="2025-08-04 18:29:45" level=info msg="    Log File Path: ./data/logs/app.log"
time="2025-08-04 18:29:45" level=info msg="  --- Dependencies ---"
time="2025-08-04 18:29:45" level=info msg="    Database: configured"
time="2025-08-04 18:29:45" level=info msg="    Redis: configured"
time="2025-08-04 18:29:45" level=info msg="===================================="
time="2025-08-04 18:29:45" level=info
time="2025-08-04 18:29:45" level=info msg="cache reloaded successfully" syncer=groups
time="2025-08-04 18:29:45" level=info msg="GPT-Load proxy server started successfully on Version: v1.0.18"
time="2025-08-04 18:29:45" level=info msg="Server address: http://0.0.0.0:3001"
time="2025-08-04 18:29:45" level=info
time="2025-08-04 18:29:55" level=info msg="GET / - 200 - 151.709µs"
time="2025-08-04 18:30:23" level=info msg="Shutting down server..."
time="2025-08-04 18:30:23" level=info msg="HTTP server has been shut down."
time="2025-08-04 18:30:23" level=info msg="cache syncer stopped." syncer=groups
time="2025-08-04 18:30:23" level=info msg="LogCleanupService stopped gracefully."
time="2025-08-04 18:30:23" level=info msg="cache syncer stopped." syncer=system_settings
time="2025-08-04 18:30:23" level=info msg="CronChecker stopped gracefully."
time="2025-08-04 18:30:23" level=info msg="RequestLogService stopped gracefully."
time="2025-08-04 18:30:23" level=info msg="All background services stopped."
time="2025-08-04 18:30:23" level=info msg="Server exited gracefully"
time="2025-08-04 18:30:29" level=info msg="Starting as Master Node."
time="2025-08-04 18:30:29" level=info msg="Database auto-migration completed."
time="2025-08-04 18:30:29" level=info msg="Initialized system setting: app_url = http://localhost:3001"
time="2025-08-04 18:30:29" level=info msg="Initialized system setting: request_log_retention_days = 7"
time="2025-08-04 18:30:29" level=info msg="Initialized system setting: request_log_write_interval_minutes = 1"
time="2025-08-04 18:30:29" level=info msg="Initialized system setting: proxy_keys = sk-redkaytop_success"
time="2025-08-04 18:30:29" level=info msg="Initialized system setting: request_timeout = 600"
time="2025-08-04 18:30:29" level=info msg="Initialized system setting: connect_timeout = 15"
time="2025-08-04 18:30:29" level=info msg="Initialized system setting: idle_conn_timeout = 120"
time="2025-08-04 18:30:29" level=info msg="Initialized system setting: response_header_timeout = 600"
time="2025-08-04 18:30:29" level=info msg="Initialized system setting: max_idle_conns = 100"
time="2025-08-04 18:30:29" level=info msg="Initialized system setting: max_idle_conns_per_host = 50"
time="2025-08-04 18:30:29" level=info msg="Initialized system setting: max_retries = 3"
time="2025-08-04 18:30:29" level=info msg="Initialized system setting: blacklist_threshold = 3"
time="2025-08-04 18:30:29" level=info msg="Initialized system setting: key_validation_interval_minutes = 60"
time="2025-08-04 18:30:29" level=info msg="Initialized system setting: key_validation_concurrency = 10"
time="2025-08-04 18:30:29" level=info msg="Initialized system setting: key_validation_timeout_seconds = 20"
time="2025-08-04 18:30:29" level=info msg="System settings initialized in DB."
time="2025-08-04 18:30:29" level=info
time="2025-08-04 18:30:29" level=info msg="========= System Settings ========="
time="2025-08-04 18:30:29" level=info msg="  --- Basic Settings ---"
time="2025-08-04 18:30:29" level=info msg="    App URL: http://localhost:3001"
time="2025-08-04 18:30:29" level=info msg="    Request Log Retention: 7 days"
time="2025-08-04 18:30:29" level=info msg="    Request Log Write Interval: 1 minutes"
time="2025-08-04 18:30:29" level=info msg="  --- Request Behavior ---"
time="2025-08-04 18:30:29" level=info msg="    Request Timeout: 600 seconds"
time="2025-08-04 18:30:29" level=info msg="    Connect Timeout: 15 seconds"
time="2025-08-04 18:30:29" level=info msg="    Response Header Timeout: 600 seconds"
time="2025-08-04 18:30:29" level=info msg="    Idle Connection Timeout: 120 seconds"
time="2025-08-04 18:30:29" level=info msg="    Max Idle Connections: 100"
time="2025-08-04 18:30:29" level=info msg="    Max Idle Connections Per Host: 50"
time="2025-08-04 18:30:29" level=info msg="  --- Key & Group Behavior ---"
time="2025-08-04 18:30:29" level=info msg="    Max Retries: 3"
time="2025-08-04 18:30:29" level=info msg="    Blacklist Threshold: 3"
time="2025-08-04 18:30:29" level=info msg="    Key Validation Interval: 60 minutes"
time="2025-08-04 18:30:29" level=info msg="===================================="
time="2025-08-04 18:30:29" level=info
time="2025-08-04 18:30:29" level=info msg="cache reloaded successfully" syncer=system_settings
time="2025-08-04 18:30:29" level=info
time="2025-08-04 18:30:29" level=info msg="======= Server Configuration ======="
time="2025-08-04 18:30:29" level=info msg="  --- Server ---"
time="2025-08-04 18:30:29" level=info msg="    Listen Address: 0.0.0.0:3001"
time="2025-08-04 18:30:29" level=info msg="    Graceful Shutdown Timeout: 10 seconds"
time="2025-08-04 18:30:29" level=info msg="    Read Timeout: 60 seconds"
time="2025-08-04 18:30:29" level=info msg="    Write Timeout: 600 seconds"
time="2025-08-04 18:30:29" level=info msg="    Idle Timeout: 120 seconds"
time="2025-08-04 18:30:29" level=info msg="  --- Performance ---"
time="2025-08-04 18:30:29" level=info msg="    Max Concurrent Requests: 100"
time="2025-08-04 18:30:29" level=info msg="  --- Security ---"
time="2025-08-04 18:30:29" level=info msg="    Authentication: enabled (key loaded)"
time="2025-08-04 18:30:29" level=info msg="    CORS: enabled (Origins: *)"
time="2025-08-04 18:30:29" level=info msg="  --- Logging ---"
time="2025-08-04 18:30:29" level=info msg="    Log Level: info"
time="2025-08-04 18:30:29" level=info msg="    Log Format: text"
time="2025-08-04 18:30:29" level=info msg="    File Logging: true"
time="2025-08-04 18:30:29" level=info msg="    Log File Path: ./data/logs/app.log"
time="2025-08-04 18:30:29" level=info msg="  --- Dependencies ---"
time="2025-08-04 18:30:29" level=info msg="    Database: configured"
time="2025-08-04 18:30:29" level=info msg="    Redis: configured"
time="2025-08-04 18:30:29" level=info msg="===================================="
time="2025-08-04 18:30:29" level=info
time="2025-08-04 18:30:29" level=info msg="cache reloaded successfully" syncer=groups
time="2025-08-04 18:30:29" level=info msg="GPT-Load proxy server started successfully on Version: v1.0.18"
time="2025-08-04 18:30:29" level=info msg="Server address: http://0.0.0.0:3001"
time="2025-08-04 18:30:29" level=info
time="2025-08-04 18:30:40" level=info msg="GET / - 200 - 5.07777ms"
time="2025-08-04 18:34:22" level=info msg="GET / - 200 - 942.275µs"
time="2025-08-04 18:34:23" level=info msg="GET /api/tasks/status - 200 - 563.574µs"
time="2025-08-04 18:34:23" level=info msg="GET /api/dashboard/chart - 200 - 2.069159ms"
time="2025-08-04 18:34:23" level=info msg="GET /api/groups/list - 200 - 3.071519ms"
time="2025-08-04 18:34:23" level=info msg="GET /api/dashboard/stats - 200 - 6.43939ms"
time="2025-08-04 18:34:26" level=info msg="GET /api/groups/config-options - 200 - 221.853µs"
time="2025-08-04 18:34:26" level=info msg="GET /api/groups - 200 - 1.127657ms"
time="2025-08-04 18:35:06" level=info msg="GET /api/dashboard/stats - 200 - 2.443553ms"
time="2025-08-04 18:35:06" level=info msg="GET /api/groups/list - 200 - 952.995µs"
time="2025-08-04 18:35:06" level=info msg="GET /api/dashboard/chart - 200 - 1.25018ms"
time="2025-08-04 18:35:06" level=info msg="GET /api/groups/config-options - 200 - 226.171µs"
time="2025-08-04 18:35:06" level=info msg="GET /api/groups - 200 - 643.304µs"
time="2025-08-04 18:35:09" level=info msg="GET /api/logs?page=1&page_size=15 - 200 - 1.676351ms"
time="2025-08-04 18:39:58" level=info msg="GET / - 200 - 566.578µs"
time="2025-08-04 18:39:59" level=info msg="GET /assets/index-BoejvLdB.css - 200 - 641.37µs"
time="2025-08-04 18:39:59" level=info msg="GET /assets/index-Bsu5oAg6.js - 200 - 64.596202ms"
time="2025-08-04 18:40:01" level=info msg="GET /assets/Login-DzpmRpZS.css - 200 - 280.384µs"
time="2025-08-04 18:40:01" level=info msg="GET /assets/Login-DUjC1gm4.js - 200 - 356.289µs"
time="2025-08-04 18:40:01" level=info msg="GET /assets/logo-BV-_qOqO.png - 200 - 4.261793ms"
time="2025-08-04 18:40:26" level=warning msg="POST /api/auth/login - 401 - 201.564µs"
time="2025-08-04 18:40:54" level=info msg="Shutting down server..."
time="2025-08-04 18:40:54" level=info msg="HTTP server has been shut down."
time="2025-08-04 18:40:54" level=info msg="cache syncer stopped." syncer=groups
time="2025-08-04 18:40:54" level=info msg="cache syncer stopped." syncer=system_settings
time="2025-08-04 18:40:54" level=info msg="CronChecker stopped gracefully."
time="2025-08-04 18:40:54" level=info msg="LogCleanupService stopped gracefully."
time="2025-08-04 18:40:54" level=info msg="RequestLogService stopped gracefully."
time="2025-08-04 18:40:54" level=info msg="All background services stopped."
time="2025-08-04 18:40:54" level=info msg="Server exited gracefully"
time="2025-08-04 18:40:55" level=info msg="Starting as Master Node."
time="2025-08-04 18:40:55" level=info msg="Database auto-migration completed."
time="2025-08-04 18:40:55" level=info msg="System settings initialized in DB."
time="2025-08-04 18:40:55" level=info
time="2025-08-04 18:40:55" level=info msg="========= System Settings ========="
time="2025-08-04 18:40:55" level=info msg="  --- Basic Settings ---"
time="2025-08-04 18:40:55" level=info msg="    App URL: https://load.ainima.de"
time="2025-08-04 18:40:55" level=info msg="    Request Log Retention: 7 days"
time="2025-08-04 18:40:55" level=info msg="    Request Log Write Interval: 1 minutes"
time="2025-08-04 18:40:55" level=info msg="  --- Request Behavior ---"
time="2025-08-04 18:40:55" level=info msg="    Request Timeout: 600 seconds"
time="2025-08-04 18:40:55" level=info msg="    Connect Timeout: 15 seconds"
time="2025-08-04 18:40:55" level=info msg="    Response Header Timeout: 600 seconds"
time="2025-08-04 18:40:55" level=info msg="    Idle Connection Timeout: 120 seconds"
time="2025-08-04 18:40:55" level=info msg="    Max Idle Connections: 100"
time="2025-08-04 18:40:55" level=info msg="    Max Idle Connections Per Host: 50"
time="2025-08-04 18:40:55" level=info msg="  --- Key & Group Behavior ---"
time="2025-08-04 18:40:55" level=info msg="    Max Retries: 3"
time="2025-08-04 18:40:55" level=info msg="    Blacklist Threshold: 3"
time="2025-08-04 18:40:55" level=info msg="    Key Validation Interval: 60 minutes"
time="2025-08-04 18:40:55" level=info msg="===================================="
time="2025-08-04 18:40:55" level=info
time="2025-08-04 18:40:55" level=info msg="cache reloaded successfully" syncer=system_settings
time="2025-08-04 18:40:55" level=info
time="2025-08-04 18:40:55" level=info msg="======= Server Configuration ======="
time="2025-08-04 18:40:55" level=info msg="  --- Server ---"
time="2025-08-04 18:40:55" level=info msg="    Listen Address: 0.0.0.0:3001"
time="2025-08-04 18:40:55" level=info msg="    Graceful Shutdown Timeout: 10 seconds"
time="2025-08-04 18:40:55" level=info msg="    Read Timeout: 60 seconds"
time="2025-08-04 18:40:55" level=info msg="    Write Timeout: 600 seconds"
time="2025-08-04 18:40:55" level=info msg="    Idle Timeout: 120 seconds"
time="2025-08-04 18:40:55" level=info msg="  --- Performance ---"
time="2025-08-04 18:40:55" level=info msg="    Max Concurrent Requests: 100"
time="2025-08-04 18:40:55" level=info msg="  --- Security ---"
time="2025-08-04 18:40:55" level=info msg="    Authentication: enabled (key loaded)"
time="2025-08-04 18:40:55" level=info msg="    CORS: enabled (Origins: *)"
time="2025-08-04 18:40:55" level=info msg="  --- Logging ---"
time="2025-08-04 18:40:55" level=info msg="    Log Level: info"
time="2025-08-04 18:40:55" level=info msg="    Log Format: text"
time="2025-08-04 18:40:55" level=info msg="    File Logging: true"
time="2025-08-04 18:40:55" level=info msg="    Log File Path: ./data/logs/app.log"
time="2025-08-04 18:40:55" level=info msg="  --- Dependencies ---"
time="2025-08-04 18:40:55" level=info msg="    Database: configured"
time="2025-08-04 18:40:55" level=info msg="    Redis: configured"
time="2025-08-04 18:40:55" level=info msg="===================================="
time="2025-08-04 18:40:55" level=info
time="2025-08-04 18:40:55" level=info msg="cache reloaded successfully" syncer=groups
time="2025-08-04 18:40:55" level=info msg="GPT-Load proxy server started successfully on Version: v1.0.18"
time="2025-08-04 18:40:55" level=info msg="Server address: http://0.0.0.0:3001"
time="2025-08-04 18:40:55" level=info
time="2025-08-04 18:41:07" level=info msg="GET / - 200 - 145.106µs"
time="2025-08-04 18:41:28" level=info msg="POST /api/auth/login - 200 - 191.704µs"
time="2025-08-04 18:41:28" level=info msg="GET /assets/Dashboard-CnqTJPiu.css - 200 - 463.864µs"
time="2025-08-04 18:41:28" level=info msg="GET /assets/Dashboard-C9hBWr32.js - 200 - 673.974µs"
time="2025-08-04 18:41:28" level=info msg="GET /assets/display-DuIJKIJ5.js - 200 - 1.603273ms"
time="2025-08-04 18:41:29" level=info msg="GET /api/tasks/status - 200 - 439.026µs"
time="2025-08-04 18:41:29" level=info msg="GET /api/dashboard/chart - 200 - 1.580939ms"
time="2025-08-04 18:41:29" level=info msg="GET /api/groups/list - 200 - 906.776µs"
time="2025-08-04 18:41:29" level=info msg="GET /api/dashboard/stats - 200 - 11.34783ms"
time="2025-08-04 18:41:47" level=info msg="GET /assets/ProxyKeysInput-CoLmi6p2.css - 200 - 176.736µs"
time="2025-08-04 18:41:47" level=info msg="GET /assets/Keys-TvQrChfu.css - 200 - 634.418µs"
time="2025-08-04 18:41:47" level=info msg="GET /assets/Keys-pxHFf5dG.js - 200 - 1.78599ms"
time="2025-08-04 18:41:47" level=info msg="GET /assets/Search-CWmTw_e5.js - 200 - 237.823µs"
time="2025-08-04 18:41:47" level=info msg="GET /assets/ProxyKeysInput-BSuO8Kst.js - 200 - 786.547µs"
time="2025-08-04 18:41:48" level=info msg="GET /api/groups/config-options - 200 - 285.483µs"
time="2025-08-04 18:41:48" level=info msg="GET /api/groups - 200 - 1.698513ms"
time="2025-08-04 18:41:49" level=info msg="GET /api/groups/2/stats - 200 - 13.628572ms"
time="2025-08-04 18:41:49" level=info msg="GET /api/keys?group_id=2&page=1&page_size=12 - 200 - 6.718538ms"
time="2025-08-04 18:43:17" level=info msg="POST /proxy/gemini/v1beta/models/gemini-2.5-pro:generateContent - 200 - 9.573529235s"
time="2025-08-04 18:43:18" level=info msg="POST /proxy/gemini/v1beta/models/gemini-2.5-flash:generateContent - 200 - 341.901051ms"
time="2025-08-04 18:43:19" level=info msg="POST /proxy/gemini/v1beta/models/gemini-2.5-flash-preview-05-20:generateContent - 200 - 466.498713ms"
time="2025-08-04 18:43:55" level=info msg="Successfully flushed 3 request logs."
time="2025-08-04 18:45:55" level=info msg="CronChecker: Group 'gemini' has no invalid keys to check."
time="2025-08-04 18:45:55" level=info msg="CronChecker: Group 'targon' has no invalid keys to check."
time="2025-08-04 18:49:55" level=info msg="Successfully flushed 1 request logs."
time="2025-08-04 18:50:17" level=info msg="POST /proxy/gemini/v1beta/models/gemini-2.5-pro:streamGenerateContent?alt=sse - 200 - 49.136368866s"
time="2025-08-04 18:51:55" level=info msg="Successfully flushed 1 request logs."
time="2025-08-04 18:51:55" level=info msg="POST /proxy/gemini/v1beta/models/gemini-2.5-pro:streamGenerateContent?alt=sse - 200 - 57.335359176s"
time="2025-08-04 18:52:56" level=info msg="POST /proxy/gemini/v1beta/models/gemini-2.5-pro:streamGenerateContent?alt=sse - 200 - 6.95154951s"
time="2025-08-04 18:53:55" level=info msg="Successfully flushed 1 request logs."
time="2025-08-04 18:58:41" level=error msg="Failed to select a key for group gemini on attempt 1: No active API keys available for this group"
time="2025-08-04 18:58:41" level=error msg="POST /proxy/gemini/v1beta/models/gemini-2.5-pro:generateContent - 503 - 1.984398ms"
time="2025-08-04 18:58:55" level=info msg="Successfully flushed 1 request logs."
time="2025-08-04 19:00:36" level=error msg="Failed to select a key for group gemini on attempt 1: No active API keys available for this group"
time="2025-08-04 19:00:36" level=error msg="POST /proxy/gemini/v1beta/models/gemini-2.5-pro:generateContent - 503 - 1.433137ms"
time="2025-08-04 19:00:55" level=info msg="Successfully flushed 1 request logs."
time="2025-08-04 19:09:02" level=error msg="Failed to select a key for group gemini on attempt 1: No active API keys available for this group"
time="2025-08-04 19:09:02" level=error msg="POST /proxy/gemini/v1beta/models/gemini-2.5-pro:streamGenerateContent?alt=sse - 503 - 2.051666ms"
time="2025-08-04 19:09:18" level=error msg="Failed to select a key for group gemini on attempt 1: No active API keys available for this group"
time="2025-08-04 19:09:18" level=error msg="POST /proxy/gemini/v1beta/models/gemini-2.5-pro:streamGenerateContent?alt=sse - 503 - 1.073463ms"
time="2025-08-04 19:09:34" level=info msg="GET / - 200 - 792.729µs"
time="2025-08-04 19:09:35" level=info msg="GET /api/groups/list - 200 - 820.993µs"
time="2025-08-04 19:09:35" level=info msg="GET /api/tasks/status - 200 - 457.35µs"
time="2025-08-04 19:09:35" level=info msg="GET /api/dashboard/chart - 200 - 1.213871ms"
time="2025-08-04 19:09:35" level=info msg="GET /api/dashboard/stats - 200 - 8.402945ms"
time="2025-08-04 19:09:39" level=info msg="GET /api/groups/config-options - 200 - 209.208µs"
time="2025-08-04 19:09:39" level=info msg="GET /api/groups - 200 - 2.037078ms"
time="2025-08-04 19:09:40" level=info msg="GET /api/keys?group_id=2&page=1&page_size=12 - 200 - 8.235594ms"
time="2025-08-04 19:09:40" level=info msg="GET /api/groups/2/stats - 200 - 11.088565ms"
time="2025-08-04 19:09:47" level=info msg="POST /api/keys/validate-group - 200 - 14.247558ms"
time="2025-08-04 19:09:47" level=info msg="Starting manual validation" group=gemini status=active
time="2025-08-04 19:09:48" level=info msg="GET /api/tasks/status - 200 - 479.462µs"
time="2025-08-04 19:09:49" level=info msg="GET /api/tasks/status - 200 - 385.535µs"
time="2025-08-04 19:09:50" level=info msg="GET /api/tasks/status - 200 - 316.192µs"
time="2025-08-04 19:09:52" level=info msg="GET /api/tasks/status - 200 - 407.836µs"
time="2025-08-04 19:09:53" level=info msg="GET /api/tasks/status - 200 - 284.692µs"
time="2025-08-04 19:09:55" level=info msg="GET /api/tasks/status - 200 - 207.996µs"
time="2025-08-04 19:09:55" level=info msg="Successfully flushed 2 request logs."
time="2025-08-04 19:09:56" level=info msg="GET /api/tasks/status - 200 - 309.248µs"
time="2025-08-04 19:09:57" level=info msg="GET /api/tasks/status - 200 - 547.321µs"
time="2025-08-04 19:09:59" level=info msg="GET /api/tasks/status - 200 - 292.527µs"
time="2025-08-04 19:10:00" level=info msg="GET /api/tasks/status - 200 - 384.131µs"
time="2025-08-04 19:10:02" level=info msg="GET /api/tasks/status - 200 - 283.69µs"
time="2025-08-04 19:10:03" level=info msg="GET /api/tasks/status - 200 - 363.451µs"
time="2025-08-04 19:10:04" level=info msg="GET /api/tasks/status - 200 - 293.969µs"
time="2025-08-04 19:10:06" level=info msg="GET /api/tasks/status - 200 - 676.218µs"
time="2025-08-04 19:10:07" level=info msg="GET /api/tasks/status - 200 - 407.967µs"
time="2025-08-04 19:10:08" level=info msg="GET /api/tasks/status - 200 - 400.112µs"
time="2025-08-04 19:10:10" level=info msg="GET /api/tasks/status - 200 - 445.277µs"
time="2025-08-04 19:10:11" level=info msg="GET /api/tasks/status - 200 - 363.371µs"
time="2025-08-04 19:10:13" level=info msg="GET /api/tasks/status - 200 - 494.401µs"
time="2025-08-04 19:10:14" level=info msg="GET /api/tasks/status - 200 - 479.974µs"
time="2025-08-04 19:10:15" level=info msg="GET /api/tasks/status - 200 - 455.146µs"
time="2025-08-04 19:10:16" level=info msg="GET /api/channel-types - 200 - 56.558µs"
time="2025-08-04 19:10:16" level=info msg="GET /api/groups/config-options - 200 - 303.369µs"
time="2025-08-04 19:10:17" level=info msg="GET /api/tasks/status - 200 - 474.454µs"
time="2025-08-04 19:10:18" level=info msg="GET /api/tasks/status - 200 - 340.507µs"
time="2025-08-04 19:10:19" level=info msg="GET /api/tasks/status - 200 - 380.544µs"
time="2025-08-04 19:10:21" level=info msg="GET /api/tasks/status - 200 - 517.946µs"
time="2025-08-04 19:10:22" level=info msg="GET /api/tasks/status - 200 - 346.75µs"
time="2025-08-04 19:10:24" level=info msg="GET /api/tasks/status - 200 - 372.239µs"
time="2025-08-04 19:10:25" level=info msg="GET /api/tasks/status - 200 - 310.581µs"
time="2025-08-04 19:10:26" level=info msg="GET /api/tasks/status - 200 - 235.028µs"
time="2025-08-04 19:10:28" level=info msg="GET /api/tasks/status - 200 - 442.242µs"
time="2025-08-04 19:10:29" level=info msg="GET /api/tasks/status - 200 - 341.811µs"
time="2025-08-04 19:10:45" level=info msg="GET /api/tasks/status - 200 - 344.847µs"
time="2025-08-04 19:10:46" level=info msg="GET /api/tasks/status - 200 - 374.834µs"
time="2025-08-04 19:10:48" level=info msg="GET /api/tasks/status - 200 - 242.743µs"
time="2025-08-04 19:10:49" level=info msg="GET /api/tasks/status - 200 - 267.709µs"
time="2025-08-04 19:10:50" level=info msg="GET /api/tasks/status - 200 - 329.607µs"
time="2025-08-04 19:10:52" level=info msg="GET /api/tasks/status - 200 - 304.8µs"
time="2025-08-04 19:10:53" level=info msg="GET /api/tasks/status - 200 - 355.457µs"
time="2025-08-04 19:10:55" level=info msg="GET /api/tasks/status - 200 - 251.599µs"
time="2025-08-04 19:10:56" level=info msg="GET /api/tasks/status - 200 - 536.621µs"
time="2025-08-04 19:10:57" level=info msg="GET /api/tasks/status - 200 - 287.006µs"
time="2025-08-04 19:10:59" level=info msg="GET /api/tasks/status - 200 - 387.657µs"
time="2025-08-04 19:11:00" level=info msg="GET /api/tasks/status - 200 - 281.826µs"
time="2025-08-04 19:11:01" level=info msg="GET /api/tasks/status - 200 - 240.939µs"
time="2025-08-04 19:11:03" level=info msg="GET /api/tasks/status - 200 - 409.739µs"
time="2025-08-04 19:11:04" level=info msg="GET /api/tasks/status - 200 - 455.356µs"
time="2025-08-04 19:11:05" level=info msg="GET /api/tasks/status - 200 - 421.282µs"
time="2025-08-04 19:11:07" level=info msg="GET /api/tasks/status - 200 - 229.627µs"
time="2025-08-04 19:11:08" level=info msg="GET /api/tasks/status - 200 - 440.939µs"
time="2025-08-04 19:11:10" level=info msg="GET /api/tasks/status - 200 - 370.625µs"
time="2025-08-04 19:11:11" level=info msg="GET /api/tasks/status - 200 - 400.332µs"
time="2025-08-04 19:11:12" level=info msg="GET /api/tasks/status - 200 - 264.403µs"
time="2025-08-04 19:11:14" level=info msg="GET /api/tasks/status - 200 - 332.913µs"
time="2025-08-04 19:11:15" level=info msg="GET /api/tasks/status - 200 - 335.549µs"
time="2025-08-04 19:11:16" level=info msg="GET /api/tasks/status - 200 - 413.147µs"
time="2025-08-04 19:11:18" level=info msg="GET /api/tasks/status - 200 - 496.305µs"
time="2025-08-04 19:11:19" level=info msg="GET /api/tasks/status - 200 - 217.624µs"
time="2025-08-04 19:11:21" level=info msg="GET /api/tasks/status - 200 - 243.314µs"
time="2025-08-04 19:11:22" level=info msg="GET /api/tasks/status - 200 - 482.369µs"
time="2025-08-04 19:11:23" level=info msg="GET /api/tasks/status - 200 - 384.732µs"
time="2025-08-04 19:11:25" level=info msg="GET /api/tasks/status - 200 - 344.936µs"
time="2025-08-04 19:11:26" level=info msg="GET /api/tasks/status - 200 - 312.624µs"
time="2025-08-04 19:11:27" level=info msg="GET /api/tasks/status - 200 - 288.018µs"
time="2025-08-04 19:11:29" level=info msg="GET /api/tasks/status - 200 - 556.58µs"
time="2025-08-04 19:11:30" level=info msg="GET /api/tasks/status - 200 - 226.23µs"
time="2025-08-04 19:11:32" level=info msg="GET /api/tasks/status - 200 - 351.078µs"
time="2025-08-04 19:11:33" level=info msg="GET /api/tasks/status - 200 - 333.035µs"
time="2025-08-04 19:11:34" level=info msg="Manual validation finished for group gemini: {TotalKeys:2315 ValidKeys:2267 InvalidKeys:48}"
time="2025-08-04 19:11:34" level=info msg="GET /api/tasks/status - 200 - 381.257µs"
time="2025-08-04 19:11:35" level=info msg="GET /api/groups/2/stats - 200 - 8.178516ms"
time="2025-08-04 19:11:35" level=info msg="GET /api/keys?group_id=2&page=1&page_size=12 - 200 - 7.707079ms"
time="2025-08-04 19:11:42" level=info msg="POST /api/keys/clear-all-invalid - 200 - 4.129741ms"
time="2025-08-04 19:11:43" level=info msg="GET /api/keys?group_id=2&page=1&page_size=12 - 200 - 7.208971ms"
time="2025-08-04 19:11:43" level=info msg="GET /api/groups/2/stats - 200 - 6.990916ms"
time="2025-08-04 19:11:47" level=info msg="POST /api/keys/test-multiple - 200 - 441.228989ms"
time="2025-08-04 19:11:48" level=info msg="GET /api/keys?group_id=2&page=1&page_size=12 - 200 - 7.08247ms"
time="2025-08-04 19:11:48" level=info msg="GET /api/groups/2/stats - 200 - 5.247828ms"
time="2025-08-04 19:13:10" level=warning msg="POST /proxy/gemini/v1beta/models/gemini-2.5-flash:generateContent - 403 - 73.857635ms"
time="2025-08-04 19:13:14" level=warning msg="POST /proxy/gemini/v1beta/models/gemini-2.5-pro:generateContent - 403 - 75.735619ms"
time="2025-08-04 19:13:15" level=warning msg="POST /proxy/gemini/v1beta/models/gemini-2.5-flash-preview-05-20:generateContent - 403 - 71.844533ms"
time="2025-08-04 19:13:55" level=info msg="Successfully flushed 3 request logs."
time="2025-08-04 19:14:51" level=warning msg="POST /proxy/gemini/v1beta/models/gemini-2.5-flash-preview-05-20:generateContent - 403 - 72.003044ms"
time="2025-08-04 19:14:55" level=info msg="Successfully flushed 1 request logs."
time="2025-08-04 19:16:26" level=warning msg="POST /proxy/gemini/v1beta/models/gemini-2.5-pro:streamGenerateContent?alt=sse - 403 - 124.598758ms"
time="2025-08-04 19:16:55" level=info msg="Successfully flushed 1 request logs."
time="2025-08-04 19:18:10" level=warning msg="POST /proxy/gemini/v1beta/models/gemini-2.5-pro:generateContent - 403 - 93.790361ms"
time="2025-08-04 19:18:10" level=warning msg="POST /proxy/gemini/v1beta/models/gemini-2.5-flash:generateContent - 403 - 71.877073ms"
time="2025-08-04 19:18:11" level=warning msg="POST /proxy/gemini/v1beta/models/gemini-2.5-flash-preview-05-20:generateContent - 403 - 73.532023ms"
time="2025-08-04 19:18:21" level=info msg="GET / - 200 - 1.648077ms"
time="2025-08-04 19:18:21" level=info msg="GET /api/dashboard/chart - 200 - 900.955µs"
time="2025-08-04 19:18:21" level=info msg="GET /api/groups/list - 200 - 1.123648ms"
time="2025-08-04 19:18:21" level=info msg="GET /api/tasks/status - 200 - 440.087µs"
time="2025-08-04 19:18:21" level=info msg="GET /api/dashboard/stats - 200 - 8.62207ms"
time="2025-08-04 19:18:32" level=info msg="GET /assets/Logs-DOB48F16.js - 200 - 853.905µs"
time="2025-08-04 19:18:32" level=info msg="GET /assets/Logs-jx6kyE3D.css - 200 - 336.911µs"
time="2025-08-04 19:18:33" level=info msg="GET /api/logs?page=1&page_size=15 - 200 - 3.014639ms"
time="2025-08-04 19:18:55" level=info msg="Successfully flushed 3 request logs."
time="2025-08-04 19:19:04" level=info msg="GET /api/groups/config-options - 200 - 259.274µs"
time="2025-08-04 19:19:04" level=info msg="GET /api/groups - 200 - 2.635225ms"
time="2025-08-04 19:19:05" level=info msg="GET /api/groups/2/stats - 200 - 5.849844ms"
time="2025-08-04 19:19:05" level=info msg="GET /api/keys?group_id=2&page=1&page_size=12 - 200 - 5.256846ms"
time="2025-08-04 19:19:26" level=info msg="POST /api/keys/validate-group - 200 - 11.431227ms"
time="2025-08-04 19:19:26" level=info msg="Starting manual validation" group=gemini status=all
time="2025-08-04 19:19:26" level=info msg="GET /api/tasks/status - 200 - 425.951µs"
time="2025-08-04 19:19:28" level=info msg="GET /api/tasks/status - 200 - 238.093µs"
time="2025-08-04 19:19:29" level=info msg="GET /api/tasks/status - 200 - 509.49µs"
time="2025-08-04 19:19:31" level=info msg="GET /api/tasks/status - 200 - 404.36µs"
time="2025-08-04 19:19:32" level=info msg="GET /api/tasks/status - 200 - 348.353µs"
time="2025-08-04 19:19:34" level=info msg="GET /api/tasks/status - 200 - 322.073µs"
time="2025-08-04 19:19:35" level=info msg="GET /api/tasks/status - 200 - 479.502µs"
time="2025-08-04 19:19:39" level=info msg="GET /api/tasks/status - 200 - 399.621µs"
time="2025-08-04 19:19:42" level=info msg="GET /api/tasks/status - 200 - 401.184µs"
time="2025-08-04 19:19:44" level=info msg="GET /api/tasks/status - 200 - 170.694µs"
time="2025-08-04 19:19:46" level=info msg="GET /api/tasks/status - 200 - 277.328µs"
time="2025-08-04 19:19:48" level=info msg="GET /api/tasks/status - 200 - 302.054µs"
time="2025-08-04 19:19:50" level=info msg="GET /api/tasks/status - 200 - 423.526µs"
time="2025-08-04 19:19:52" level=info msg="GET /api/tasks/status - 200 - 403.267µs"
time="2025-08-04 19:20:10" level=info msg="GET /api/tasks/status - 200 - 325.98µs"
time="2025-08-04 19:20:12" level=info msg="GET /api/tasks/status - 200 - 514.489µs"
time="2025-08-04 19:20:13" level=info msg="GET /api/tasks/status - 200 - 364.393µs"
time="2025-08-04 19:20:14" level=info msg="GET /api/tasks/status - 200 - 384.431µs"
time="2025-08-04 19:20:17" level=info msg="GET /api/tasks/status - 200 - 321.451µs"
time="2025-08-04 19:20:19" level=info msg="GET /api/tasks/status - 200 - 406.203µs"
time="2025-08-04 19:20:21" level=info msg="GET /api/tasks/status - 200 - 744.727µs"
time="2025-08-04 19:20:23" level=info msg="GET /api/tasks/status - 200 - 323.486µs"
time="2025-08-04 19:20:25" level=info msg="GET /api/tasks/status - 200 - 570.004µs"
time="2025-08-04 19:20:27" level=info msg="GET /api/tasks/status - 200 - 443.744µs"
time="2025-08-04 19:20:29" level=info msg="GET /api/tasks/status - 200 - 420.38µs"
time="2025-08-04 19:20:31" level=info msg="GET /api/tasks/status - 200 - 250.528µs"
time="2025-08-04 19:20:33" level=info msg="GET /api/tasks/status - 200 - 297.326µs"
time="2025-08-04 19:20:59" level=info msg="GET /api/tasks/status - 200 - 306.493µs"
time="2025-08-04 19:21:01" level=info msg="GET /api/tasks/status - 200 - 481.165µs"
time="2025-08-04 19:21:02" level=info msg="GET /api/tasks/status - 200 - 398.809µs"
time="2025-08-04 19:21:04" level=info msg="GET /api/tasks/status - 200 - 724.618µs"
time="2025-08-04 19:21:05" level=info msg="GET /api/tasks/status - 200 - 404.12µs"
time="2025-08-04 19:21:06" level=info msg="GET /api/tasks/status - 200 - 472.329µs"
time="2025-08-04 19:21:08" level=info msg="GET /api/tasks/status - 200 - 227.644µs"
time="2025-08-04 19:21:11" level=info msg="GET /api/tasks/status - 200 - 302.435µs"
time="2025-08-04 19:21:13" level=info msg="GET /api/tasks/status - 200 - 270.824µs"
time="2025-08-04 19:21:15" level=info msg="GET /api/tasks/status - 200 - 422.183µs"
time="2025-08-04 19:21:16" level=info msg="GET /api/tasks/status - 200 - 423.646µs"
time="2025-08-04 19:21:18" level=info msg="GET /api/tasks/status - 200 - 405.351µs"
time="2025-08-04 19:21:19" level=info msg="GET /api/tasks/status - 200 - 439.637µs"
time="2025-08-04 19:21:21" level=info msg="GET /api/tasks/status - 200 - 389.502µs"
time="2025-08-04 19:21:22" level=info msg="GET /api/tasks/status - 200 - 364.033µs"
time="2025-08-04 19:21:24" level=info msg="GET /api/tasks/status - 200 - 342.983µs"
time="2025-08-04 19:21:25" level=info msg="GET /api/tasks/status - 200 - 398.028µs"
time="2025-08-04 19:21:26" level=info msg="GET /api/tasks/status - 200 - 328.266µs"
time="2025-08-04 19:21:28" level=info msg="GET /api/tasks/status - 200 - 448.994µs"
time="2025-08-04 19:21:29" level=info msg="GET /api/tasks/status - 200 - 498.017µs"
time="2025-08-04 19:21:31" level=info msg="GET /api/tasks/status - 200 - 527.073µs"
time="2025-08-04 19:21:33" level=info msg="GET /api/tasks/status - 200 - 308.899µs"
time="2025-08-04 19:21:34" level=info msg="Manual validation finished for group gemini: {TotalKeys:2315 ValidKeys:2261 InvalidKeys:54}"
time="2025-08-04 19:21:34" level=info msg="GET /api/tasks/status - 200 - 485.744µs"
time="2025-08-04 19:21:34" level=info msg="GET /api/keys?group_id=2&page=1&page_size=12 - 200 - 5.382295ms"
time="2025-08-04 19:21:34" level=info msg="GET /api/groups/2/stats - 200 - 6.505742ms"
time="2025-08-04 19:21:44" level=info msg="POST /api/keys/clear-all-invalid - 200 - 3.422465ms"
time="2025-08-04 19:21:45" level=info msg="GET /api/keys?group_id=2&page=1&page_size=12 - 200 - 5.281211ms"
time="2025-08-04 19:21:45" level=info msg="GET /api/groups/2/stats - 200 - 5.362366ms"
time="2025-08-04 19:21:50" level=info msg="POST /api/keys/validate-group - 200 - 12.427563ms"
time="2025-08-04 19:21:50" level=info msg="Starting manual validation" group=gemini status=all
time="2025-08-04 19:21:50" level=info msg="GET /api/tasks/status - 200 - 413.758µs"
time="2025-08-04 19:21:51" level=info msg="GET /api/tasks/status - 200 - 379.163µs"
time="2025-08-04 19:21:53" level=info msg="GET /api/tasks/status - 200 - 390.835µs"
time="2025-08-04 19:21:56" level=info msg="GET /api/tasks/status - 200 - 342.081µs"
time="2025-08-04 19:21:59" level=info msg="GET /api/tasks/status - 200 - 381.918µs"
time="2025-08-04 19:22:01" level=info msg="GET /api/tasks/status - 200 - 638.375µs"
time="2025-08-04 19:22:03" level=info msg="GET /api/tasks/status - 200 - 373.551µs"
time="2025-08-04 19:22:05" level=info msg="GET /api/tasks/status - 200 - 414.429µs"
time="2025-08-04 19:22:07" level=info msg="GET /api/tasks/status - 200 - 332.402µs"
time="2025-08-04 19:22:09" level=info msg="GET /api/tasks/status - 200 - 329.436µs"
time="2025-08-04 19:22:11" level=info msg="GET /api/tasks/status - 200 - 546.48µs"
time="2025-08-04 19:22:13" level=info msg="GET /api/tasks/status - 200 - 524.409µs"
time="2025-08-04 19:22:15" level=info msg="GET /api/tasks/status - 200 - 402.215µs"
time="2025-08-04 19:22:17" level=info msg="GET /api/tasks/status - 200 - 514.689µs"
time="2025-08-04 19:22:19" level=info msg="GET /api/tasks/status - 200 - 532.383µs"
time="2025-08-04 19:22:21" level=info msg="GET /api/tasks/status - 200 - 421.642µs"
time="2025-08-04 19:22:32" level=warning msg="Key has reached blacklist threshold, disabling." keyID=13553 threshold=3
time="2025-08-04 19:22:40" level=warning msg="Key has reached blacklist threshold, disabling." keyID=13848 threshold=3
time="2025-08-04 19:22:44" level=warning msg="Key has reached blacklist threshold, disabling." keyID=13951 threshold=3
time="2025-08-04 19:22:47" level=warning msg="Key has reached blacklist threshold, disabling." keyID=13984 threshold=3
time="2025-08-04 19:23:34" level=info msg="GET /api/tasks/status - 200 - 453.703µs"
time="2025-08-04 19:23:35" level=warning msg="Key has reached blacklist threshold, disabling." keyID=15443 threshold=3
time="2025-08-04 19:23:35" level=info msg="GET /api/tasks/status - 200 - 585.716µs"
time="2025-08-04 19:23:36" level=warning msg="Key has reached blacklist threshold, disabling." keyID=15460 threshold=3
time="2025-08-04 19:23:36" level=warning msg="Key has reached blacklist threshold, disabling." keyID=15454 threshold=3
time="2025-08-04 19:23:36" level=warning msg="Key has reached blacklist threshold, disabling." keyID=15464 threshold=3
time="2025-08-04 19:23:36" level=warning msg="Key has reached blacklist threshold, disabling." keyID=15401 threshold=3
time="2025-08-04 19:23:37" level=info msg="GET /api/tasks/status - 200 - 392.657µs"
time="2025-08-04 19:23:38" level=info msg="GET /api/tasks/status - 200 - 324.157µs"
time="2025-08-04 19:23:38" level=warning msg="Key has reached blacklist threshold, disabling." keyID=15452 threshold=3
time="2025-08-04 19:23:39" level=info msg="GET /api/tasks/status - 200 - 388.99µs"
time="2025-08-04 19:23:41" level=warning msg="Key has reached blacklist threshold, disabling." keyID=15096 threshold=3
time="2025-08-04 19:23:41" level=info msg="GET /api/tasks/status - 200 - 450.347µs"
time="2025-08-04 19:23:42" level=info msg="GET /api/tasks/status - 200 - 543.574µs"
time="2025-08-04 19:23:44" level=info msg="GET /api/tasks/status - 200 - 626.253µs"
time="2025-08-04 19:23:45" level=info msg="GET /api/tasks/status - 200 - 333.654µs"
time="2025-08-04 19:23:47" level=info msg="GET /api/tasks/status - 200 - 322.413µs"
time="2025-08-04 19:23:48" level=info msg="GET /api/tasks/status - 200 - 355.787µs"
time="2025-08-04 19:23:48" level=info msg="Manual validation finished for group gemini: {TotalKeys:2315 ValidKeys:2278 InvalidKeys:37}"
time="2025-08-04 19:23:49" level=info msg="GET /api/tasks/status - 200 - 569.373µs"
time="2025-08-04 19:23:50" level=info msg="GET /api/keys?group_id=2&page=1&page_size=12 - 200 - 6.079151ms"
time="2025-08-04 19:23:50" level=info msg="GET /api/groups/2/stats - 200 - 7.701569ms"
time="2025-08-04 19:23:56" level=info msg="POST /api/keys/clear-all-invalid - 200 - 9.559685ms"
time="2025-08-04 19:23:57" level=info msg="GET /api/keys?group_id=2&page=1&page_size=12 - 200 - 7.080897ms"
time="2025-08-04 19:23:57" level=info msg="GET /api/groups/2/stats - 200 - 4.925555ms"
time="2025-08-04 19:24:02" level=info msg="POST /api/keys/test-multiple - 200 - 398.026731ms"
time="2025-08-04 19:24:02" level=info msg="GET /api/keys?group_id=2&page=1&page_size=12 - 200 - 9.269723ms"
time="2025-08-04 19:24:03" level=info msg="GET /api/groups/2/stats - 200 - 8.445744ms"
time="2025-08-04 19:25:27" level=warning msg="POST /proxy/gemini/v1beta/models/gemini-2.5-flash-preview-05-20:generateContent - 403 - 73.030896ms"
time="2025-08-04 19:25:29" level=warning msg="POST /proxy/gemini/v1beta/models/gemini-2.5-flash:generateContent - 403 - 75.288454ms"
time="2025-08-04 19:25:31" level=warning msg="POST /proxy/gemini/v1beta/models/gemini-2.5-flash-preview-05-20:generateContent - 403 - 73.935289ms"
time="2025-08-04 19:25:34" level=warning msg="POST /proxy/gemini/v1beta/models/gemini-2.5-flash-preview-05-20:generateContent - 403 - 74.553334ms"
time="2025-08-04 19:25:55" level=info msg="Successfully flushed 4 request logs."
time="2025-08-04 19:27:10" level=info msg="GET / - 200 - 429.778µs"
time="2025-08-04 19:27:11" level=info msg="GET /api/dashboard/stats - 200 - 8.956235ms"
time="2025-08-04 19:27:11" level=info msg="GET /api/groups/list - 200 - 730.4µs"
time="2025-08-04 19:27:11" level=info msg="GET /api/tasks/status - 200 - 901.175µs"
time="2025-08-04 19:27:11" level=info msg="GET /api/dashboard/chart - 200 - 1.963247ms"
time="2025-08-04 19:27:15" level=info msg="GET /api/groups/config-options - 200 - 134.957µs"
time="2025-08-04 19:27:15" level=info msg="GET /api/groups - 200 - 913.439µs"
time="2025-08-04 19:27:15" level=info msg="GET /api/keys?group_id=2&page=1&page_size=12 - 200 - 5.329062ms"
time="2025-08-04 19:27:15" level=info msg="GET /api/groups/2/stats - 200 - 5.734765ms"
time="2025-08-04 19:27:21" level=info msg="GET /assets/Settings-DfDSODzu.js - 200 - 370.074µs"
time="2025-08-04 19:27:21" level=info msg="GET /api/settings - 200 - 209.048µs"
time="2025-08-04 19:27:25" level=info msg="GET /api/groups/config-options - 200 - 144.866µs"
time="2025-08-04 19:27:25" level=info msg="GET /api/groups - 200 - 1.573323ms"
time="2025-08-04 19:27:25" level=info msg="GET /api/keys?group_id=2&page=1&page_size=12 - 200 - 4.227417ms"
time="2025-08-04 19:27:25" level=info msg="GET /api/groups/2/stats - 200 - 5.271923ms"
time="2025-08-04 19:27:28" level=info msg="GET /api/channel-types - 200 - 73.53µs"
time="2025-08-04 19:27:28" level=info msg="GET /api/groups/config-options - 200 - 167.539µs"
time="2025-08-04 19:27:56" level=info msg="GET /api/settings - 200 - 201.374µs"
time="2025-08-04 19:28:12" level=warning msg="GET /proxy/gemini/v1/models - 403 - 131.023638ms"
time="2025-08-04 19:28:17" level=warning msg="GET /proxy/gemini/v1/models - 403 - 127.732331ms"
time="2025-08-04 19:28:19" level=warning msg="GET /proxy/gemini/v1/models - 403 - 117.858318ms"
time="2025-08-04 19:28:25" level=warning msg="GET /proxy/gemini/v1/models - 403 - 110.832541ms"
time="2025-08-04 19:28:31" level=warning msg="GET /proxy/gemini/v1/models - 403 - 119.128772ms"
time="2025-08-04 19:28:32" level=warning msg="GET /proxy/gemini/v1/models - 403 - 95.909681ms"
time="2025-08-04 19:28:55" level=info msg="Successfully flushed 6 request logs."
time="2025-08-04 19:31:43" level=warning msg="POST /proxy/gemini/v1beta/models/gemini-2.5-pro:streamGenerateContent?alt=sse - 403 - 140.210344ms"
time="2025-08-04 19:31:55" level=info msg="Successfully flushed 1 request logs."
time="2025-08-04 19:50:55" level=info msg="CronChecker: Group 'targon' has no invalid keys to check."
time="2025-08-04 19:50:55" level=info msg="CronChecker: Group 'gemini' has no invalid keys to check."
time="2025-08-04 20:34:42" level=info msg="Shutting down server..."
time="2025-08-04 20:34:42" level=info msg="HTTP server has been shut down."
time="2025-08-04 20:34:42" level=info msg="cache syncer stopped." syncer=groups
time="2025-08-04 20:34:42" level=info msg="cache syncer stopped." syncer=system_settings
time="2025-08-04 20:34:42" level=info msg="CronChecker stopped gracefully."
time="2025-08-04 20:34:42" level=info msg="LogCleanupService stopped gracefully."
time="2025-08-04 20:34:42" level=info msg="RequestLogService stopped gracefully."
time="2025-08-04 20:34:42" level=info msg="All background services stopped."
time="2025-08-04 20:34:42" level=info msg="Server exited gracefully"
time="2025-08-04 20:34:43" level=info msg="Starting as Master Node."
time="2025-08-04 20:34:43" level=info msg="Database auto-migration completed."
time="2025-08-04 20:34:43" level=info msg="System settings initialized in DB."
time="2025-08-04 20:34:43" level=info
time="2025-08-04 20:34:43" level=info msg="========= System Settings ========="
time="2025-08-04 20:34:43" level=info msg="  --- Basic Settings ---"
time="2025-08-04 20:34:43" level=info msg="    App URL: https://load.ainima.de"
time="2025-08-04 20:34:43" level=info msg="    Request Log Retention: 7 days"
time="2025-08-04 20:34:43" level=info msg="    Request Log Write Interval: 1 minutes"
time="2025-08-04 20:34:43" level=info msg="  --- Request Behavior ---"
time="2025-08-04 20:34:43" level=info msg="    Request Timeout: 600 seconds"
time="2025-08-04 20:34:43" level=info msg="    Connect Timeout: 15 seconds"
time="2025-08-04 20:34:43" level=info msg="    Response Header Timeout: 600 seconds"
time="2025-08-04 20:34:43" level=info msg="    Idle Connection Timeout: 120 seconds"
time="2025-08-04 20:34:43" level=info msg="    Max Idle Connections: 100"
time="2025-08-04 20:34:43" level=info msg="    Max Idle Connections Per Host: 50"
time="2025-08-04 20:34:43" level=info msg="  --- Key & Group Behavior ---"
time="2025-08-04 20:34:43" level=info msg="    Max Retries: 3"
time="2025-08-04 20:34:43" level=info msg="    Blacklist Threshold: 3"
time="2025-08-04 20:34:43" level=info msg="    Key Validation Interval: 60 minutes"
time="2025-08-04 20:34:43" level=info msg="===================================="
time="2025-08-04 20:34:43" level=info
time="2025-08-04 20:34:43" level=info msg="cache reloaded successfully" syncer=system_settings
time="2025-08-04 20:34:43" level=info msg="Updating active key lists for all groups..."
time="2025-08-04 20:34:43" level=info
time="2025-08-04 20:34:43" level=info msg="======= Server Configuration ======="
time="2025-08-04 20:34:43" level=info msg="  --- Server ---"
time="2025-08-04 20:34:43" level=info msg="    Listen Address: 0.0.0.0:3001"
time="2025-08-04 20:34:43" level=info msg="    Graceful Shutdown Timeout: 10 seconds"
time="2025-08-04 20:34:43" level=info msg="    Read Timeout: 60 seconds"
time="2025-08-04 20:34:43" level=info msg="    Write Timeout: 600 seconds"
time="2025-08-04 20:34:43" level=info msg="    Idle Timeout: 120 seconds"
time="2025-08-04 20:34:43" level=info msg="  --- Performance ---"
time="2025-08-04 20:34:43" level=info msg="    Max Concurrent Requests: 100"
time="2025-08-04 20:34:43" level=info msg="  --- Security ---"
time="2025-08-04 20:34:43" level=info msg="    Authentication: enabled (key loaded)"
time="2025-08-04 20:34:43" level=info msg="    CORS: enabled (Origins: *)"
time="2025-08-04 20:34:43" level=info msg="  --- Logging ---"
time="2025-08-04 20:34:43" level=info msg="    Log Level: info"
time="2025-08-04 20:34:43" level=info msg="    Log Format: text"
time="2025-08-04 20:34:43" level=info msg="    File Logging: true"
time="2025-08-04 20:34:43" level=info msg="    Log File Path: ./data/logs/app.log"
time="2025-08-04 20:34:43" level=info msg="  --- Dependencies ---"
time="2025-08-04 20:34:43" level=info msg="    Database: configured"
time="2025-08-04 20:34:43" level=info msg="    Redis: configured"
time="2025-08-04 20:34:43" level=info msg="===================================="
time="2025-08-04 20:34:43" level=info
time="2025-08-04 20:34:43" level=info msg="cache reloaded successfully" syncer=groups
time="2025-08-04 20:34:43" level=info msg="GPT-Load proxy server started successfully on Version: v1.0.18"
time="2025-08-04 20:34:43" level=info msg="Server address: http://0.0.0.0:3001"
time="2025-08-04 20:34:43" level=info
time="2025-08-04 20:39:13" level=info msg="Shutting down server..."
time="2025-08-04 20:39:13" level=info msg="HTTP server has been shut down."
time="2025-08-04 20:39:13" level=info msg="cache syncer stopped." syncer=system_settings
time="2025-08-04 20:39:13" level=info msg="LogCleanupService stopped gracefully."
time="2025-08-04 20:39:13" level=info msg="CronChecker stopped gracefully."
time="2025-08-04 20:39:13" level=info msg="cache syncer stopped." syncer=groups
time="2025-08-04 20:39:13" level=info msg="RequestLogService stopped gracefully."
time="2025-08-04 20:39:13" level=info msg="All background services stopped."
time="2025-08-04 20:39:13" level=info msg="Server exited gracefully"
time="2025-08-04 20:39:21" level=info msg="Starting as Master Node."
time="2025-08-04 20:39:21" level=info msg="Database auto-migration completed."
time="2025-08-04 20:39:21" level=info msg="System settings initialized in DB."
time="2025-08-04 20:39:21" level=info
time="2025-08-04 20:39:21" level=info msg="========= System Settings ========="
time="2025-08-04 20:39:21" level=info msg="  --- Basic Settings ---"
time="2025-08-04 20:39:21" level=info msg="    App URL: https://load.ainima.de"
time="2025-08-04 20:39:21" level=info msg="    Request Log Retention: 7 days"
time="2025-08-04 20:39:21" level=info msg="    Request Log Write Interval: 1 minutes"
time="2025-08-04 20:39:21" level=info msg="  --- Request Behavior ---"
time="2025-08-04 20:39:21" level=info msg="    Request Timeout: 600 seconds"
time="2025-08-04 20:39:21" level=info msg="    Connect Timeout: 15 seconds"
time="2025-08-04 20:39:21" level=info msg="    Response Header Timeout: 600 seconds"
time="2025-08-04 20:39:21" level=info msg="    Idle Connection Timeout: 120 seconds"
time="2025-08-04 20:39:21" level=info msg="    Max Idle Connections: 100"
time="2025-08-04 20:39:21" level=info msg="    Max Idle Connections Per Host: 50"
time="2025-08-04 20:39:21" level=info msg="  --- Key & Group Behavior ---"
time="2025-08-04 20:39:21" level=info msg="    Max Retries: 3"
time="2025-08-04 20:39:21" level=info msg="    Blacklist Threshold: 3"
time="2025-08-04 20:39:21" level=info msg="    Key Validation Interval: 60 minutes"
time="2025-08-04 20:39:21" level=info msg="===================================="
time="2025-08-04 20:39:21" level=info
time="2025-08-04 20:39:21" level=info msg="cache reloaded successfully" syncer=system_settings
time="2025-08-04 20:39:21" level=info msg="Updating active key lists for all groups..."
time="2025-08-04 20:39:21" level=info
time="2025-08-04 20:39:21" level=info msg="======= Server Configuration ======="
time="2025-08-04 20:39:21" level=info msg="  --- Server ---"
time="2025-08-04 20:39:21" level=info msg="    Listen Address: 0.0.0.0:3001"
time="2025-08-04 20:39:21" level=info msg="    Graceful Shutdown Timeout: 10 seconds"
time="2025-08-04 20:39:21" level=info msg="    Read Timeout: 60 seconds"
time="2025-08-04 20:39:21" level=info msg="    Write Timeout: 600 seconds"
time="2025-08-04 20:39:21" level=info msg="    Idle Timeout: 120 seconds"
time="2025-08-04 20:39:21" level=info msg="  --- Performance ---"
time="2025-08-04 20:39:21" level=info msg="    Max Concurrent Requests: 100"
time="2025-08-04 20:39:21" level=info msg="  --- Security ---"
time="2025-08-04 20:39:21" level=info msg="    Authentication: enabled (key loaded)"
time="2025-08-04 20:39:21" level=info msg="    CORS: enabled (Origins: *)"
time="2025-08-04 20:39:21" level=info msg="  --- Logging ---"
time="2025-08-04 20:39:21" level=info msg="    Log Level: info"
time="2025-08-04 20:39:21" level=info msg="    Log Format: text"
time="2025-08-04 20:39:21" level=info msg="    File Logging: true"
time="2025-08-04 20:39:21" level=info msg="    Log File Path: ./data/logs/app.log"
time="2025-08-04 20:39:21" level=info msg="  --- Dependencies ---"
time="2025-08-04 20:39:21" level=info msg="    Database: configured"
time="2025-08-04 20:39:21" level=info msg="    Redis: configured"
time="2025-08-04 20:39:21" level=info msg="===================================="
time="2025-08-04 20:39:21" level=info
time="2025-08-04 20:39:21" level=info msg="cache reloaded successfully" syncer=groups
time="2025-08-04 20:39:21" level=info msg="GPT-Load proxy server started successfully on Version: v1.0.18"
time="2025-08-04 20:39:21" level=info msg="Server address: http://0.0.0.0:3001"
time="2025-08-04 20:39:21" level=info
time="2025-08-04 20:40:55" level=info msg="GET /api/groups - 200 - 1.578024ms"
time="2025-08-04 20:40:55" level=warning msg="GET /proxy/targon/v1/models - 401 - 166.969µs"
time="2025-08-04 20:40:55" level=warning msg="GET /proxy/gemini/v1/models - 401 - 49.714µs"
time="2025-08-04 20:54:21" level=info msg="CronChecker: Group 'gemini' has no invalid keys to check."
time="2025-08-04 20:54:21" level=info msg="CronChecker: Group 'targon' has no invalid keys to check."
time="2025-08-04 21:02:40" level=error msg="Failed to select a key for group gemini on attempt 1: No active API keys available for this group"
time="2025-08-04 21:02:40" level=error msg="POST /proxy/gemini/v1beta/models/gemini-2.5-flash-preview-05-20:generateContent - 503 - 1.243106ms"
time="2025-08-04 21:02:42" level=error msg="Failed to select a key for group gemini on attempt 1: No active API keys available for this group"
time="2025-08-04 21:02:42" level=error msg="POST /proxy/gemini/v1beta/models/gemini-2.5-flash:generateContent - 503 - 783.111µs"
time="2025-08-04 21:02:45" level=error msg="Failed to select a key for group gemini on attempt 1: No active API keys available for this group"
time="2025-08-04 21:02:45" level=error msg="POST /proxy/gemini/v1beta/models/gemini-2.5-pro:generateContent - 503 - 988.09µs"
time="2025-08-04 21:02:59" level=info msg="GET / - 200 - 730.791µs"
time="2025-08-04 21:03:01" level=info msg="GET /assets/index-BoejvLdB.css - 200 - 2.031657ms"
time="2025-08-04 21:03:01" level=info msg="GET /assets/index-Bsu5oAg6.js - 200 - 68.406373ms"
time="2025-08-04 21:03:03" level=info msg="GET /assets/Dashboard-C9hBWr32.js - 200 - 733.867µs"
time="2025-08-04 21:03:03" level=info msg="GET /assets/display-DuIJKIJ5.js - 200 - 311.292µs"
time="2025-08-04 21:03:03" level=info msg="GET /assets/Dashboard-CnqTJPiu.css - 200 - 514.329µs"
time="2025-08-04 21:03:04" level=info msg="GET /api/tasks/status - 200 - 410µs"
time="2025-08-04 21:03:04" level=info msg="GET /api/groups/list - 200 - 1.144569ms"
time="2025-08-04 21:03:04" level=info msg="GET /api/dashboard/chart - 200 - 1.230532ms"
time="2025-08-04 21:03:04" level=info msg="GET /api/dashboard/stats - 200 - 11.385037ms"
time="2025-08-04 21:03:04" level=info msg="GET /assets/logo-BV-_qOqO.png - 200 - 1.265989ms"
time="2025-08-04 21:03:18" level=info msg="GET /assets/ProxyKeysInput-BSuO8Kst.js - 200 - 2.186161ms"
time="2025-08-04 21:03:18" level=info msg="GET /assets/Keys-pxHFf5dG.js - 200 - 2.016457ms"
time="2025-08-04 21:03:18" level=info msg="GET /assets/Search-CWmTw_e5.js - 200 - 428.866µs"
time="2025-08-04 21:03:18" level=info msg="GET /assets/ProxyKeysInput-CoLmi6p2.css - 200 - 190.423µs"
time="2025-08-04 21:03:18" level=info msg="GET /assets/Keys-TvQrChfu.css - 200 - 728.175µs"
time="2025-08-04 21:03:19" level=info msg="GET /api/groups - 200 - 1.132336ms"
time="2025-08-04 21:03:19" level=info msg="GET /api/groups/config-options - 200 - 198.598µs"
time="2025-08-04 21:03:19" level=info msg="GET /api/keys?group_id=2&page=1&page_size=12 - 200 - 14.122866ms"
time="2025-08-04 21:03:19" level=info msg="GET /api/groups/2/stats - 200 - 17.765469ms"
time="2025-08-04 21:03:21" level=info msg="Successfully flushed 3 request logs."
time="2025-08-04 21:03:37" level=info msg="POST /api/keys/test-multiple - 200 - 15.004170799s"
time="2025-08-04 21:03:38" level=info msg="GET /api/keys?group_id=2&page=1&page_size=12 - 200 - 7.937245ms"
time="2025-08-04 21:03:38" level=info msg="GET /api/groups/2/stats - 200 - 8.379236ms"
time="2025-08-04 21:06:20" level=info msg="GET /assets/Settings-DfDSODzu.js - 200 - 496.265µs"
time="2025-08-04 21:06:21" level=info msg="GET /api/settings - 200 - 330.188µs"
time="2025-08-04 21:08:02" level=info msg="Shutting down server..."
time="2025-08-04 21:08:02" level=info msg="HTTP server has been shut down."
time="2025-08-04 21:08:02" level=info msg="cache syncer stopped." syncer=system_settings
time="2025-08-04 21:08:02" level=info msg="LogCleanupService stopped gracefully."
time="2025-08-04 21:08:02" level=info msg="CronChecker stopped gracefully."
time="2025-08-04 21:08:02" level=info msg="RequestLogService stopped gracefully."
time="2025-08-04 21:08:02" level=info msg="cache syncer stopped." syncer=groups
time="2025-08-04 21:08:02" level=info msg="All background services stopped."
time="2025-08-04 21:08:02" level=info msg="Server exited gracefully"
time="2025-08-04 21:08:03" level=info msg="Starting as Master Node."
time="2025-08-04 21:08:03" level=info msg="Database auto-migration completed."
time="2025-08-04 21:08:03" level=info msg="System settings initialized in DB."
time="2025-08-04 21:08:03" level=info
time="2025-08-04 21:08:03" level=info msg="========= System Settings ========="
time="2025-08-04 21:08:03" level=info msg="  --- Basic Settings ---"
time="2025-08-04 21:08:03" level=info msg="    App URL: https://load.ainima.de"
time="2025-08-04 21:08:03" level=info msg="    Request Log Retention: 7 days"
time="2025-08-04 21:08:03" level=info msg="    Request Log Write Interval: 1 minutes"
time="2025-08-04 21:08:03" level=info msg="  --- Request Behavior ---"
time="2025-08-04 21:08:03" level=info msg="    Request Timeout: 600 seconds"
time="2025-08-04 21:08:03" level=info msg="    Connect Timeout: 15 seconds"
time="2025-08-04 21:08:03" level=info msg="    Response Header Timeout: 600 seconds"
time="2025-08-04 21:08:03" level=info msg="    Idle Connection Timeout: 120 seconds"
time="2025-08-04 21:08:03" level=info msg="    Max Idle Connections: 100"
time="2025-08-04 21:08:03" level=info msg="    Max Idle Connections Per Host: 50"
time="2025-08-04 21:08:03" level=info msg="  --- Key & Group Behavior ---"
time="2025-08-04 21:08:03" level=info msg="    Max Retries: 3"
time="2025-08-04 21:08:03" level=info msg="    Blacklist Threshold: 3"
time="2025-08-04 21:08:03" level=info msg="    Key Validation Interval: 60 minutes"
time="2025-08-04 21:08:03" level=info msg="===================================="
time="2025-08-04 21:08:03" level=info
time="2025-08-04 21:08:03" level=info msg="cache reloaded successfully" syncer=system_settings
time="2025-08-04 21:08:03" level=info msg="Updating active key lists for all groups..."
time="2025-08-04 21:08:03" level=info
time="2025-08-04 21:08:03" level=info msg="======= Server Configuration ======="
time="2025-08-04 21:08:03" level=info msg="  --- Server ---"
time="2025-08-04 21:08:03" level=info msg="    Listen Address: 0.0.0.0:3001"
time="2025-08-04 21:08:03" level=info msg="    Graceful Shutdown Timeout: 10 seconds"
time="2025-08-04 21:08:03" level=info msg="    Read Timeout: 60 seconds"
time="2025-08-04 21:08:03" level=info msg="    Write Timeout: 600 seconds"
time="2025-08-04 21:08:03" level=info msg="    Idle Timeout: 120 seconds"
time="2025-08-04 21:08:03" level=info msg="  --- Performance ---"
time="2025-08-04 21:08:03" level=info msg="    Max Concurrent Requests: 100"
time="2025-08-04 21:08:03" level=info msg="  --- Security ---"
time="2025-08-04 21:08:03" level=info msg="    Authentication: enabled (key loaded)"
time="2025-08-04 21:08:03" level=info msg="    CORS: enabled (Origins: *)"
time="2025-08-04 21:08:03" level=info msg="  --- Logging ---"
time="2025-08-04 21:08:03" level=info msg="    Log Level: info"
time="2025-08-04 21:08:03" level=info msg="    Log Format: text"
time="2025-08-04 21:08:03" level=info msg="    File Logging: true"
time="2025-08-04 21:08:03" level=info msg="    Log File Path: ./data/logs/app.log"
time="2025-08-04 21:08:03" level=info msg="  --- Dependencies ---"
time="2025-08-04 21:08:03" level=info msg="    Database: configured"
time="2025-08-04 21:08:03" level=info msg="    Redis: configured"
time="2025-08-04 21:08:03" level=info msg="===================================="
time="2025-08-04 21:08:03" level=info
time="2025-08-04 21:08:03" level=info msg="cache reloaded successfully" syncer=groups
time="2025-08-04 21:08:03" level=info msg="GPT-Load proxy server started successfully on Version: v1.0.18"
time="2025-08-04 21:08:03" level=info msg="Server address: http://0.0.0.0:3001"
time="2025-08-04 21:08:03" level=info
time="2025-08-04 21:08:09" level=info msg="GET /settings - 200 - 544.477µs"
time="2025-08-04 21:08:10" level=info msg="GET /assets/index-BoejvLdB.css - 200 - 3.057178ms"
time="2025-08-04 21:08:10" level=info msg="GET /assets/index-Bsu5oAg6.js - 200 - 60.207169ms"
time="2025-08-04 21:08:13" level=info msg="GET /assets/ProxyKeysInput-BSuO8Kst.js - 200 - 1.739931ms"
time="2025-08-04 21:08:13" level=info msg="GET /assets/ProxyKeysInput-CoLmi6p2.css - 200 - 782.429µs"
time="2025-08-04 21:08:13" level=info msg="GET /api/settings - 200 - 349.285µs"
time="2025-08-04 21:08:13" level=info msg="GET /api/tasks/status - 200 - 305.141µs"
time="2025-08-04 21:08:14" level=info msg="GET /assets/logo-BV-_qOqO.png - 200 - 1.207728ms"
time="2025-08-04 21:08:29" level=info msg="Shutting down server..."
time="2025-08-04 21:08:29" level=info msg="HTTP server has been shut down."
time="2025-08-04 21:08:29" level=info msg="CronChecker stopped gracefully."
time="2025-08-04 21:08:29" level=info msg="LogCleanupService stopped gracefully."
time="2025-08-04 21:08:29" level=info msg="cache syncer stopped." syncer=system_settings
time="2025-08-04 21:08:29" level=info msg="cache syncer stopped." syncer=groups
time="2025-08-04 21:08:29" level=info msg="RequestLogService stopped gracefully."
time="2025-08-04 21:08:29" level=info msg="All background services stopped."
time="2025-08-04 21:08:29" level=info msg="Server exited gracefully"
time="2025-08-04 21:08:36" level=info msg="Starting as Master Node."
time="2025-08-04 21:08:36" level=info msg="Database auto-migration completed."
time="2025-08-04 21:08:36" level=info msg="System settings initialized in DB."
time="2025-08-04 21:08:36" level=info
time="2025-08-04 21:08:36" level=info msg="========= System Settings ========="
time="2025-08-04 21:08:36" level=info msg="  --- Basic Settings ---"
time="2025-08-04 21:08:36" level=info msg="    App URL: https://load.ainima.de"
time="2025-08-04 21:08:36" level=info msg="    Request Log Retention: 7 days"
time="2025-08-04 21:08:36" level=info msg="    Request Log Write Interval: 1 minutes"
time="2025-08-04 21:08:36" level=info msg="  --- Request Behavior ---"
time="2025-08-04 21:08:36" level=info msg="    Request Timeout: 600 seconds"
time="2025-08-04 21:08:36" level=info msg="    Connect Timeout: 15 seconds"
time="2025-08-04 21:08:36" level=info msg="    Response Header Timeout: 600 seconds"
time="2025-08-04 21:08:36" level=info msg="    Idle Connection Timeout: 120 seconds"
time="2025-08-04 21:08:36" level=info msg="    Max Idle Connections: 100"
time="2025-08-04 21:08:36" level=info msg="    Max Idle Connections Per Host: 50"
time="2025-08-04 21:08:36" level=info msg="  --- Key & Group Behavior ---"
time="2025-08-04 21:08:36" level=info msg="    Max Retries: 3"
time="2025-08-04 21:08:36" level=info msg="    Blacklist Threshold: 3"
time="2025-08-04 21:08:36" level=info msg="    Key Validation Interval: 60 minutes"
time="2025-08-04 21:08:36" level=info msg="===================================="
time="2025-08-04 21:08:36" level=info
time="2025-08-04 21:08:36" level=info msg="cache reloaded successfully" syncer=system_settings
time="2025-08-04 21:08:36" level=info
time="2025-08-04 21:08:36" level=info msg="======= Server Configuration ======="
time="2025-08-04 21:08:36" level=info msg="  --- Server ---"
time="2025-08-04 21:08:36" level=info msg="    Listen Address: 0.0.0.0:3001"
time="2025-08-04 21:08:36" level=info msg="    Graceful Shutdown Timeout: 10 seconds"
time="2025-08-04 21:08:36" level=info msg="    Read Timeout: 60 seconds"
time="2025-08-04 21:08:36" level=info msg="    Write Timeout: 600 seconds"
time="2025-08-04 21:08:36" level=info msg="    Idle Timeout: 120 seconds"
time="2025-08-04 21:08:36" level=info msg="  --- Performance ---"
time="2025-08-04 21:08:36" level=info msg="    Max Concurrent Requests: 100"
time="2025-08-04 21:08:36" level=info msg="  --- Security ---"
time="2025-08-04 21:08:36" level=info msg="    Authentication: enabled (key loaded)"
time="2025-08-04 21:08:36" level=info msg="    CORS: enabled (Origins: *)"
time="2025-08-04 21:08:36" level=info msg="  --- Logging ---"
time="2025-08-04 21:08:36" level=info msg="    Log Level: info"
time="2025-08-04 21:08:36" level=info msg="    Log Format: text"
time="2025-08-04 21:08:36" level=info msg="    File Logging: true"
time="2025-08-04 21:08:36" level=info msg="    Log File Path: ./data/logs/app.log"
time="2025-08-04 21:08:36" level=info msg="  --- Dependencies ---"
time="2025-08-04 21:08:36" level=info msg="    Database: configured"
time="2025-08-04 21:08:36" level=info msg="    Redis: configured"
time="2025-08-04 21:08:36" level=info msg="===================================="
time="2025-08-04 21:08:36" level=info
time="2025-08-04 21:08:36" level=info msg="cache reloaded successfully" syncer=groups
time="2025-08-04 21:08:36" level=info msg="GPT-Load proxy server started successfully on Version: v1.0.18"
time="2025-08-04 21:08:36" level=info msg="Server address: http://0.0.0.0:3001"
time="2025-08-04 21:08:36" level=info
time="2025-08-04 21:09:19" level=warning msg="GET /proxy/gemini/v1beta/models/gemini-2.0-flash-lite:generateContent - 401 - 148.443µs"
time="2025-08-04 21:12:51" level=info msg="Shutting down server..."
time="2025-08-04 21:12:51" level=info msg="HTTP server has been shut down."
time="2025-08-04 21:12:51" level=info msg="cache syncer stopped." syncer=groups
time="2025-08-04 21:12:51" level=info msg="CronChecker stopped gracefully."
time="2025-08-04 21:12:51" level=info msg="cache syncer stopped." syncer=system_settings
time="2025-08-04 21:12:51" level=info msg="LogCleanupService stopped gracefully."
time="2025-08-04 21:12:51" level=info msg="RequestLogService stopped gracefully."
time="2025-08-04 21:12:51" level=info msg="All background services stopped."
time="2025-08-04 21:12:51" level=info msg="Server exited gracefully"
time="2025-08-04 21:13:08" level=info msg="Starting as Master Node."
time="2025-08-04 21:13:08" level=info msg="Database auto-migration completed."
time="2025-08-04 21:13:08" level=info msg="System settings initialized in DB."
time="2025-08-04 21:13:08" level=info
time="2025-08-04 21:13:08" level=info msg="========= System Settings ========="
time="2025-08-04 21:13:08" level=info msg="  --- Basic Settings ---"
time="2025-08-04 21:13:08" level=info msg="    App URL: https://load.ainima.de"
time="2025-08-04 21:13:08" level=info msg="    Request Log Retention: 7 days"
time="2025-08-04 21:13:08" level=info msg="    Request Log Write Interval: 1 minutes"
time="2025-08-04 21:13:08" level=info msg="  --- Request Behavior ---"
time="2025-08-04 21:13:08" level=info msg="    Request Timeout: 600 seconds"
time="2025-08-04 21:13:08" level=info msg="    Connect Timeout: 15 seconds"
time="2025-08-04 21:13:08" level=info msg="    Response Header Timeout: 600 seconds"
time="2025-08-04 21:13:08" level=info msg="    Idle Connection Timeout: 120 seconds"
time="2025-08-04 21:13:08" level=info msg="    Max Idle Connections: 100"
time="2025-08-04 21:13:08" level=info msg="    Max Idle Connections Per Host: 50"
time="2025-08-04 21:13:08" level=info msg="  --- Key & Group Behavior ---"
time="2025-08-04 21:13:08" level=info msg="    Max Retries: 3"
time="2025-08-04 21:13:08" level=info msg="    Blacklist Threshold: 3"
time="2025-08-04 21:13:08" level=info msg="    Key Validation Interval: 60 minutes"
time="2025-08-04 21:13:08" level=info msg="===================================="
time="2025-08-04 21:13:08" level=info
time="2025-08-04 21:13:08" level=info msg="cache reloaded successfully" syncer=system_settings
time="2025-08-04 21:13:08" level=info
time="2025-08-04 21:13:08" level=info msg="======= Server Configuration ======="
time="2025-08-04 21:13:08" level=info msg="  --- Server ---"
time="2025-08-04 21:13:08" level=info msg="    Listen Address: 0.0.0.0:3001"
time="2025-08-04 21:13:08" level=info msg="    Graceful Shutdown Timeout: 10 seconds"
time="2025-08-04 21:13:08" level=info msg="    Read Timeout: 60 seconds"
time="2025-08-04 21:13:08" level=info msg="    Write Timeout: 600 seconds"
time="2025-08-04 21:13:08" level=info msg="    Idle Timeout: 120 seconds"
time="2025-08-04 21:13:08" level=info msg="  --- Performance ---"
time="2025-08-04 21:13:08" level=info msg="    Max Concurrent Requests: 100"
time="2025-08-04 21:13:08" level=info msg="  --- Security ---"
time="2025-08-04 21:13:08" level=info msg="    Authentication: enabled (key loaded)"
time="2025-08-04 21:13:08" level=info msg="    CORS: enabled (Origins: *)"
time="2025-08-04 21:13:08" level=info msg="  --- Logging ---"
time="2025-08-04 21:13:08" level=info msg="    Log Level: info"
time="2025-08-04 21:13:08" level=info msg="    Log Format: text"
time="2025-08-04 21:13:08" level=info msg="    File Logging: true"
time="2025-08-04 21:13:08" level=info msg="    Log File Path: ./data/logs/app.log"
time="2025-08-04 21:13:08" level=info msg="  --- Dependencies ---"
time="2025-08-04 21:13:08" level=info msg="    Database: configured"
time="2025-08-04 21:13:08" level=info msg="    Redis: configured"
time="2025-08-04 21:13:08" level=info msg="===================================="
time="2025-08-04 21:13:08" level=info
time="2025-08-04 21:13:08" level=info msg="cache reloaded successfully" syncer=groups
time="2025-08-04 21:13:08" level=info msg="GPT-Load proxy server started successfully on Version: v1.0.18"
time="2025-08-04 21:13:08" level=info msg="Server address: http://0.0.0.0:3001"
time="2025-08-04 21:13:08" level=info
time="2025-08-04 21:14:18" level=warning msg="GET /proxy/gemini/v1beta/models/gemini-2.0-flash-lite:generateContent - 401 - 76.796µs"
time="2025-08-04 21:15:49" level=warning msg="GET /proxy/gemini/v1beta/models/gemini-2.0-flash-lite:generateContent - 401 - 79.622µs"
time="2025-08-04 21:15:49" level=warning msg="GET /proxy/targon/v1/models - 401 - 55.687µs"
time="2025-08-04 21:22:29" level=info msg="GET /settings - 200 - 501.655µs"
time="2025-08-04 21:22:30" level=info msg="GET /assets/index-BoejvLdB.css - 200 - 1.214933ms"
time="2025-08-04 21:22:30" level=info msg="GET /assets/index-Bsu5oAg6.js - 200 - 67.812941ms"
time="2025-08-04 21:22:32" level=info msg="GET /assets/ProxyKeysInput-BSuO8Kst.js - 200 - 628.065µs"
time="2025-08-04 21:22:32" level=info msg="GET /assets/ProxyKeysInput-CoLmi6p2.css - 200 - 220.549µs"
time="2025-08-04 21:22:32" level=info msg="GET /assets/Settings-DfDSODzu.js - 200 - 817.205µs"
time="2025-08-04 21:22:33" level=info msg="GET /api/tasks/status - 200 - 448.493µs"
time="2025-08-04 21:22:33" level=info msg="GET /api/settings - 200 - 346.99µs"
time="2025-08-04 21:22:33" level=info msg="GET /assets/logo-BV-_qOqO.png - 200 - 1.133858ms"
time="2025-08-04 21:22:35" level=info msg="POST /proxy/gemini/v1beta/models/gemini-2.5-flash-preview-05-20:generateContent - 200 - 505.314193ms"
time="2025-08-04 21:22:37" level=info msg="POST /proxy/gemini/v1beta/models/gemini-2.5-flash:generateContent - 200 - 398.186896ms"
time="2025-08-04 21:22:44" level=info msg="POST /proxy/gemini/v1beta/models/gemini-2.5-pro:generateContent - 200 - 7.11573567s"
time="2025-08-04 21:23:08" level=info msg="Successfully flushed 3 request logs."
time="2025-08-04 21:51:32" level=warning msg="POST /proxy/targon/api/paas/v4/chat/completions - 401 - 56.286µs"
time="2025-08-04 21:51:42" level=info msg="GET / - 200 - 970.738µs"
time="2025-08-04 21:51:43" level=info msg="GET /assets/index-BoejvLdB.css - 200 - 1.463887ms"
time="2025-08-04 21:51:43" level=info msg="GET /assets/index-Bsu5oAg6.js - 200 - 69.262845ms"
time="2025-08-04 21:51:45" level=info msg="GET /assets/Dashboard-C9hBWr32.js - 200 - 1.078103ms"
time="2025-08-04 21:51:45" level=info msg="GET /assets/display-DuIJKIJ5.js - 200 - 178.59µs"
time="2025-08-04 21:51:45" level=info msg="GET /assets/Dashboard-CnqTJPiu.css - 200 - 326.412µs"
time="2025-08-04 21:51:45" level=info msg="GET /api/groups/list - 200 - 1.875942ms"
time="2025-08-04 21:51:45" level=info msg="GET /api/tasks/status - 200 - 307.435µs"
time="2025-08-04 21:51:45" level=info msg="GET /api/dashboard/chart - 200 - 1.409123ms"
time="2025-08-04 21:51:45" level=info msg="GET /api/dashboard/stats - 200 - 13.378074ms"
time="2025-08-04 21:51:46" level=info msg="GET /assets/logo-BV-_qOqO.png - 200 - 945.42µs"
time="2025-08-04 21:51:47" level=info msg="GET /assets/ProxyKeysInput-BSuO8Kst.js - 200 - 434.538µs"
time="2025-08-04 21:51:47" level=info msg="GET /assets/Keys-pxHFf5dG.js - 200 - 2.275724ms"
time="2025-08-04 21:51:47" level=info msg="GET /assets/Search-CWmTw_e5.js - 200 - 343.464µs"
time="2025-08-04 21:51:47" level=info msg="GET /assets/Keys-TvQrChfu.css - 200 - 878.644µs"
time="2025-08-04 21:51:47" level=info msg="GET /assets/ProxyKeysInput-CoLmi6p2.css - 200 - 363.652µs"
time="2025-08-04 21:51:48" level=info msg="GET /api/groups/config-options - 200 - 319.008µs"
time="2025-08-04 21:51:48" level=info msg="GET /api/groups - 200 - 2.50459ms"
time="2025-08-04 21:51:48" level=info msg="GET /api/keys?group_id=2&page=1&page_size=12 - 200 - 9.248122ms"
time="2025-08-04 21:51:48" level=info msg="GET /api/groups/2/stats - 200 - 13.449321ms"
time="2025-08-04 21:51:49" level=info msg="GET /assets/Settings-DfDSODzu.js - 200 - 486.327µs"
time="2025-08-04 21:51:51" level=info msg="GET /api/settings - 200 - 163.671µs"
time="2025-08-04 21:52:00" level=warning msg="POST /proxy/targon/api/paas/v4/chat/completions - 401 - 57.319µs"
time="2025-08-04 21:53:53" level=info msg="GET /api/groups/config-options - 200 - 139.396µs"
time="2025-08-04 21:53:53" level=info msg="GET /api/groups - 200 - 2.460455ms"
time="2025-08-04 21:53:54" level=info msg="GET /api/groups/2/stats - 200 - 5.985647ms"
time="2025-08-04 21:53:54" level=info msg="GET /api/keys?group_id=2&page=1&page_size=12 - 200 - 8.194713ms"
time="2025-08-04 21:54:06" level=info msg="GET /api/settings - 200 - 195.362µs"
time="2025-08-04 21:54:44" level=info msg="GET /api/groups/config-options - 200 - 177.006µs"
time="2025-08-04 21:54:44" level=info msg="GET /api/groups - 200 - 1.001316ms"
time="2025-08-04 21:54:45" level=info msg="GET /api/groups/2/stats - 200 - 6.619804ms"
time="2025-08-04 21:54:45" level=info msg="GET /api/keys?group_id=2&page=1&page_size=12 - 200 - 7.191192ms"
time="2025-08-04 21:54:48" level=info msg="POST /api/keys/test-multiple - 200 - 1.009275786s"
time="2025-08-04 21:54:49" level=info msg="GET /api/keys?group_id=2&page=1&page_size=12 - 200 - 5.934891ms"
time="2025-08-04 21:54:49" level=info msg="GET /api/groups/2/stats - 200 - 5.630551ms"
time="2025-08-04 21:58:08" level=info msg="CronChecker: Group 'targon' has no invalid keys to check."
time="2025-08-04 21:58:08" level=info msg="CronChecker: Group 'gemini' has no invalid keys to check."
time="2025-08-04 22:06:08" level=warning msg="Key has reached blacklist threshold, disabling." keyID=91 threshold=3
time="2025-08-04 22:06:08" level=error msg="Failed to select a key for group gemini on attempt 4: No active API keys available for this group"
time="2025-08-04 22:06:08" level=error msg="POST /proxy/gemini/v1beta/models/gemini-2.5-flash:generateContent - 503 - 249.971424ms"
time="2025-08-04 22:06:08" level=info msg="Successfully flushed 1 request logs."
time="2025-08-04 22:06:12" level=error msg="Failed to select a key for group gemini on attempt 1: No active API keys available for this group"
time="2025-08-04 22:06:12" level=error msg="POST /proxy/gemini/v1beta/models/gemini-2.5-pro:generateContent - 503 - 1.028317ms"
time="2025-08-04 22:06:13" level=error msg="Failed to select a key for group gemini on attempt 1: No active API keys available for this group"
time="2025-08-04 22:06:13" level=error msg="POST /proxy/gemini/v1beta/models/gemini-2.5-flash-preview-05-20:generateContent - 503 - 1.230692ms"
time="2025-08-04 22:06:22" level=error msg="Failed to select a key for group gemini on attempt 1: No active API keys available for this group"
time="2025-08-04 22:06:22" level=error msg="POST /proxy/gemini/v1beta/models/gemini-2.5-flash-preview-05-20:generateContent - 503 - 980.656µs"
time="2025-08-04 22:06:23" level=error msg="Failed to select a key for group gemini on attempt 1: No active API keys available for this group"
time="2025-08-04 22:06:23" level=error msg="POST /proxy/gemini/v1beta/models/gemini-2.5-flash:generateContent - 503 - 1.242195ms"
time="2025-08-04 22:06:24" level=error msg="Failed to select a key for group gemini on attempt 1: No active API keys available for this group"
time="2025-08-04 22:06:24" level=error msg="POST /proxy/gemini/v1beta/models/gemini-2.5-flash:generateContent - 503 - 823.858µs"
time="2025-08-04 22:06:25" level=error msg="Failed to select a key for group gemini on attempt 1: No active API keys available for this group"
time="2025-08-04 22:06:25" level=error msg="POST /proxy/gemini/v1beta/models/gemini-2.5-flash:generateContent - 503 - 737.223µs"
time="2025-08-04 22:06:31" level=info msg="GET / - 200 - 604.532µs"
time="2025-08-04 22:06:32" level=info msg="GET /assets/index-BoejvLdB.css - 200 - 2.5794ms"
time="2025-08-04 22:06:32" level=info msg="GET /assets/index-Bsu5oAg6.js - 200 - 58.593147ms"
time="2025-08-04 22:06:34" level=info msg="GET /assets/Dashboard-CnqTJPiu.css - 200 - 463.974µs"
time="2025-08-04 22:06:34" level=info msg="GET /assets/display-DuIJKIJ5.js - 200 - 557.821µs"
time="2025-08-04 22:06:34" level=info msg="GET /assets/Dashboard-C9hBWr32.js - 200 - 1.126906ms"
time="2025-08-04 22:06:35" level=info msg="GET /api/groups/list - 200 - 1.104212ms"
time="2025-08-04 22:06:35" level=info msg="GET /api/dashboard/stats - 200 - 7.943962ms"
time="2025-08-04 22:06:35" level=info msg="GET /api/tasks/status - 200 - 422.614µs"
time="2025-08-04 22:06:35" level=info msg="GET /api/dashboard/chart - 200 - 1.021425ms"
time="2025-08-04 22:06:35" level=info msg="GET /assets/logo-BV-_qOqO.png - 200 - 1.245391ms"
time="2025-08-04 22:06:37" level=info msg="GET /assets/Search-CWmTw_e5.js - 200 - 355.998µs"
time="2025-08-04 22:06:37" level=info msg="GET /assets/ProxyKeysInput-BSuO8Kst.js - 200 - 391.614µs"
time="2025-08-04 22:06:37" level=info msg="GET /assets/Keys-TvQrChfu.css - 200 - 711.074µs"
time="2025-08-04 22:06:37" level=info msg="GET /assets/ProxyKeysInput-CoLmi6p2.css - 200 - 211.393µs"
time="2025-08-04 22:06:37" level=info msg="GET /assets/Keys-pxHFf5dG.js - 200 - 3.380114ms"
time="2025-08-04 22:06:38" level=info msg="GET /api/groups/config-options - 200 - 202.585µs"
time="2025-08-04 22:06:38" level=info msg="GET /api/groups - 200 - 1.353215ms"
time="2025-08-04 22:06:38" level=info msg="GET /api/groups/2/stats - 200 - 6.393711ms"
time="2025-08-04 22:06:38" level=info msg="GET /api/keys?group_id=2&page=1&page_size=12 - 200 - 8.848284ms"
time="2025-08-04 22:06:42" level=info msg="POST /api/keys/test-multiple - 200 - 453.586699ms"
time="2025-08-04 22:06:42" level=info msg="GET /api/keys?group_id=2&page=1&page_size=12 - 200 - 5.774763ms"
time="2025-08-04 22:06:43" level=info msg="GET /api/groups/2/stats - 200 - 6.023566ms"
time="2025-08-04 22:06:47" level=info msg="POST /api/keys/test-multiple - 200 - 454.315076ms"
time="2025-08-04 22:06:47" level=info msg="GET /api/keys?group_id=2&page=1&page_size=12 - 200 - 7.229672ms"
time="2025-08-04 22:06:47" level=info msg="GET /api/groups/2/stats - 200 - 6.384383ms"
time="2025-08-04 22:06:50" level=info msg="GET /api/keys?group_id=1&page=1&page_size=12 - 200 - 7.86397ms"
time="2025-08-04 22:06:51" level=info msg="GET /api/groups/1/stats - 200 - 9.88167ms"
time="2025-08-04 22:06:52" level=info msg="GET /api/groups/2/stats - 200 - 6.085565ms"
time="2025-08-04 22:06:52" level=info msg="GET /api/keys?group_id=2&page=1&page_size=12 - 200 - 5.478769ms"
time="2025-08-04 22:06:56" level=info msg="GET /assets/Settings-DfDSODzu.js - 200 - 406.564µs"
time="2025-08-04 22:06:57" level=info msg="GET /api/settings - 200 - 152.972µs"
time="2025-08-04 22:07:08" level=info msg="Successfully flushed 6 request logs."
time="2025-08-04 22:07:12" level=warning msg="POST /proxy/gemini/v1beta/models/gemini-2.5-flash:generateContent - 403 - 273.263809ms"
time="2025-08-04 22:08:08" level=info msg="Successfully flushed 1 request logs."
time="2025-08-04 22:08:41" level=error msg="Failed to select a key for group targon on attempt 1: No active API keys available for this group"
time="2025-08-04 22:08:41" level=error msg="POST /proxy/targon/v4/chat/completions - 503 - 1.21894ms"
time="2025-08-04 22:08:49" level=info msg="GET /api/groups/config-options - 200 - 160.636µs"
time="2025-08-04 22:08:49" level=info msg="GET /api/groups - 200 - 861.75µs"
time="2025-08-04 22:08:49" level=info msg="GET /api/groups/2/stats - 200 - 6.058643ms"
time="2025-08-04 22:08:49" level=info msg="GET /api/keys?group_id=2&page=1&page_size=12 - 200 - 7.057474ms"
time="2025-08-04 22:08:50" level=info msg="GET /api/groups/1/stats - 200 - 7.34408ms"
time="2025-08-04 22:08:50" level=info msg="GET /api/keys?group_id=1&page=1&page_size=12 - 200 - 8.464733ms"
time="2025-08-04 22:08:54" level=info msg="POST /api/keys/test-multiple - 200 - 2.290749309s"
time="2025-08-04 22:08:55" level=info msg="GET /api/keys?group_id=1&page=1&page_size=12 - 200 - 8.958021ms"
time="2025-08-04 22:08:55" level=info msg="GET /api/groups/1/stats - 200 - 6.788773ms"
time="2025-08-04 22:09:08" level=info msg="Successfully flushed 1 request logs."
time="2025-08-04 22:09:14" level=info msg="GET /api/keys?group_id=2&page=1&page_size=12 - 200 - 5.613927ms"
time="2025-08-04 22:09:14" level=info msg="GET /api/groups/2/stats - 200 - 5.801513ms"
time="2025-08-04 22:09:21" level=info msg="GET /assets/Logs-jx6kyE3D.css - 200 - 285.874µs"
time="2025-08-04 22:09:21" level=info msg="GET /assets/Logs-DOB48F16.js - 200 - 577.399µs"
time="2025-08-04 22:09:22" level=info msg="GET /api/logs?page=1&page_size=15 - 200 - 2.374289ms"
time="2025-08-04 22:14:31" level=warning msg="Key has reached blacklist threshold, disabling." keyID=868 threshold=3
time="2025-08-04 22:14:31" level=error msg="POST /proxy/targon/v1/chat/completions - 500 - 1.198273421s"
time="2025-08-04 22:14:39" level=warning msg="Key has reached blacklist threshold, disabling." keyID=88 threshold=3
time="2025-08-04 22:14:39" level=warning msg="Key has reached blacklist threshold, disabling." keyID=91 threshold=3
time="2025-08-04 22:14:39" level=error msg="Failed to select a key for group gemini on attempt 3: No active API keys available for this group"
time="2025-08-04 22:14:39" level=error msg="POST /proxy/gemini/v1beta/models/gemini-2.5-flash:generateContent - 503 - 170.402703ms"
time="2025-08-04 22:15:08" level=info msg="Successfully flushed 2 request logs."
time="2025-08-04 23:03:08" level=info msg="CronChecker: Group 'gemini' validation finished. Total checked: 2, became valid: 1. Duration: 553.692011ms."
time="2025-08-04 23:03:10" level=info msg="CronChecker: Group 'targon' validation finished. Total checked: 1, became valid: 1. Duration: 1.758949248s."
time="2025-08-05 00:08:08" level=info msg="CronChecker: Group 'targon' has no invalid keys to check."
time="2025-08-05 00:08:09" level=info msg="CronChecker: Group 'gemini' validation finished. Total checked: 1, became valid: 1. Duration: 1.083000904s."
time="2025-08-05 00:53:08" level=error msg="CronChecker: Failed to get groups: ERROR: relation \"groups\" does not exist (SQLSTATE 42P01)"
time="2025-08-05 00:58:08" level=error msg="CronChecker: Failed to get groups: ERROR: relation \"groups\" does not exist (SQLSTATE 42P01)"
time="2025-08-05 01:03:08" level=error msg="CronChecker: Failed to get groups: ERROR: relation \"groups\" does not exist (SQLSTATE 42P01)"
time="2025-08-05 01:08:08" level=error msg="CronChecker: Failed to get groups: ERROR: relation \"groups\" does not exist (SQLSTATE 42P01)"
time="2025-08-05 01:13:08" level=error msg="Failed to cleanup expired request logs" error="ERROR: relation \"request_logs\" does not exist (SQLSTATE 42P01)"
time="2025-08-05 01:13:08" level=error msg="CronChecker: Failed to get groups: ERROR: relation \"groups\" does not exist (SQLSTATE 42P01)"
time="2025-08-05 01:18:08" level=error msg="CronChecker: Failed to get groups: ERROR: relation \"groups\" does not exist (SQLSTATE 42P01)"
time="2025-08-05 01:23:08" level=error msg="CronChecker: Failed to get groups: ERROR: relation \"groups\" does not exist (SQLSTATE 42P01)"
time="2025-08-05 01:28:08" level=error msg="CronChecker: Failed to get groups: ERROR: relation \"groups\" does not exist (SQLSTATE 42P01)"
time="2025-08-05 01:33:08" level=error msg="CronChecker: Failed to get groups: ERROR: relation \"groups\" does not exist (SQLSTATE 42P01)"
time="2025-08-05 01:38:08" level=error msg="CronChecker: Failed to get groups: ERROR: relation \"groups\" does not exist (SQLSTATE 42P01)"
time="2025-08-05 01:43:08" level=error msg="CronChecker: Failed to get groups: ERROR: relation \"groups\" does not exist (SQLSTATE 42P01)"
time="2025-08-05 01:48:08" level=error msg="CronChecker: Failed to get groups: ERROR: relation \"groups\" does not exist (SQLSTATE 42P01)"
time="2025-08-05 01:53:08" level=error msg="CronChecker: Failed to get groups: ERROR: relation \"groups\" does not exist (SQLSTATE 42P01)"
time="2025-08-05 01:58:08" level=error msg="CronChecker: Failed to get groups: ERROR: relation \"groups\" does not exist (SQLSTATE 42P01)"
time="2025-08-05 02:03:08" level=error msg="CronChecker: Failed to get groups: ERROR: relation \"groups\" does not exist (SQLSTATE 42P01)"
time="2025-08-05 02:08:08" level=error msg="CronChecker: Failed to get groups: ERROR: relation \"groups\" does not exist (SQLSTATE 42P01)"
time="2025-08-05 02:13:08" level=error msg="CronChecker: Failed to get groups: ERROR: relation \"groups\" does not exist (SQLSTATE 42P01)"
time="2025-08-05 02:18:08" level=error msg="CronChecker: Failed to get groups: ERROR: relation \"groups\" does not exist (SQLSTATE 42P01)"
time="2025-08-05 02:20:44" level=info msg="GET / - 200 - 2.015369ms"
time="2025-08-05 02:20:44" level=info msg="GET /uuid - 200 - 232.952µs"
time="2025-08-05 02:20:44" level=info msg="GET /anything/test - 200 - 2.013676ms"
time="2025-08-05 02:23:08" level=error msg="CronChecker: Failed to get groups: ERROR: relation \"groups\" does not exist (SQLSTATE 42P01)"
time="2025-08-05 02:28:08" level=error msg="CronChecker: Failed to get groups: ERROR: relation \"groups\" does not exist (SQLSTATE 42P01)"
time="2025-08-05 02:33:08" level=error msg="CronChecker: Failed to get groups: ERROR: relation \"groups\" does not exist (SQLSTATE 42P01)"
time="2025-08-05 02:38:08" level=error msg="CronChecker: Failed to get groups: ERROR: relation \"groups\" does not exist (SQLSTATE 42P01)"
time="2025-08-05 02:43:08" level=error msg="CronChecker: Failed to get groups: ERROR: relation \"groups\" does not exist (SQLSTATE 42P01)"
time="2025-08-05 02:48:08" level=error msg="CronChecker: Failed to get groups: ERROR: relation \"groups\" does not exist (SQLSTATE 42P01)"
time="2025-08-05 02:53:08" level=error msg="CronChecker: Failed to get groups: ERROR: relation \"groups\" does not exist (SQLSTATE 42P01)"
time="2025-08-05 02:58:08" level=error msg="CronChecker: Failed to get groups: ERROR: relation \"groups\" does not exist (SQLSTATE 42P01)"
time="2025-08-05 03:03:08" level=error msg="CronChecker: Failed to get groups: ERROR: relation \"groups\" does not exist (SQLSTATE 42P01)"
time="2025-08-05 03:08:08" level=error msg="CronChecker: Failed to get groups: ERROR: relation \"groups\" does not exist (SQLSTATE 42P01)"
time="2025-08-05 03:13:08" level=error msg="Failed to cleanup expired request logs" error="ERROR: relation \"request_logs\" does not exist (SQLSTATE 42P01)"
time="2025-08-05 03:13:08" level=error msg="CronChecker: Failed to get groups: ERROR: relation \"groups\" does not exist (SQLSTATE 42P01)"
time="2025-08-05 03:18:08" level=error msg="CronChecker: Failed to get groups: ERROR: relation \"groups\" does not exist (SQLSTATE 42P01)"
time="2025-08-05 03:23:08" level=error msg="CronChecker: Failed to get groups: ERROR: relation \"groups\" does not exist (SQLSTATE 42P01)"
time="2025-08-05 03:23:12" level=info msg="GET / - 200 - 307.415µs"
time="2025-08-05 03:28:08" level=error msg="CronChecker: Failed to get groups: ERROR: relation \"groups\" does not exist (SQLSTATE 42P01)"
time="2025-08-05 03:33:08" level=error msg="CronChecker: Failed to get groups: ERROR: relation \"groups\" does not exist (SQLSTATE 42P01)"
time="2025-08-05 03:38:08" level=error msg="CronChecker: Failed to get groups: ERROR: relation \"groups\" does not exist (SQLSTATE 42P01)"
time="2025-08-05 03:43:08" level=error msg="CronChecker: Failed to get groups: ERROR: relation \"groups\" does not exist (SQLSTATE 42P01)"
time="2025-08-05 03:48:08" level=error msg="CronChecker: Failed to get groups: ERROR: relation \"groups\" does not exist (SQLSTATE 42P01)"
time="2025-08-05 03:53:08" level=error msg="CronChecker: Failed to get groups: ERROR: relation \"groups\" does not exist (SQLSTATE 42P01)"
time="2025-08-05 03:58:08" level=error msg="CronChecker: Failed to get groups: ERROR: relation \"groups\" does not exist (SQLSTATE 42P01)"
time="2025-08-05 04:03:08" level=error msg="CronChecker: Failed to get groups: ERROR: relation \"groups\" does not exist (SQLSTATE 42P01)"
time="2025-08-05 04:08:08" level=error msg="CronChecker: Failed to get groups: ERROR: relation \"groups\" does not exist (SQLSTATE 42P01)"
time="2025-08-05 04:13:08" level=error msg="CronChecker: Failed to get groups: ERROR: relation \"groups\" does not exist (SQLSTATE 42P01)"
time="2025-08-05 04:18:08" level=error msg="CronChecker: Failed to get groups: ERROR: relation \"groups\" does not exist (SQLSTATE 42P01)"
time="2025-08-05 04:23:08" level=error msg="CronChecker: Failed to get groups: ERROR: relation \"groups\" does not exist (SQLSTATE 42P01)"
time="2025-08-05 04:28:08" level=error msg="CronChecker: Failed to get groups: ERROR: relation \"groups\" does not exist (SQLSTATE 42P01)"
time="2025-08-05 04:33:08" level=error msg="CronChecker: Failed to get groups: ERROR: relation \"groups\" does not exist (SQLSTATE 42P01)"
time="2025-08-05 04:38:08" level=error msg="CronChecker: Failed to get groups: ERROR: relation \"groups\" does not exist (SQLSTATE 42P01)"
time="2025-08-05 04:43:08" level=error msg="CronChecker: Failed to get groups: ERROR: relation \"groups\" does not exist (SQLSTATE 42P01)"
time="2025-08-05 04:48:08" level=error msg="CronChecker: Failed to get groups: ERROR: relation \"groups\" does not exist (SQLSTATE 42P01)"
time="2025-08-05 04:53:08" level=error msg="CronChecker: Failed to get groups: ERROR: relation \"groups\" does not exist (SQLSTATE 42P01)"
time="2025-08-05 04:58:08" level=error msg="CronChecker: Failed to get groups: ERROR: relation \"groups\" does not exist (SQLSTATE 42P01)"
time="2025-08-05 05:03:08" level=error msg="CronChecker: Failed to get groups: ERROR: relation \"groups\" does not exist (SQLSTATE 42P01)"
time="2025-08-05 05:08:08" level=error msg="CronChecker: Failed to get groups: ERROR: relation \"groups\" does not exist (SQLSTATE 42P01)"
time="2025-08-05 05:13:08" level=error msg="Failed to cleanup expired request logs" error="ERROR: relation \"request_logs\" does not exist (SQLSTATE 42P01)"
time="2025-08-05 05:13:08" level=error msg="CronChecker: Failed to get groups: ERROR: relation \"groups\" does not exist (SQLSTATE 42P01)"
time="2025-08-05 05:18:08" level=error msg="CronChecker: Failed to get groups: ERROR: relation \"groups\" does not exist (SQLSTATE 42P01)"
time="2025-08-05 05:23:08" level=error msg="CronChecker: Failed to get groups: ERROR: relation \"groups\" does not exist (SQLSTATE 42P01)"
time="2025-08-05 05:28:08" level=error msg="CronChecker: Failed to get groups: ERROR: relation \"groups\" does not exist (SQLSTATE 42P01)"
time="2025-08-05 05:31:08" level=warning msg="GET /api/v1/users - 404 - 1.718501ms"
time="2025-08-05 05:31:08" level=warning msg="GET /api/users - 404 - 1.671451ms"
time="2025-08-05 05:31:08" level=info msg="GET /.env - 200 - 175.143µs"
time="2025-08-05 05:31:08" level=info msg="GET /assets/index-Bsu5oAg6.js - 200 - 69.143508ms"
time="2025-08-05 05:33:08" level=error msg="CronChecker: Failed to get groups: ERROR: relation \"groups\" does not exist (SQLSTATE 42P01)"
time="2025-08-05 05:38:08" level=error msg="CronChecker: Failed to get groups: ERROR: relation \"groups\" does not exist (SQLSTATE 42P01)"
time="2025-08-05 05:43:08" level=error msg="CronChecker: Failed to get groups: ERROR: relation \"groups\" does not exist (SQLSTATE 42P01)"
time="2025-08-05 05:48:08" level=error msg="CronChecker: Failed to get groups: ERROR: relation \"groups\" does not exist (SQLSTATE 42P01)"
time="2025-08-05 05:53:08" level=error msg="CronChecker: Failed to get groups: ERROR: relation \"groups\" does not exist (SQLSTATE 42P01)"
time="2025-08-05 05:58:08" level=error msg="CronChecker: Failed to get groups: ERROR: relation \"groups\" does not exist (SQLSTATE 42P01)"
time="2025-08-05 06:03:08" level=error msg="CronChecker: Failed to get groups: ERROR: relation \"groups\" does not exist (SQLSTATE 42P01)"
time="2025-08-05 06:08:08" level=error msg="CronChecker: Failed to get groups: ERROR: relation \"groups\" does not exist (SQLSTATE 42P01)"
time="2025-08-05 06:13:08" level=error msg="CronChecker: Failed to get groups: ERROR: relation \"groups\" does not exist (SQLSTATE 42P01)"
time="2025-08-05 06:18:08" level=error msg="CronChecker: Failed to get groups: ERROR: relation \"groups\" does not exist (SQLSTATE 42P01)"
time="2025-08-05 06:23:08" level=error msg="CronChecker: Failed to get groups: ERROR: relation \"groups\" does not exist (SQLSTATE 42P01)"
time="2025-08-05 06:28:08" level=error msg="CronChecker: Failed to get groups: ERROR: relation \"groups\" does not exist (SQLSTATE 42P01)"
time="2025-08-05 06:33:08" level=error msg="CronChecker: Failed to get groups: ERROR: relation \"groups\" does not exist (SQLSTATE 42P01)"
time="2025-08-05 06:38:08" level=error msg="CronChecker: Failed to get groups: ERROR: relation \"groups\" does not exist (SQLSTATE 42P01)"
time="2025-08-05 06:43:08" level=error msg="CronChecker: Failed to get groups: ERROR: relation \"groups\" does not exist (SQLSTATE 42P01)"
time="2025-08-05 06:48:08" level=error msg="CronChecker: Failed to get groups: ERROR: relation \"groups\" does not exist (SQLSTATE 42P01)"
time="2025-08-05 06:53:08" level=error msg="CronChecker: Failed to get groups: ERROR: relation \"groups\" does not exist (SQLSTATE 42P01)"
time="2025-08-05 06:58:08" level=error msg="CronChecker: Failed to get groups: ERROR: relation \"groups\" does not exist (SQLSTATE 42P01)"
time="2025-08-05 07:03:08" level=error msg="CronChecker: Failed to get groups: ERROR: relation \"groups\" does not exist (SQLSTATE 42P01)"
time="2025-08-05 07:08:08" level=error msg="CronChecker: Failed to get groups: ERROR: relation \"groups\" does not exist (SQLSTATE 42P01)"
time="2025-08-05 07:13:08" level=error msg="Failed to cleanup expired request logs" error="ERROR: relation \"request_logs\" does not exist (SQLSTATE 42P01)"
time="2025-08-05 07:13:08" level=error msg="CronChecker: Failed to get groups: ERROR: relation \"groups\" does not exist (SQLSTATE 42P01)"
time="2025-08-05 07:18:08" level=error msg="CronChecker: Failed to get groups: ERROR: relation \"groups\" does not exist (SQLSTATE 42P01)"
time="2025-08-05 07:23:08" level=error msg="CronChecker: Failed to get groups: ERROR: relation \"groups\" does not exist (SQLSTATE 42P01)"
time="2025-08-05 07:28:08" level=error msg="CronChecker: Failed to get groups: ERROR: relation \"groups\" does not exist (SQLSTATE 42P01)"
time="2025-08-05 07:33:08" level=error msg="CronChecker: Failed to get groups: ERROR: relation \"groups\" does not exist (SQLSTATE 42P01)"
time="2025-08-05 07:38:08" level=error msg="CronChecker: Failed to get groups: ERROR: relation \"groups\" does not exist (SQLSTATE 42P01)"
time="2025-08-05 07:43:08" level=error msg="CronChecker: Failed to get groups: ERROR: relation \"groups\" does not exist (SQLSTATE 42P01)"
time="2025-08-05 07:48:08" level=error msg="CronChecker: Failed to get groups: ERROR: relation \"groups\" does not exist (SQLSTATE 42P01)"
time="2025-08-05 07:53:08" level=error msg="CronChecker: Failed to get groups: ERROR: relation \"groups\" does not exist (SQLSTATE 42P01)"
time="2025-08-05 07:58:08" level=error msg="CronChecker: Failed to get groups: ERROR: relation \"groups\" does not exist (SQLSTATE 42P01)"
time="2025-08-05 08:03:08" level=error msg="CronChecker: Failed to get groups: ERROR: relation \"groups\" does not exist (SQLSTATE 42P01)"
time="2025-08-05 08:08:08" level=error msg="CronChecker: Failed to get groups: ERROR: relation \"groups\" does not exist (SQLSTATE 42P01)"
time="2025-08-05 08:13:08" level=error msg="CronChecker: Failed to get groups: ERROR: relation \"groups\" does not exist (SQLSTATE 42P01)"
time="2025-08-05 08:18:08" level=error msg="CronChecker: Failed to get groups: ERROR: relation \"groups\" does not exist (SQLSTATE 42P01)"
time="2025-08-05 08:23:08" level=error msg="CronChecker: Failed to get groups: ERROR: relation \"groups\" does not exist (SQLSTATE 42P01)"
time="2025-08-05 08:28:08" level=error msg="CronChecker: Failed to get groups: ERROR: relation \"groups\" does not exist (SQLSTATE 42P01)"
time="2025-08-05 08:33:08" level=error msg="CronChecker: Failed to get groups: ERROR: relation \"groups\" does not exist (SQLSTATE 42P01)"
time="2025-08-05 08:38:08" level=error msg="CronChecker: Failed to get groups: ERROR: relation \"groups\" does not exist (SQLSTATE 42P01)"
time="2025-08-05 08:43:08" level=error msg="CronChecker: Failed to get groups: ERROR: relation \"groups\" does not exist (SQLSTATE 42P01)"
time="2025-08-05 08:48:08" level=error msg="CronChecker: Failed to get groups: ERROR: relation \"groups\" does not exist (SQLSTATE 42P01)"
time="2025-08-05 08:53:08" level=error msg="CronChecker: Failed to get groups: ERROR: relation \"groups\" does not exist (SQLSTATE 42P01)"
time="2025-08-05 08:58:08" level=error msg="CronChecker: Failed to get groups: ERROR: relation \"groups\" does not exist (SQLSTATE 42P01)"
time="2025-08-05 09:03:08" level=error msg="CronChecker: Failed to get groups: ERROR: relation \"groups\" does not exist (SQLSTATE 42P01)"
time="2025-08-05 09:08:08" level=error msg="CronChecker: Failed to get groups: ERROR: relation \"groups\" does not exist (SQLSTATE 42P01)"
time="2025-08-05 09:13:08" level=error msg="Failed to cleanup expired request logs" error="ERROR: relation \"request_logs\" does not exist (SQLSTATE 42P01)"
time="2025-08-05 09:13:08" level=error msg="CronChecker: Failed to get groups: ERROR: relation \"groups\" does not exist (SQLSTATE 42P01)"
time="2025-08-05 09:18:08" level=error msg="CronChecker: Failed to get groups: ERROR: relation \"groups\" does not exist (SQLSTATE 42P01)"
time="2025-08-05 09:23:08" level=error msg="CronChecker: Failed to get groups: ERROR: relation \"groups\" does not exist (SQLSTATE 42P01)"
time="2025-08-05 09:28:08" level=error msg="CronChecker: Failed to get groups: ERROR: relation \"groups\" does not exist (SQLSTATE 42P01)"
time="2025-08-05 09:33:08" level=error msg="CronChecker: Failed to get groups: ERROR: relation \"groups\" does not exist (SQLSTATE 42P01)"
time="2025-08-05 09:38:08" level=error msg="CronChecker: Failed to get groups: ERROR: relation \"groups\" does not exist (SQLSTATE 42P01)"
time="2025-08-05 09:43:08" level=error msg="CronChecker: Failed to get groups: ERROR: relation \"groups\" does not exist (SQLSTATE 42P01)"
time="2025-08-05 09:48:08" level=error msg="CronChecker: Failed to get groups: ERROR: relation \"groups\" does not exist (SQLSTATE 42P01)"
time="2025-08-05 09:53:08" level=error msg="CronChecker: Failed to get groups: ERROR: relation \"groups\" does not exist (SQLSTATE 42P01)"
time="2025-08-05 09:55:20" level=info msg="GET / - 200 - 1.342634ms"
time="2025-08-05 09:55:21" level=info msg="GET /api/tasks/status - 200 - 362.27µs"
time="2025-08-05 09:55:21" level=error msg="GET /api/groups/list - 500 - 1.375928ms"
time="2025-08-05 09:55:21" level=error msg="GET /api/dashboard/chart - 500 - 8.324645ms"
time="2025-08-05 09:55:21" level=error msg="GET /api/dashboard/stats - 500 - 10.007915ms"
time="2025-08-05 09:55:27" level=info msg="GET /assets/Login-DzpmRpZS.css - 200 - 281.344µs"
time="2025-08-05 09:55:27" level=info msg="GET /assets/Login-DUjC1gm4.js - 200 - 4.180252ms"
time="2025-08-05 09:55:40" level=info msg="POST /api/auth/login - 200 - 124.147µs"
time="2025-08-05 09:55:40" level=error msg="GET /api/dashboard/stats - 500 - 1.922607ms"
time="2025-08-05 09:55:40" level=error msg="GET /api/groups/list - 500 - 1.2247ms"
time="2025-08-05 09:55:40" level=info msg="GET /api/tasks/status - 200 - 244.205µs"
time="2025-08-05 09:55:40" level=error msg="GET /api/dashboard/chart - 500 - 334.646µs"
time="2025-08-05 09:55:44" level=info msg="GET /api/groups/config-options - 200 - 192.207µs"
time="2025-08-05 09:55:44" level=error msg="GET /api/groups - 500 - 676.887µs"
time="2025-08-05 09:58:08" level=error msg="CronChecker: Failed to get groups: ERROR: relation \"groups\" does not exist (SQLSTATE 42P01)"
time="2025-08-05 10:05:29" level=info msg="Shutting down server..."
time="2025-08-05 10:05:29" level=info msg="HTTP server has been shut down."
time="2025-08-05 10:05:29" level=info msg="cache syncer stopped." syncer=groups
time="2025-08-05 10:05:29" level=info msg="LogCleanupService stopped gracefully."
time="2025-08-05 10:05:29" level=info msg="CronChecker stopped gracefully."
time="2025-08-05 10:05:29" level=info msg="cache syncer stopped." syncer=system_settings
time="2025-08-05 10:05:29" level=info msg="RequestLogService stopped gracefully."
time="2025-08-05 10:05:29" level=info msg="All background services stopped."
time="2025-08-05 10:05:29" level=info msg="Server exited gracefully"
time="2025-08-05 10:05:29" level=info msg="Starting as Master Node."
time="2025-08-05 10:05:30" level=fatal msg="Failed to start application: database auto-migration failed: ERROR: default for column \"upstreams\" cannot be cast automatically to type jsonb (SQLSTATE 42804)"
time="2025-08-05 10:05:30" level=info msg="Starting as Master Node."
time="2025-08-05 10:05:30" level=fatal msg="Failed to start application: database auto-migration failed: ERROR: default for column \"upstreams\" cannot be cast automatically to type jsonb (SQLSTATE 42804)"
time="2025-08-05 10:05:31" level=info msg="Starting as Master Node."
time="2025-08-05 10:05:31" level=fatal msg="Failed to start application: database auto-migration failed: ERROR: default for column \"upstreams\" cannot be cast automatically to type jsonb (SQLSTATE 42804)"
time="2025-08-05 10:05:31" level=info msg="Starting as Master Node."
time="2025-08-05 10:05:32" level=fatal msg="Failed to start application: database auto-migration failed: ERROR: default for column \"upstreams\" cannot be cast automatically to type jsonb (SQLSTATE 42804)"
time="2025-08-05 10:05:33" level=info msg="Starting as Master Node."
time="2025-08-05 10:05:33" level=fatal msg="Failed to start application: database auto-migration failed: ERROR: default for column \"upstreams\" cannot be cast automatically to type jsonb (SQLSTATE 42804)"
time="2025-08-05 10:05:35" level=info msg="Starting as Master Node."
time="2025-08-05 10:05:35" level=fatal msg="Failed to start application: database auto-migration failed: ERROR: default for column \"upstreams\" cannot be cast automatically to type jsonb (SQLSTATE 42804)"
time="2025-08-05 10:05:38" level=info msg="Starting as Master Node."
time="2025-08-05 10:05:38" level=fatal msg="Failed to start application: database auto-migration failed: ERROR: default for column \"upstreams\" cannot be cast automatically to type jsonb (SQLSTATE 42804)"
time="2025-08-05 10:05:45" level=info msg="Starting as Master Node."
time="2025-08-05 10:05:45" level=fatal msg="Failed to start application: database auto-migration failed: ERROR: default for column \"upstreams\" cannot be cast automatically to type jsonb (SQLSTATE 42804)"
time="2025-08-05 10:05:58" level=info msg="Starting as Master Node."
time="2025-08-05 10:05:58" level=fatal msg="Failed to start application: database auto-migration failed: ERROR: default for column \"upstreams\" cannot be cast automatically to type jsonb (SQLSTATE 42804)"
time="2025-08-05 10:06:24" level=info msg="Starting as Master Node."
time="2025-08-05 10:06:24" level=fatal msg="Failed to start application: database auto-migration failed: ERROR: default for column \"upstreams\" cannot be cast automatically to type jsonb (SQLSTATE 42804)"
time="2025-08-05 10:07:16" level=info msg="Starting as Master Node."
time="2025-08-05 10:07:16" level=fatal msg="Failed to start application: database auto-migration failed: ERROR: default for column \"config\" cannot be cast automatically to type jsonb (SQLSTATE 42804)"
time="2025-08-05 10:07:31" level=info msg="Starting as Master Node."
time="2025-08-05 10:07:31" level=fatal msg="Failed to start application: database auto-migration failed: ERROR: constraint \"uni_api_keys_key_value\" of relation \"api_keys\" does not exist (SQLSTATE 42704)"
time="2025-08-05 10:07:31" level=info msg="Starting as Master Node."
time="2025-08-05 10:07:31" level=fatal msg="Failed to start application: database auto-migration failed: ERROR: constraint \"uni_api_keys_key_value\" of relation \"api_keys\" does not exist (SQLSTATE 42704)"
time="2025-08-05 10:07:32" level=info msg="Starting as Master Node."
time="2025-08-05 10:07:32" level=fatal msg="Failed to start application: database auto-migration failed: ERROR: constraint \"uni_api_keys_key_value\" of relation \"api_keys\" does not exist (SQLSTATE 42704)"
time="2025-08-05 10:07:33" level=info msg="Starting as Master Node."
time="2025-08-05 10:07:33" level=fatal msg="Failed to start application: database auto-migration failed: ERROR: constraint \"uni_api_keys_key_value\" of relation \"api_keys\" does not exist (SQLSTATE 42704)"
time="2025-08-05 10:07:34" level=info msg="Starting as Master Node."
time="2025-08-05 10:07:34" level=fatal msg="Failed to start application: database auto-migration failed: ERROR: constraint \"uni_api_keys_key_value\" of relation \"api_keys\" does not exist (SQLSTATE 42704)"
time="2025-08-05 10:07:36" level=info msg="Starting as Master Node."
time="2025-08-05 10:07:36" level=fatal msg="Failed to start application: database auto-migration failed: ERROR: constraint \"uni_api_keys_key_value\" of relation \"api_keys\" does not exist (SQLSTATE 42704)"
time="2025-08-05 10:07:39" level=info msg="Starting as Master Node."
time="2025-08-05 10:07:40" level=fatal msg="Failed to start application: database auto-migration failed: ERROR: constraint \"uni_api_keys_key_value\" of relation \"api_keys\" does not exist (SQLSTATE 42704)"
time="2025-08-05 10:07:46" level=info msg="Starting as Master Node."
time="2025-08-05 10:07:46" level=fatal msg="Failed to start application: database auto-migration failed: ERROR: constraint \"uni_api_keys_key_value\" of relation \"api_keys\" does not exist (SQLSTATE 42704)"
time="2025-08-05 10:07:59" level=info msg="Starting as Master Node."
time="2025-08-05 10:08:00" level=fatal msg="Failed to start application: database auto-migration failed: ERROR: constraint \"uni_api_keys_key_value\" of relation \"api_keys\" does not exist (SQLSTATE 42704)"
time="2025-08-05 10:08:25" level=info msg="Starting as Master Node."
time="2025-08-05 10:08:26" level=fatal msg="Failed to start application: database auto-migration failed: ERROR: constraint \"uni_api_keys_key_value\" of relation \"api_keys\" does not exist (SQLSTATE 42704)"
time="2025-08-05 10:08:48" level=info msg="Starting as Master Node."
time="2025-08-05 10:08:49" level=info msg="Database auto-migration completed."
time="2025-08-05 10:08:49" level=info msg="System settings initialized in DB."
time="2025-08-05 10:08:49" level=info
time="2025-08-05 10:08:49" level=info msg="========= System Settings ========="
time="2025-08-05 10:08:49" level=info msg="  --- Basic Settings ---"
time="2025-08-05 10:08:49" level=info msg="    App URL: https://load.ainima.de"
time="2025-08-05 10:08:49" level=info msg="    Request Log Retention: 7 days"
time="2025-08-05 10:08:49" level=info msg="    Request Log Write Interval: 1 minutes"
time="2025-08-05 10:08:49" level=info msg="  --- Request Behavior ---"
time="2025-08-05 10:08:49" level=info msg="    Request Timeout: 600 seconds"
time="2025-08-05 10:08:49" level=info msg="    Connect Timeout: 15 seconds"
time="2025-08-05 10:08:49" level=info msg="    Response Header Timeout: 600 seconds"
time="2025-08-05 10:08:49" level=info msg="    Idle Connection Timeout: 120 seconds"
time="2025-08-05 10:08:49" level=info msg="    Max Idle Connections: 100"
time="2025-08-05 10:08:49" level=info msg="    Max Idle Connections Per Host: 50"
time="2025-08-05 10:08:49" level=info msg="  --- Key & Group Behavior ---"
time="2025-08-05 10:08:49" level=info msg="    Max Retries: 3"
time="2025-08-05 10:08:49" level=info msg="    Blacklist Threshold: 3"
time="2025-08-05 10:08:49" level=info msg="    Key Validation Interval: 60 minutes"
time="2025-08-05 10:08:49" level=info msg="===================================="
time="2025-08-05 10:08:49" level=info
time="2025-08-05 10:08:49" level=info msg="cache reloaded successfully" syncer=system_settings
time="2025-08-05 10:08:50" level=info msg="Updating active key lists for all groups..."
time="2025-08-05 10:08:50" level=info
time="2025-08-05 10:08:50" level=info msg="======= Server Configuration ======="
time="2025-08-05 10:08:50" level=info msg="  --- Server ---"
time="2025-08-05 10:08:50" level=info msg="    Listen Address: 0.0.0.0:3001"
time="2025-08-05 10:08:50" level=info msg="    Graceful Shutdown Timeout: 10 seconds"
time="2025-08-05 10:08:50" level=info msg="    Read Timeout: 60 seconds"
time="2025-08-05 10:08:50" level=info msg="    Write Timeout: 600 seconds"
time="2025-08-05 10:08:50" level=info msg="    Idle Timeout: 120 seconds"
time="2025-08-05 10:08:50" level=info msg="  --- Performance ---"
time="2025-08-05 10:08:50" level=info msg="    Max Concurrent Requests: 100"
time="2025-08-05 10:08:50" level=info msg="  --- Security ---"
time="2025-08-05 10:08:50" level=info msg="    Authentication: enabled (key loaded)"
time="2025-08-05 10:08:50" level=info msg="    CORS: enabled (Origins: *)"
time="2025-08-05 10:08:50" level=info msg="  --- Logging ---"
time="2025-08-05 10:08:50" level=info msg="    Log Level: info"
time="2025-08-05 10:08:50" level=info msg="    Log Format: text"
time="2025-08-05 10:08:50" level=info msg="    File Logging: true"
time="2025-08-05 10:08:50" level=info msg="    Log File Path: ./data/logs/app.log"
time="2025-08-05 10:08:50" level=info msg="  --- Dependencies ---"
time="2025-08-05 10:08:50" level=info msg="    Database: configured"
time="2025-08-05 10:08:50" level=info msg="    Redis: configured"
time="2025-08-05 10:08:50" level=info msg="===================================="
time="2025-08-05 10:08:50" level=info
time="2025-08-05 10:08:50" level=info msg="cache reloaded successfully" syncer=groups
time="2025-08-05 10:08:50" level=info msg="GPT-Load proxy server started successfully on Version: v1.0.18"
time="2025-08-05 10:08:50" level=info msg="Server address: http://0.0.0.0:3001"
time="2025-08-05 10:08:50" level=info
time="2025-08-05 10:08:50" level=info msg="CronChecker: Group 'gemini' has no invalid keys to check."
time="2025-08-05 10:08:50" level=info msg="CronChecker: Group 'targon' has no invalid keys to check."
time="2025-08-05 10:10:22" level=info msg="GET /api/groups - 200 - 1.862866ms"
time="2025-08-05 10:10:22" level=warning msg="GET /proxy/targon/v1/models - 401 - 158.752µs"
time="2025-08-05 10:10:22" level=warning msg="GET /proxy/gemini/v1beta/models/gemini-2.0-flash-lite:generateContent - 401 - 51.858µs"
time="2025-08-05 10:18:50" level=error msg="CronChecker: Failed to get groups: ERROR: relation \"groups\" does not exist (SQLSTATE 42P01)"
time="2025-08-05 10:23:50" level=error msg="CronChecker: Failed to get groups: ERROR: relation \"groups\" does not exist (SQLSTATE 42P01)"
time="2025-08-05 10:28:50" level=error msg="CronChecker: Failed to get groups: ERROR: relation \"groups\" does not exist (SQLSTATE 42P01)"
time="2025-08-05 10:32:52" level=info msg="GET / - 200 - 588.78µs"
time="2025-08-05 10:32:53" level=info msg="GET /api/tasks/status - 200 - 413.277µs"
time="2025-08-05 10:32:53" level=error msg="GET /api/dashboard/chart - 500 - 1.453226ms"
time="2025-08-05 10:32:53" level=error msg="GET /api/groups/list - 500 - 339.156µs"
time="2025-08-05 10:32:54" level=error msg="GET /api/dashboard/stats - 500 - 1.652666ms"
time="2025-08-05 10:33:00" level=info msg="GET /assets/Login-DUjC1gm4.js - 200 - 1.940914ms"
time="2025-08-05 10:33:00" level=info msg="GET /assets/Login-DzpmRpZS.css - 200 - 299.951µs"
time="2025-08-05 10:33:15" level=info msg="POST /api/auth/login - 200 - 180.394µs"
time="2025-08-05 10:33:15" level=info msg="GET /api/tasks/status - 200 - 770.206µs"
time="2025-08-05 10:33:15" level=error msg="GET /api/dashboard/stats - 500 - 1.84428ms"
time="2025-08-05 10:33:15" level=error msg="GET /api/dashboard/chart - 500 - 496.946µs"
time="2025-08-05 10:33:15" level=error msg="GET /api/groups/list - 500 - 4.893606ms"
time="2025-08-05 10:33:22" level=info msg="GET /api/groups/config-options - 200 - 261.307µs"
time="2025-08-05 10:33:22" level=error msg="GET /api/groups - 500 - 611.485µs"
time="2025-08-05 10:33:50" level=error msg="CronChecker: Failed to get groups: ERROR: relation \"groups\" does not exist (SQLSTATE 42P01)"
time="2025-08-05 10:33:56" level=info msg="GET /api/settings - 200 - 334.797µs"
time="2025-08-05 10:33:59" level=info msg="GET /api/groups/config-options - 200 - 152.811µs"
time="2025-08-05 10:33:59" level=error msg="GET /api/groups - 500 - 640.83µs"
