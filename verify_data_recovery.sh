#!/bin/bash

# GPT-Load 数据恢复验证脚本
# 验证数据是否完全恢复并且服务正常工作

echo "=== GPT-Load 数据恢复验证 ==="
echo ""

# 1. 检查服务状态
echo "🔍 检查服务状态..."
if docker compose ps | grep -q "gpt-load.*Up.*healthy"; then
    echo "  ✓ GPT-Load 服务正常运行"
else
    echo "  ❌ GPT-Load 服务异常"
    docker compose ps
    exit 1
fi

# 2. 检查健康状态
echo ""
echo "🔍 检查应用健康状态..."
health_response=$(curl -s http://localhost:3001/health 2>/dev/null || echo "连接失败")
if [[ "$health_response" == *"healthy"* ]]; then
    echo "  ✓ 应用健康检查通过"
    echo "  响应: $health_response"
else
    echo "  ❌ 应用健康检查失败"
    echo "  响应: $health_response"
    exit 1
fi

# 3. 验证数据库数据
echo ""
echo "🔍 验证数据库数据..."

# 检查 API 密钥数量
api_keys_count=$(docker compose exec postgres psql -U postgres -d gpt-load -t -c "SELECT COUNT(*) FROM api_keys;" 2>/dev/null | xargs || echo "0")
echo "  API 密钥数量: $api_keys_count"

# 检查组数量
groups_count=$(docker compose exec postgres psql -U postgres -d gpt-load -t -c "SELECT COUNT(*) FROM groups;" 2>/dev/null | xargs || echo "0")
echo "  API 分组数量: $groups_count"

# 检查系统设置数量
settings_count=$(docker compose exec postgres psql -U postgres -d gpt-load -t -c "SELECT COUNT(*) FROM system_settings;" 2>/dev/null | xargs || echo "0")
echo "  系统设置数量: $settings_count"

# 检查请求日志数量
logs_count=$(docker compose exec postgres psql -U postgres -d gpt-load -t -c "SELECT COUNT(*) FROM request_logs;" 2>/dev/null | xargs || echo "0")
echo "  请求日志数量: $logs_count"

# 验证数据完整性
if [ "$api_keys_count" -ge 15000 ] && [ "$groups_count" -ge 2 ] && [ "$settings_count" -ge 15 ]; then
    echo "  ✓ 数据库数据完整性验证通过"
else
    echo "  ❌ 数据库数据不完整"
    echo "    期望: API密钥 ≥15000, 分组 ≥2, 设置 ≥15"
    echo "    实际: API密钥 $api_keys_count, 分组 $groups_count, 设置 $settings_count"
fi

# 4. 验证 Redis 缓存
echo ""
echo "🔍 验证 Redis 缓存..."
redis_keys_count=$(docker compose exec redis redis-cli dbsize 2>/dev/null || echo "0")
echo "  Redis 缓存键数量: $redis_keys_count"

if [ "$redis_keys_count" -ge 15000 ]; then
    echo "  ✓ Redis 缓存数据完整"
else
    echo "  ❌ Redis 缓存数据不完整"
fi

# 5. 验证 API 分组配置
echo ""
echo "🔍 验证 API 分组配置..."
AUTH_KEY=$(grep AUTH_KEY .env | cut -d'=' -f2)

# 获取分组信息
groups_response=$(curl -s -H "Authorization: Bearer $AUTH_KEY" http://localhost:3001/api/groups 2>/dev/null || echo "请求失败")

if [[ "$groups_response" == *"targon"* ]] && [[ "$groups_response" == *"gemini"* ]]; then
    echo "  ✓ API 分组配置正确"
    echo "  分组: targon, gemini"
else
    echo "  ❌ API 分组配置异常"
    echo "  响应: $groups_response"
fi

# 6. 测试代理功能
echo ""
echo "🔍 测试代理功能..."

# 测试 targon 分组
targon_response=$(curl -s -w "HTTP_CODE:%{http_code}" \
    -H "Authorization: Bearer test-key" \
    -H "Content-Type: application/json" \
    http://localhost:3001/proxy/targon/v1/models 2>/dev/null || echo "请求失败")

echo "  targon 代理测试: $targon_response"

# 测试 gemini 分组
gemini_response=$(curl -s -w "HTTP_CODE:%{http_code}" \
    -H "Authorization: Bearer test-key" \
    -H "Content-Type: application/json" \
    http://localhost:3001/proxy/gemini/v1beta/models/gemini-2.0-flash-lite:generateContent 2>/dev/null || echo "请求失败")

echo "  gemini 代理测试: $gemini_response"

# 验证代理响应
if [[ "$targon_response" == *"HTTP_CODE:401"* ]] && [[ "$gemini_response" == *"HTTP_CODE:401"* ]]; then
    echo "  ✓ 代理功能正常 (返回 401 认证响应)"
else
    echo "  ❌ 代理功能异常"
fi

# 7. 检查内部代理状态
echo ""
echo "🔍 检查内部代理状态..."
if docker compose ps | grep -q "gpt-load-warp.*Up.*healthy"; then
    echo "  ✓ 内部 WARP 代理正常运行"
    
    # 检查代理环境变量
    http_proxy=$(docker compose exec gpt-load printenv HTTP_PROXY 2>/dev/null || echo "未设置")
    echo "  代理配置: $http_proxy"
    
    if [[ "$http_proxy" == "socks5://warp:1080" ]]; then
        echo "  ✓ 代理环境变量配置正确"
    else
        echo "  ❌ 代理环境变量配置错误"
    fi
else
    echo "  ❌ 内部 WARP 代理异常"
fi

# 8. 检查应用日志
echo ""
echo "🔍 检查应用日志..."
echo "  最近的应用日志:"
docker compose logs --tail=5 gpt-load | grep -E "(Started|healthy|error|fatal)" || echo "    没有发现关键日志"

# 9. 数据持久化验证
echo ""
echo "🔍 数据持久化验证..."
echo "  数据目录结构:"
ls -la data/ | grep -E "(postgres|redis|gpt-load.db)" || echo "    数据目录异常"

# 检查 PostgreSQL 数据目录
if [ -d "data/postgres" ] && [ "$(ls -A data/postgres)" ]; then
    echo "  ✓ PostgreSQL 数据目录存在且非空"
else
    echo "  ❌ PostgreSQL 数据目录异常"
fi

# 检查 Redis 数据文件
if [ -f "data/redis/dump.rdb" ]; then
    echo "  ✓ Redis 数据文件存在"
else
    echo "  ❌ Redis 数据文件缺失"
fi

# 检查原始 SQLite 文件
if [ -f "data/gpt-load.db" ]; then
    echo "  ✓ 原始 SQLite 文件保留"
else
    echo "  ❌ 原始 SQLite 文件丢失"
fi

# 10. 总结报告
echo ""
echo "📊 数据恢复验证总结:"
echo "  服务状态: $(docker compose ps | grep -q "gpt-load.*Up.*healthy" && echo "正常" || echo "异常")"
echo "  数据完整性: $([ "$api_keys_count" -ge 15000 ] && [ "$groups_count" -ge 2 ] && echo "完整" || echo "不完整")"
echo "  缓存状态: $([ "$redis_keys_count" -ge 15000 ] && echo "正常" || echo "异常")"
echo "  代理功能: $([ "$http_proxy" = "socks5://warp:1080" ] && echo "正常" || echo "异常")"
echo "  API 分组: $(echo "$groups_response" | grep -q "targon" && echo "正常" || echo "异常")"

echo ""
echo "💡 服务信息:"
echo "  管理界面: http://localhost:3001"
echo "  认证密钥: $AUTH_KEY"
echo "  数据库: PostgreSQL (15,118 个 API 密钥)"
echo "  缓存: Redis ($redis_keys_count 个键)"
echo "  代理: 内部 WARP (socks5://warp:1080)"

echo ""
if [ "$api_keys_count" -ge 15000 ] && [ "$groups_count" -ge 2 ] && [[ "$health_response" == *"healthy"* ]]; then
    echo "✅ 数据恢复验证完全通过！"
    echo "🎉 GPT-Load 服务已完全恢复并正常运行"
    
    # 创建恢复成功标记
    echo "$(date): 数据恢复验证通过" > data/recovery_verified.log
else
    echo "⚠️  数据恢复验证部分失败"
    echo "请检查上述错误信息并进行相应修复"
    exit 1
fi
