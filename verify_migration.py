#!/usr/bin/env python3
"""
数据迁移验证脚本
验证SQLite到Redis的数据迁移是否成功
"""

import sqlite3
import redis
import json
import sys
import os
from typing import Dict, List, Any

class MigrationVerifier:
    def __init__(self, sqlite_path: str, redis_host: str = 'localhost', redis_port: int = 6379, redis_db: int = 0):
        """初始化验证器"""
        self.sqlite_path = sqlite_path
        self.redis_client = redis.Redis(host=redis_host, port=redis_port, db=redis_db, decode_responses=True)
        
        # 测试连接
        try:
            self.redis_client.ping()
            print(f"✓ Redis连接成功: {redis_host}:{redis_port}")
        except Exception as e:
            print(f"✗ Redis连接失败: {e}")
            sys.exit(1)
            
        if not os.path.exists(sqlite_path):
            print(f"✗ SQLite数据库文件不存在: {sqlite_path}")
            sys.exit(1)
        print(f"✓ SQLite数据库文件存在: {sqlite_path}")

    def verify_system_settings(self):
        """验证系统设置迁移"""
        print("\n=== 验证系统设置 ===")
        
        # 从SQLite获取数据
        conn = sqlite3.connect(self.sqlite_path)
        cursor = conn.cursor()
        cursor.execute("SELECT COUNT(*) FROM system_settings")
        sqlite_count = cursor.fetchone()[0]
        
        cursor.execute("SELECT setting_key, setting_value FROM system_settings")
        sqlite_settings = dict(cursor.fetchall())
        conn.close()
        
        # 从Redis获取数据
        redis_keys = self.redis_client.smembers("gpt-load:system_settings:keys")
        redis_count = len(redis_keys)
        
        print(f"SQLite记录数: {sqlite_count}")
        print(f"Redis记录数: {redis_count}")
        
        if sqlite_count != redis_count:
            print("❌ 记录数不匹配!")
            return False
        
        # 验证具体数据
        mismatched = 0
        for key in redis_keys:
            redis_key = f"gpt-load:system_settings:{key}"
            redis_value = self.redis_client.hget(redis_key, 'setting_value')
            sqlite_value = sqlite_settings.get(key)
            
            if redis_value != sqlite_value:
                print(f"  ❌ {key}: SQLite='{sqlite_value}' vs Redis='{redis_value}'")
                mismatched += 1
        
        if mismatched == 0:
            print("✓ 系统设置验证通过")
            return True
        else:
            print(f"❌ 系统设置验证失败: {mismatched} 个不匹配")
            return False

    def verify_groups(self):
        """验证组配置迁移"""
        print("\n=== 验证组配置 ===")
        
        # 从SQLite获取数据
        conn = sqlite3.connect(self.sqlite_path)
        cursor = conn.cursor()
        cursor.execute("SELECT COUNT(*) FROM groups")
        sqlite_count = cursor.fetchone()[0]
        
        cursor.execute("SELECT id, name FROM groups")
        sqlite_groups = dict(cursor.fetchall())
        conn.close()
        
        # 从Redis获取数据
        redis_ids = self.redis_client.smembers("gpt-load:groups:ids")
        redis_count = len(redis_ids)
        
        print(f"SQLite记录数: {sqlite_count}")
        print(f"Redis记录数: {redis_count}")
        
        if sqlite_count != redis_count:
            print("❌ 记录数不匹配!")
            return False
        
        # 验证具体数据
        mismatched = 0
        for group_id in redis_ids:
            redis_key = f"gpt-load:groups:{group_id}"
            redis_name = self.redis_client.hget(redis_key, 'name')
            sqlite_name = sqlite_groups.get(int(group_id))
            
            if redis_name != sqlite_name:
                print(f"  ❌ Group {group_id}: SQLite='{sqlite_name}' vs Redis='{redis_name}'")
                mismatched += 1
        
        if mismatched == 0:
            print("✓ 组配置验证通过")
            return True
        else:
            print(f"❌ 组配置验证失败: {mismatched} 个不匹配")
            return False

    def verify_api_keys(self):
        """验证API密钥迁移"""
        print("\n=== 验证API密钥 ===")
        
        # 从SQLite获取数据
        conn = sqlite3.connect(self.sqlite_path)
        cursor = conn.cursor()
        cursor.execute("SELECT COUNT(*) FROM api_keys")
        sqlite_count = cursor.fetchone()[0]
        conn.close()
        
        # 从Redis获取数据 - 通过模式匹配计算
        redis_keys = self.redis_client.keys("gpt-load:api_keys:[0-9]*")
        redis_count = len(redis_keys)
        
        print(f"SQLite记录数: {sqlite_count}")
        print(f"Redis记录数: {redis_count}")
        
        if sqlite_count != redis_count:
            print("❌ 记录数不匹配!")
            return False
        
        print("✓ API密钥数量验证通过")
        
        # 验证索引
        group_keys = self.redis_client.keys("gpt-load:api_keys:group:*")
        status_keys = self.redis_client.keys("gpt-load:api_keys:status:*")
        
        print(f"  ✓ 组索引: {len(group_keys)} 个")
        print(f"  ✓ 状态索引: {len(status_keys)} 个")
        
        return True

    def verify_request_logs(self):
        """验证请求日志迁移"""
        print("\n=== 验证请求日志 ===")
        
        # 从SQLite获取数据
        conn = sqlite3.connect(self.sqlite_path)
        cursor = conn.cursor()
        cursor.execute("SELECT COUNT(*) FROM request_logs")
        sqlite_count = cursor.fetchone()[0]
        conn.close()
        
        # 从Redis获取数据
        redis_keys = self.redis_client.keys("gpt-load:request_logs:*")
        # 排除索引键
        log_keys = [k for k in redis_keys if not k.startswith("gpt-load:request_logs:date:") 
                   and not k.startswith("gpt-load:request_logs:group:")]
        redis_count = len(log_keys)
        
        print(f"SQLite记录数: {sqlite_count}")
        print(f"Redis记录数: {redis_count}")
        
        if sqlite_count != redis_count:
            print("❌ 记录数不匹配!")
            return False
        
        # 验证索引
        date_keys = self.redis_client.keys("gpt-load:request_logs:date:*")
        group_keys = self.redis_client.keys("gpt-load:request_logs:group:*")
        
        print(f"  ✓ 日期索引: {len(date_keys)} 个")
        print(f"  ✓ 组索引: {len(group_keys)} 个")
        print("✓ 请求日志验证通过")
        
        return True

    def verify_group_hourly_stats(self):
        """验证小时统计数据迁移"""
        print("\n=== 验证小时统计数据 ===")
        
        # 从SQLite获取数据
        conn = sqlite3.connect(self.sqlite_path)
        cursor = conn.cursor()
        cursor.execute("SELECT COUNT(*) FROM group_hourly_stats")
        sqlite_count = cursor.fetchone()[0]
        conn.close()
        
        # 从Redis获取数据
        redis_keys = self.redis_client.keys("gpt-load:group_hourly_stats:[0-9]*")
        redis_count = len(redis_keys)
        
        print(f"SQLite记录数: {sqlite_count}")
        print(f"Redis记录数: {redis_count}")
        
        if sqlite_count != redis_count:
            print("❌ 记录数不匹配!")
            return False
        
        # 验证时间序列索引
        ts_keys = self.redis_client.keys("gpt-load:group_hourly_stats:group:*")
        print(f"  ✓ 时间序列索引: {len(ts_keys)} 个")
        print("✓ 小时统计数据验证通过")
        
        return True

    def verify_migration_metadata(self):
        """验证迁移元数据"""
        print("\n=== 验证迁移元数据 ===")
        
        if not self.redis_client.exists("gpt-load:migration:metadata"):
            print("❌ 迁移元数据不存在")
            return False
        
        metadata = self.redis_client.hgetall("gpt-load:migration:metadata")
        print(f"  迁移时间: {metadata.get('migration_time', 'N/A')}")
        print(f"  源数据库: {metadata.get('source', 'N/A')}")
        print(f"  目标数据库: {metadata.get('target', 'N/A')}")
        print(f"  版本: {metadata.get('version', 'N/A')}")
        
        tables = metadata.get('tables_migrated', '').split(',') if metadata.get('tables_migrated') else []
        print(f"  迁移表数: {len(tables)}")
        
        print("✓ 迁移元数据验证通过")
        return True

    def get_redis_summary(self):
        """获取Redis数据摘要"""
        print("\n=== Redis数据摘要 ===")
        
        total_keys = len(self.redis_client.keys("gpt-load:*"))
        print(f"总键数: {total_keys}")
        
        # 按类型统计
        categories = {
            'system_settings': len(self.redis_client.keys("gpt-load:system_settings:*")),
            'groups': len(self.redis_client.keys("gpt-load:groups:*")),
            'api_keys': len(self.redis_client.keys("gpt-load:api_keys:*")),
            'request_logs': len(self.redis_client.keys("gpt-load:request_logs:*")),
            'group_hourly_stats': len(self.redis_client.keys("gpt-load:group_hourly_stats:*")),
            'migration': len(self.redis_client.keys("gpt-load:migration:*"))
        }
        
        for category, count in categories.items():
            print(f"  {category}: {count} 个键")

    def run_verification(self):
        """运行完整验证"""
        print("开始验证SQLite到Redis数据迁移...")
        print(f"源数据库: {self.sqlite_path}")
        print(f"目标Redis: {self.redis_client.connection_pool.connection_kwargs}")
        
        results = []
        results.append(self.verify_system_settings())
        results.append(self.verify_groups())
        results.append(self.verify_api_keys())
        results.append(self.verify_request_logs())
        results.append(self.verify_group_hourly_stats())
        results.append(self.verify_migration_metadata())
        
        self.get_redis_summary()
        
        print(f"\n=== 验证结果 ===")
        passed = sum(results)
        total = len(results)
        
        if passed == total:
            print(f"🎉 验证通过! ({passed}/{total})")
            print("数据迁移成功完成!")
            return True
        else:
            print(f"❌ 验证失败! ({passed}/{total})")
            print("请检查迁移过程中的错误")
            return False

if __name__ == "__main__":
    # 配置参数
    sqlite_path = "./data/gpt-load.db"

    # 检测运行环境，如果在Docker容器中则使用容器名
    import socket
    try:
        # 尝试解析redis主机名，如果成功说明在Docker网络中
        socket.gethostbyname('redis')
        redis_host = "redis"
        print("检测到Docker环境，使用Redis容器名连接")
    except socket.gaierror:
        # 如果失败，使用localhost
        redis_host = "localhost"
        print("使用localhost连接Redis")

    redis_port = 6379
    redis_db = 0

    # 执行验证
    verifier = MigrationVerifier(sqlite_path, redis_host, redis_port, redis_db)
    success = verifier.run_verification()

    sys.exit(0 if success else 1)
