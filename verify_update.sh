#!/bin/bash

# GPT-Load 更新验证脚本
# 验证更新到 v1.0.19 后的功能完整性

echo "=== GPT-Load v1.0.19 更新验证 ==="
echo ""

AUTH_KEY=$(grep AUTH_KEY .env | cut -d'=' -f2)
BASE_URL="http://localhost:3001"

# 1. 检查版本
echo "🔍 检查版本信息..."
version_info=$(docker compose logs gpt-load | grep "started successfully on Version" | tail -1)
if [[ "$version_info" == *"v1.0.19"* ]]; then
    echo "  ✓ 版本更新成功: v1.0.19"
else
    echo "  ❌ 版本更新失败"
    echo "  当前版本信息: $version_info"
    exit 1
fi

# 2. 健康检查
echo ""
echo "🔍 健康检查..."
health_response=$(curl -s "$BASE_URL/health" 2>/dev/null || echo "连接失败")
if [[ "$health_response" == *"healthy"* ]]; then
    echo "  ✓ 服务健康"
    uptime=$(echo "$health_response" | grep -o '"uptime":"[^"]*"' | cut -d'"' -f4)
    echo "  运行时间: $uptime"
else
    echo "  ❌ 服务异常: $health_response"
    exit 1
fi

# 3. 数据完整性检查
echo ""
echo "🔍 数据完整性检查..."
stats_response=$(curl -s -H "Authorization: Bearer $AUTH_KEY" "$BASE_URL/api/dashboard/stats" 2>/dev/null || echo "请求失败")

if [[ "$stats_response" == *"key_count"* ]]; then
    key_count=$(echo "$stats_response" | grep -o '"key_count":{"value":[0-9]*' | grep -o '[0-9]*$')
    echo "  ✓ 仪表板数据正常"
    echo "  密钥总数: $key_count"
    
    if [ "$key_count" -ge 15000 ]; then
        echo "  ✓ 数据完整性验证通过"
    else
        echo "  ❌ 数据可能丢失，密钥数量异常"
    fi
else
    echo "  ❌ 仪表板数据异常: $stats_response"
fi

# 4. API 分组检查
echo ""
echo "🔍 API 分组检查..."
groups_response=$(curl -s -H "Authorization: Bearer $AUTH_KEY" "$BASE_URL/api/groups" 2>/dev/null || echo "请求失败")

if [[ "$groups_response" == *"targon"* ]] && [[ "$groups_response" == *"gemini"* ]]; then
    echo "  ✓ API 分组正常"
    echo "  分组: targon, gemini"
else
    echo "  ❌ API 分组异常"
fi

# 5. 密钥列表检查
echo ""
echo "🔍 密钥列表检查..."
keys_response=$(curl -s -H "Authorization: Bearer $AUTH_KEY" "$BASE_URL/api/keys?group_id=1&page=1&page_size=1" 2>/dev/null || echo "请求失败")

if [[ "$keys_response" == *"total_items"* ]]; then
    total_keys=$(echo "$keys_response" | grep -o '"total_items":[0-9]*' | grep -o '[0-9]*$')
    echo "  ✓ 密钥列表正常"
    echo "  targon 分组密钥数: $total_keys"
else
    echo "  ❌ 密钥列表异常"
fi

# 6. 代理功能检查
echo ""
echo "🔍 代理功能检查..."
proxy_response=$(curl -s -w "HTTP_CODE:%{http_code}" \
    -H "Authorization: Bearer test-key" \
    -H "Content-Type: application/json" \
    "$BASE_URL/proxy/targon/v1/models" 2>/dev/null || echo "请求失败")

if [[ "$proxy_response" == *"HTTP_CODE:401"* ]]; then
    echo "  ✓ 代理功能正常 (返回 401 认证响应)"
else
    echo "  ❌ 代理功能异常: $proxy_response"
fi

# 7. 内部代理检查
echo ""
echo "🔍 内部代理检查..."
if docker compose ps | grep -q "gpt-load-warp.*Up.*healthy"; then
    echo "  ✓ 内部 WARP 代理正常"
    
    # 检查代理环境变量
    http_proxy=$(docker compose exec gpt-load printenv HTTP_PROXY 2>/dev/null || echo "未设置")
    if [[ "$http_proxy" == "socks5://warp:1080" ]]; then
        echo "  ✓ 代理环境变量正确"
    else
        echo "  ❌ 代理环境变量异常: $http_proxy"
    fi
else
    echo "  ❌ 内部 WARP 代理异常"
fi

# 8. 数据库连接检查
echo ""
echo "🔍 数据库连接检查..."
db_count=$(docker compose exec postgres psql -U postgres -d gpt-load -t -c "SELECT COUNT(*) FROM api_keys;" 2>/dev/null | xargs || echo "0")
redis_count=$(docker compose exec redis redis-cli dbsize 2>/dev/null || echo "0")

echo "  PostgreSQL 密钥数: $db_count"
echo "  Redis 缓存键数: $redis_count"

if [ "$db_count" -ge 15000 ] && [ "$redis_count" -ge 15000 ]; then
    echo "  ✓ 数据库连接正常"
else
    echo "  ❌ 数据库连接异常"
fi

# 9. 新功能检查 (v1.0.19)
echo ""
echo "🔍 v1.0.19 新功能检查..."

# 检查请求日志是否包含模型字段
log_sample=$(docker compose exec postgres psql -U postgres -d gpt-load -t -c "SELECT column_name FROM information_schema.columns WHERE table_name = 'request_logs' AND column_name = 'model';" 2>/dev/null | xargs || echo "")

if [ "$log_sample" = "model" ]; then
    echo "  ✓ 请求日志模型字段已添加"
else
    echo "  ⚠️  请求日志模型字段未找到 (可能需要数据库迁移)"
fi

# 10. 性能检查
echo ""
echo "🔍 性能检查..."
start_time=$(date +%s%N)
perf_response=$(curl -s "$BASE_URL/health" 2>/dev/null)
end_time=$(date +%s%N)
response_time=$(( (end_time - start_time) / 1000000 ))

echo "  健康检查响应时间: ${response_time}ms"
if [ "$response_time" -lt 1000 ]; then
    echo "  ✓ 响应性能良好"
else
    echo "  ⚠️  响应时间较慢"
fi

# 11. 检查应用日志
echo ""
echo "🔍 检查应用日志..."
error_count=$(docker compose logs --tail=20 gpt-load | grep -c -i "error\|fatal" || echo "0")
echo "  最近 20 条日志中的错误数: $error_count"

if [ "$error_count" -eq 0 ]; then
    echo "  ✓ 没有发现错误日志"
else
    echo "  ⚠️  发现 $error_count 个错误，请检查日志"
fi

# 12. 总结
echo ""
echo "📊 更新验证总结:"
echo "  版本: $([ "$version_info" == *"v1.0.19"* ] && echo "v1.0.19 ✓" || echo "异常 ❌")"
echo "  服务健康: $([ "$health_response" == *"healthy"* ] && echo "正常 ✓" || echo "异常 ❌")"
echo "  数据完整性: $([ "$key_count" -ge 15000 ] && echo "完整 ✓" || echo "异常 ❌")"
echo "  API 功能: $([ "$groups_response" == *"targon"* ] && echo "正常 ✓" || echo "异常 ❌")"
echo "  代理功能: $([ "$proxy_response" == *"HTTP_CODE:401"* ] && echo "正常 ✓" || echo "异常 ❌")"
echo "  数据库: $([ "$db_count" -ge 15000 ] && echo "正常 ✓" || echo "异常 ❌")"

echo ""
echo "💡 更新信息:"
echo "  更新版本: v1.0.18 → v1.0.19"
echo "  更新时间: $(date)"
echo "  管理界面: $BASE_URL"
echo "  认证密钥: $AUTH_KEY"
echo "  数据备份: backup/$(ls backup/ | tail -1)/data"

echo ""
if [[ "$version_info" == *"v1.0.19"* ]] && [[ "$health_response" == *"healthy"* ]] && [ "$key_count" -ge 15000 ]; then
    echo "✅ GPT-Load 更新到 v1.0.19 成功！"
    echo "🎉 所有功能正常，数据完整"
    
    # 记录更新成功
    echo "$(date): 成功更新到 v1.0.19" >> data/update_history.log
else
    echo "⚠️  更新验证部分失败"
    echo "请检查上述错误信息"
    exit 1
fi
