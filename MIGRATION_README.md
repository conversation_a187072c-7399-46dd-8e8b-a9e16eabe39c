# GPT-Load SQLite 到 Redis 数据迁移指南

本指南将帮助您将 GPT-Load 项目从 SQLite 数据库迁移到 Redis。

## 📋 迁移概述

### 已完成的配置更改

1. **docker-compose.yml**
   - ✅ 启用了 Redis 服务
   - ✅ 配置了 Redis 健康检查
   - ✅ 添加了数据持久化卷 `./data/redis:/data`
   - ✅ 配置了 gpt-load 服务依赖 Redis

2. **.env 文件**
   - ✅ 启用了 Redis 配置: `REDIS_DSN=redis://redis:6379/0`

3. **迁移工具**
   - ✅ `migrate_sqlite_to_redis.py` - 数据迁移脚本
   - ✅ `run_migration.sh` - 自动化迁移执行脚本
   - ✅ `verify_migration.py` - 数据验证脚本

## 🚀 快速迁移

### 方法一：使用自动化脚本（推荐）

```bash
# 执行自动化迁移
./run_migration.sh
```

这个脚本会：
1. 检查必要文件和环境
2. 停止当前的 gpt-load 服务
3. 启动 Redis 服务
4. 执行数据迁移
5. 提供启动完整服务的选项

### 方法二：手动执行

```bash
# 1. 停止当前服务
docker-compose stop gpt-load

# 2. 启动 Redis
docker-compose up -d redis

# 3. 等待 Redis 启动
sleep 5

# 4. 执行迁移
docker run --rm \
    --network "$(basename $(pwd))_default" \
    -v "$(pwd):/app" \
    -w /app \
    python:3.9-slim \
    bash -c "pip install redis && python migrate_sqlite_to_redis.py"

# 5. 启动完整服务
docker-compose up -d
```

## 📊 数据迁移详情

### 迁移的数据表

| SQLite 表 | Redis 键模式 | 描述 |
|-----------|-------------|------|
| `system_settings` | `gpt-load:system_settings:*` | 系统配置 |
| `groups` | `gpt-load:groups:*` | API 组配置 |
| `api_keys` | `gpt-load:api_keys:*` | API 密钥 |
| `request_logs` | `gpt-load:request_logs:*` | 请求日志 |
| `group_hourly_stats` | `gpt-load:group_hourly_stats:*` | 小时统计 |

### Redis 数据结构

- **Hash**: 存储记录详细信息
- **Set**: 创建索引（如按组、状态分类）
- **Sorted Set**: 时间序列数据（如日志按时间排序）

### 当前数据量

```
system_settings: 15 条记录
groups: 2 条记录  
api_keys: 13,435 条记录
request_logs: 129 条记录
group_hourly_stats: 8 条记录
```

## ✅ 验证迁移

### 自动验证

```bash
# 使用验证脚本
docker run --rm \
    --network "$(basename $(pwd))_default" \
    -v "$(pwd):/app" \
    -w /app \
    python:3.9-slim \
    bash -c "pip install redis && python verify_migration.py"
```

### 手动验证

```bash
# 检查 Redis 连接
docker-compose exec redis redis-cli ping

# 查看迁移的键
docker-compose exec redis redis-cli keys "gpt-load:*" | head -10

# 检查系统设置
docker-compose exec redis redis-cli smembers "gpt-load:system_settings:keys"

# 检查组数量
docker-compose exec redis redis-cli scard "gpt-load:groups:ids"
```

### 应用日志验证

启动服务后检查日志：

```bash
# 查看启动日志
docker-compose logs gpt-load

# 应该看到类似信息：
# "Redis: configured" (而不是 "Redis: not configured")
```

## 🔧 故障排除

### 常见问题

1. **Redis 连接失败**
   ```bash
   # 检查 Redis 服务状态
   docker-compose ps redis
   
   # 检查 Redis 日志
   docker-compose logs redis
   ```

2. **数据迁移失败**
   ```bash
   # 检查 SQLite 文件权限
   ls -la data/gpt-load.db
   
   # 手动测试 Redis 连接
   docker-compose exec redis redis-cli ping
   ```

3. **服务启动失败**
   ```bash
   # 检查环境变量
   grep REDIS_DSN .env
   
   # 检查服务依赖
   docker-compose config
   ```

### 回滚到 SQLite

如果需要回滚：

1. 停止服务：`docker-compose stop`
2. 注释 .env 中的 `REDIS_DSN`
3. 修改 docker-compose.yml，注释 Redis 相关配置
4. 重启：`docker-compose up -d gpt-load`

## 📈 性能优势

迁移到 Redis 后的预期改进：

1. **并发性能**: 解决 SQLite 的数据库锁定问题
2. **响应速度**: 内存数据库，更快的读写速度
3. **扩展性**: 支持集群和主从复制
4. **缓存能力**: 原生支持过期时间和缓存策略

## 🔒 数据安全

- ✅ 原始 SQLite 数据保持不变
- ✅ Redis 数据持久化到 `./data/redis`
- ✅ 支持数据验证和回滚
- ✅ 迁移过程包含完整性检查

## 📞 支持

如果遇到问题：

1. 检查日志：`docker-compose logs`
2. 验证配置：`docker-compose config`
3. 运行验证脚本：`python verify_migration.py`
4. 查看 Redis 状态：`docker-compose exec redis redis-cli info`

---

**注意**: 迁移完成后，建议保留 SQLite 文件作为备份，直到确认 Redis 运行稳定。
