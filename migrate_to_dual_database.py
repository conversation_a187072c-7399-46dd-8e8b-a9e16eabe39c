#!/usr/bin/env python3
"""
Redis 到 PostgreSQL + Redis 双数据库迁移脚本
将现有 Redis 数据迁移到 PostgreSQL 作为主存储，Redis 作为缓存层
"""

import redis
import psycopg2
import psycopg2.extras
import json
import sys
import os
from datetime import datetime
from typing import Dict, List, Any
import uuid

class DualDatabaseMigrator:
    def __init__(self, redis_host: str = 'redis', redis_port: int = 6379, 
                 pg_host: str = 'postgres', pg_port: int = 5432):
        """初始化双数据库迁移器"""
        
        # Redis 连接
        self.redis_client = redis.Redis(host=redis_host, port=redis_port, db=0, decode_responses=True)
        
        # PostgreSQL 连接
        self.pg_conn = None
        self.pg_cursor = None
        
        # 连接参数
        self.pg_params = {
            'host': pg_host,
            'port': pg_port,
            'database': 'gpt_load',
            'user': 'gptload',
            'password': 'gptload_secure_2024'
        }
        
        self.connect_databases()

    def connect_databases(self):
        """连接数据库"""
        try:
            # 测试 Redis 连接
            self.redis_client.ping()
            print(f"✓ Redis 连接成功")
        except Exception as e:
            print(f"✗ Redis 连接失败: {e}")
            sys.exit(1)
        
        try:
            # 连接 PostgreSQL
            self.pg_conn = psycopg2.connect(**self.pg_params)
            self.pg_cursor = self.pg_conn.cursor(cursor_factory=psycopg2.extras.RealDictCursor)
            print(f"✓ PostgreSQL 连接成功")
        except Exception as e:
            print(f"✗ PostgreSQL 连接失败: {e}")
            sys.exit(1)

    def migrate_system_settings(self):
        """迁移系统设置"""
        print("\n=== 迁移系统设置到 PostgreSQL ===")
        
        # 从 Redis 获取系统设置
        setting_keys = self.redis_client.smembers("gpt-load:system_settings:keys")
        
        migrated_count = 0
        for setting_key in setting_keys:
            redis_key = f"gpt-load:system_settings:{setting_key}"
            setting_data = self.redis_client.hgetall(redis_key)
            
            if setting_data:
                # 插入到 PostgreSQL
                self.pg_cursor.execute("""
                    INSERT INTO system_settings (setting_key, setting_value, description, created_at, updated_at)
                    VALUES (%s, %s, %s, %s, %s)
                    ON CONFLICT (setting_key) DO UPDATE SET
                        setting_value = EXCLUDED.setting_value,
                        description = EXCLUDED.description,
                        updated_at = EXCLUDED.updated_at
                """, (
                    setting_data.get('setting_key', setting_key),
                    setting_data.get('setting_value', ''),
                    setting_data.get('description', ''),
                    setting_data.get('created_at') or datetime.now(),
                    setting_data.get('updated_at') or datetime.now()
                ))
                
                migrated_count += 1
                print(f"  ✓ {setting_key}: {setting_data.get('setting_value', '')}")
        
        self.pg_conn.commit()
        print(f"✓ 系统设置迁移完成: {migrated_count} 条记录")

    def migrate_groups(self):
        """迁移组配置"""
        print("\n=== 迁移组配置到 PostgreSQL ===")
        
        # 从 Redis 获取组数据
        group_ids = self.redis_client.smembers("gpt-load:groups:ids")
        
        migrated_count = 0
        for group_id in group_ids:
            redis_key = f"gpt-load:groups:{group_id}"
            group_data = self.redis_client.hgetall(redis_key)
            
            if group_data:
                # 插入到 PostgreSQL
                self.pg_cursor.execute("""
                    INSERT INTO api_groups 
                    (id, name, display_name, organization_id, proxy_keys, description, upstreams, 
                     validation_endpoint, channel_type, sort_order, test_model, param_overrides, 
                     config, last_validated_at, created_at, updated_at)
                    VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                    ON CONFLICT (id) DO UPDATE SET
                        name = EXCLUDED.name,
                        display_name = EXCLUDED.display_name,
                        updated_at = EXCLUDED.updated_at
                """, (
                    int(group_id),
                    group_data.get('name', ''),
                    group_data.get('display_name', ''),
                    1,  # 默认组织 ID
                    group_data.get('proxy_keys', ''),
                    group_data.get('description', ''),
                    group_data.get('upstreams', '[]'),
                    group_data.get('validation_endpoint', ''),
                    group_data.get('channel_type', 'openai'),
                    int(group_data.get('sort', 0)),
                    group_data.get('test_model', 'gpt-3.5-turbo'),
                    group_data.get('param_overrides', '{}'),
                    group_data.get('config', '{}'),
                    group_data.get('last_validated_at'),
                    group_data.get('created_at') or datetime.now(),
                    group_data.get('updated_at') or datetime.now()
                ))
                
                migrated_count += 1
                print(f"  ✓ {group_data.get('name', '')} (ID: {group_id})")
        
        self.pg_conn.commit()
        print(f"✓ 组配置迁移完成: {migrated_count} 条记录")

    def migrate_api_keys(self):
        """迁移 API 密钥"""
        print("\n=== 迁移 API 密钥到 PostgreSQL ===")
        
        # 获取所有 API 密钥
        api_key_keys = self.redis_client.keys("gpt-load:api_keys:[0-9]*")
        
        batch_size = 1000
        total_migrated = 0
        
        for i in range(0, len(api_key_keys), batch_size):
            batch_keys = api_key_keys[i:i + batch_size]
            
            for redis_key in batch_keys:
                key_data = self.redis_client.hgetall(redis_key)
                
                if key_data:
                    # 插入到 PostgreSQL
                    self.pg_cursor.execute("""
                        INSERT INTO api_keys 
                        (id, key_value, organization_id, group_id, status, request_count, 
                         failure_count, success_count, last_used_at, created_at, updated_at)
                        VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                        ON CONFLICT (id) DO UPDATE SET
                            status = EXCLUDED.status,
                            request_count = EXCLUDED.request_count,
                            failure_count = EXCLUDED.failure_count,
                            success_count = EXCLUDED.success_count,
                            last_used_at = EXCLUDED.last_used_at,
                            updated_at = EXCLUDED.updated_at
                    """, (
                        int(key_data.get('id', 0)),
                        key_data.get('key_value', ''),
                        1,  # 默认组织 ID
                        int(key_data.get('group_id', 1)),
                        key_data.get('status', 'active'),
                        int(key_data.get('request_count', 0)),
                        int(key_data.get('failure_count', 0)),
                        int(key_data.get('request_count', 0)) - int(key_data.get('failure_count', 0)),
                        key_data.get('last_used_at'),
                        key_data.get('created_at') or datetime.now(),
                        key_data.get('updated_at') or datetime.now()
                    ))
                    
                    total_migrated += 1
            
            # 批量提交
            self.pg_conn.commit()
            print(f"  ✓ 已迁移 {total_migrated} 个 API 密钥...")
        
        print(f"✓ API 密钥迁移完成: {total_migrated} 条记录")

    def migrate_request_logs(self):
        """迁移请求日志"""
        print("\n=== 迁移请求日志到 PostgreSQL ===")
        
        # 获取所有请求日志
        log_keys = [k for k in self.redis_client.keys("gpt-load:request_logs:*") 
                   if not k.startswith("gpt-load:request_logs:date:") 
                   and not k.startswith("gpt-load:request_logs:group:")]
        
        migrated_count = 0
        for redis_key in log_keys:
            log_data = self.redis_client.hgetall(redis_key)
            
            if log_data:
                # 生成 UUID 如果没有 ID
                log_id = log_data.get('id', str(uuid.uuid4()))
                
                # 插入到 PostgreSQL
                self.pg_cursor.execute("""
                    INSERT INTO request_logs 
                    (id, timestamp, group_id, organization_id, group_name, key_value, 
                     is_success, source_ip, status_code, request_path, duration, 
                     error_message, user_agent, retries, upstream_addr, is_stream)
                    VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                    ON CONFLICT (id) DO NOTHING
                """, (
                    log_id,
                    log_data.get('timestamp') or datetime.now(),
                    int(log_data.get('group_id', 1)),
                    1,  # 默认组织 ID
                    log_data.get('group_name', ''),
                    log_data.get('key_value', ''),
                    bool(int(log_data.get('is_success', 0))),
                    log_data.get('source_ip'),
                    int(log_data.get('status_code', 0)),
                    log_data.get('request_path', ''),
                    int(log_data.get('duration', 0)),
                    log_data.get('error_message', ''),
                    log_data.get('user_agent', ''),
                    int(log_data.get('retries', 0)),
                    log_data.get('upstream_addr', ''),
                    bool(int(log_data.get('is_stream', 0)))
                ))
                
                migrated_count += 1
        
        self.pg_conn.commit()
        print(f"✓ 请求日志迁移完成: {migrated_count} 条记录")

    def migrate_hourly_stats(self):
        """迁移小时统计数据"""
        print("\n=== 迁移小时统计数据到 PostgreSQL ===")
        
        # 获取所有小时统计数据
        stats_keys = self.redis_client.keys("gpt-load:group_hourly_stats:[0-9]*")
        
        migrated_count = 0
        for redis_key in stats_keys:
            stats_data = self.redis_client.hgetall(redis_key)
            
            if stats_data:
                # 插入到 PostgreSQL
                self.pg_cursor.execute("""
                    INSERT INTO hourly_statistics 
                    (time_bucket, organization_id, group_id, success_count, failure_count, 
                     total_requests, created_at, updated_at)
                    VALUES (%s, %s, %s, %s, %s, %s, %s, %s)
                    ON CONFLICT (time_bucket, organization_id, group_id) DO UPDATE SET
                        success_count = EXCLUDED.success_count,
                        failure_count = EXCLUDED.failure_count,
                        total_requests = EXCLUDED.total_requests,
                        updated_at = EXCLUDED.updated_at
                """, (
                    stats_data.get('time') or datetime.now(),
                    1,  # 默认组织 ID
                    int(stats_data.get('group_id', 1)),
                    int(stats_data.get('success_count', 0)),
                    int(stats_data.get('failure_count', 0)),
                    int(stats_data.get('success_count', 0)) + int(stats_data.get('failure_count', 0)),
                    stats_data.get('created_at') or datetime.now(),
                    stats_data.get('updated_at') or datetime.now()
                ))
                
                migrated_count += 1
        
        self.pg_conn.commit()
        print(f"✓ 小时统计数据迁移完成: {migrated_count} 条记录")

    def setup_redis_cache_layer(self):
        """设置 Redis 缓存层"""
        print("\n=== 设置 Redis 缓存层 ===")
        
        # 清理旧的 Redis 数据结构，保留作为缓存
        print("清理旧的数据结构...")
        
        # 缓存活跃的 API 密钥
        self.pg_cursor.execute("""
            SELECT id, key_value, group_id, status, quota_limit, quota_used, request_count
            FROM api_keys 
            WHERE status = 'active' 
            AND (last_used_at IS NULL OR last_used_at > NOW() - INTERVAL '7 days')
            LIMIT 1000
        """)
        
        active_keys = self.pg_cursor.fetchall()
        cached_count = 0
        
        for key_record in active_keys:
            cache_data = {
                'id': key_record['id'],
                'group_id': key_record['group_id'],
                'status': key_record['status'],
                'quota_remaining': (key_record['quota_limit'] or 0) - (key_record['quota_used'] or 0),
                'request_count': key_record['request_count']
            }
            
            # 缓存到 Redis，1小时过期
            self.redis_client.setex(
                f"api_key_cache:{key_record['key_value']}", 
                3600,
                json.dumps(cache_data)
            )
            cached_count += 1
        
        print(f"✓ 缓存了 {cached_count} 个活跃 API 密钥")
        
        # 缓存系统设置
        self.pg_cursor.execute("SELECT setting_key, setting_value FROM system_settings")
        settings = self.pg_cursor.fetchall()
        
        for setting in settings:
            self.redis_client.setex(
                f"setting_cache:{setting['setting_key']}", 
                1800,  # 30分钟过期
                setting['setting_value']
            )
        
        print(f"✓ 缓存了 {len(settings)} 个系统设置")

    def create_migration_metadata(self):
        """创建迁移元数据"""
        print("\n=== 创建迁移元数据 ===")
        
        # 在 PostgreSQL 中记录迁移信息
        self.pg_cursor.execute("""
            INSERT INTO system_settings (setting_key, setting_value, description, category)
            VALUES (%s, %s, %s, %s)
            ON CONFLICT (setting_key) DO UPDATE SET
                setting_value = EXCLUDED.setting_value,
                updated_at = NOW()
        """, (
            'migration_completed_at',
            datetime.now().isoformat(),
            'Dual database migration completion timestamp',
            'migration'
        ))
        
        self.pg_cursor.execute("""
            INSERT INTO system_settings (setting_key, setting_value, description, category)
            VALUES (%s, %s, %s, %s)
            ON CONFLICT (setting_key) DO UPDATE SET
                setting_value = EXCLUDED.setting_value,
                updated_at = NOW()
        """, (
            'database_architecture',
            'postgresql_redis_dual',
            'Current database architecture type',
            'system'
        ))
        
        self.pg_conn.commit()
        print("✓ 迁移元数据已创建")

    def run_migration(self):
        """执行完整的双数据库迁移"""
        print("开始 Redis 到 PostgreSQL + Redis 双数据库迁移...")
        print(f"源: Redis")
        print(f"目标: PostgreSQL (主存储) + Redis (缓存层)")
        
        try:
            # 执行迁移步骤
            self.migrate_system_settings()
            self.migrate_groups()
            self.migrate_api_keys()
            self.migrate_request_logs()
            self.migrate_hourly_stats()
            self.setup_redis_cache_layer()
            self.create_migration_metadata()
            
            print("\n🎉 双数据库迁移完成!")
            print("\n📝 后续步骤:")
            print("  1. 重启服务以使用新的数据库配置")
            print("  2. 验证数据完整性")
            print("  3. 监控系统性能")
            
        except Exception as e:
            print(f"\n❌ 迁移过程中出现错误: {e}")
            self.pg_conn.rollback()
            raise
        finally:
            if self.pg_conn:
                self.pg_conn.close()

if __name__ == "__main__":
    # 检测运行环境
    import socket
    try:
        socket.gethostbyname('redis')
        socket.gethostbyname('postgres')
        print("检测到 Docker 环境")
        redis_host = "redis"
        pg_host = "postgres"
    except socket.gaierror:
        print("使用 localhost 连接")
        redis_host = "localhost"
        pg_host = "localhost"
    
    # 执行迁移
    migrator = DualDatabaseMigrator(redis_host=redis_host, pg_host=pg_host)
    migrator.run_migration()
