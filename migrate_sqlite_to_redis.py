#!/usr/bin/env python3
"""
SQLite to Redis Migration Script for GPT-Load
将SQLite数据库中的数据迁移到Redis
"""

import sqlite3
import redis
import json
import sys
import os
from datetime import datetime
from typing import Dict, List, Any

class SQLiteToRedisMigrator:
    def __init__(self, sqlite_path: str, redis_host: str = 'localhost', redis_port: int = 6379, redis_db: int = 0):
        """初始化迁移器"""
        self.sqlite_path = sqlite_path
        self.redis_client = redis.Redis(host=redis_host, port=redis_port, db=redis_db, decode_responses=True)
        
        # 测试连接
        try:
            self.redis_client.ping()
            print(f"✓ Redis连接成功: {redis_host}:{redis_port}")
        except Exception as e:
            print(f"✗ Redis连接失败: {e}")
            sys.exit(1)
            
        if not os.path.exists(sqlite_path):
            print(f"✗ SQLite数据库文件不存在: {sqlite_path}")
            sys.exit(1)
        print(f"✓ SQLite数据库文件存在: {sqlite_path}")

    def migrate_system_settings(self):
        """迁移系统设置"""
        print("\n=== 迁移系统设置 ===")
        
        conn = sqlite3.connect(self.sqlite_path)
        conn.row_factory = sqlite3.Row
        cursor = conn.cursor()
        
        cursor.execute("SELECT * FROM system_settings")
        settings = cursor.fetchall()
        
        migrated_count = 0
        for setting in settings:
            key = f"gpt-load:system_settings:{setting['setting_key']}"
            value = {
                'id': str(setting['id']),
                'setting_key': setting['setting_key'] or '',
                'setting_value': setting['setting_value'] or '',
                'description': setting['description'] or '',
                'created_at': setting['created_at'] or '',
                'updated_at': setting['updated_at'] or ''
            }
            
            self.redis_client.hset(key, mapping=value)
            migrated_count += 1
            print(f"  ✓ {setting['setting_key']}: {setting['setting_value']}")
        
        # 创建设置索引
        setting_keys = [setting['setting_key'] for setting in settings]
        self.redis_client.sadd("gpt-load:system_settings:keys", *setting_keys)
        
        conn.close()
        print(f"✓ 系统设置迁移完成: {migrated_count} 条记录")

    def migrate_groups(self):
        """迁移组配置"""
        print("\n=== 迁移组配置 ===")
        
        conn = sqlite3.connect(self.sqlite_path)
        conn.row_factory = sqlite3.Row
        cursor = conn.cursor()
        
        cursor.execute("SELECT * FROM groups")
        groups = cursor.fetchall()
        
        migrated_count = 0
        for group in groups:
            key = f"gpt-load:groups:{group['id']}"
            value = {
                'id': str(group['id']),
                'name': group['name'] or '',
                'display_name': group['display_name'] or '',
                'proxy_keys': group['proxy_keys'] or '',
                'description': group['description'] or '',
                'upstreams': group['upstreams'] or '',
                'validation_endpoint': group['validation_endpoint'] or '',
                'channel_type': group['channel_type'] or '',
                'sort': str(group['sort'] or 0),
                'test_model': group['test_model'] or '',
                'param_overrides': group['param_overrides'] or '',
                'config': group['config'] or '',
                'last_validated_at': group['last_validated_at'] or '',
                'created_at': group['created_at'] or '',
                'updated_at': group['updated_at'] or ''
            }
            
            self.redis_client.hset(key, mapping=value)
            
            # 创建名称到ID的映射
            self.redis_client.set(f"gpt-load:groups:name:{group['name']}", group['id'])
            
            migrated_count += 1
            print(f"  ✓ {group['name']} (ID: {group['id']})")
        
        # 创建组ID索引
        group_ids = [str(group['id']) for group in groups]
        self.redis_client.sadd("gpt-load:groups:ids", *group_ids)
        
        conn.close()
        print(f"✓ 组配置迁移完成: {migrated_count} 条记录")

    def migrate_api_keys(self):
        """迁移API密钥"""
        print("\n=== 迁移API密钥 ===")
        
        conn = sqlite3.connect(self.sqlite_path)
        conn.row_factory = sqlite3.Row
        cursor = conn.cursor()
        
        # 分批处理大量数据
        batch_size = 1000
        offset = 0
        total_migrated = 0
        
        while True:
            cursor.execute(f"SELECT * FROM api_keys LIMIT {batch_size} OFFSET {offset}")
            api_keys = cursor.fetchall()
            
            if not api_keys:
                break
                
            pipe = self.redis_client.pipeline()
            
            for api_key in api_keys:
                key = f"gpt-load:api_keys:{api_key['id']}"
                value = {
                    'id': str(api_key['id']),
                    'key_value': api_key['key_value'] or '',
                    'group_id': str(api_key['group_id']),
                    'status': api_key['status'] or 'active',
                    'request_count': str(api_key['request_count'] or 0),
                    'failure_count': str(api_key['failure_count'] or 0),
                    'last_used_at': api_key['last_used_at'] or '',
                    'created_at': api_key['created_at'] or '',
                    'updated_at': api_key['updated_at'] or ''
                }
                
                pipe.hset(key, mapping=value)
                
                # 创建组ID索引
                pipe.sadd(f"gpt-load:api_keys:group:{api_key['group_id']}", api_key['id'])
                
                # 创建状态索引
                pipe.sadd(f"gpt-load:api_keys:status:{api_key['status']}", api_key['id'])
            
            pipe.execute()
            total_migrated += len(api_keys)
            offset += batch_size
            print(f"  ✓ 已迁移 {total_migrated} 个API密钥...")
        
        conn.close()
        print(f"✓ API密钥迁移完成: {total_migrated} 条记录")

    def migrate_request_logs(self):
        """迁移请求日志"""
        print("\n=== 迁移请求日志 ===")
        
        conn = sqlite3.connect(self.sqlite_path)
        conn.row_factory = sqlite3.Row
        cursor = conn.cursor()
        
        cursor.execute("SELECT * FROM request_logs ORDER BY timestamp DESC")
        logs = cursor.fetchall()
        
        migrated_count = 0
        pipe = self.redis_client.pipeline()
        
        for log in logs:
            key = f"gpt-load:request_logs:{log['id']}"
            value = {
                'id': log['id'] or '',
                'timestamp': log['timestamp'] or '',
                'group_id': str(log['group_id'] or 0),
                'group_name': log['group_name'] or '',
                'key_value': log['key_value'] or '',
                'is_success': str(log['is_success'] or 0),
                'source_ip': log['source_ip'] or '',
                'status_code': str(log['status_code'] or 0),
                'request_path': log['request_path'] or '',
                'duration': str(log['duration'] or 0),
                'error_message': log['error_message'] or '',
                'user_agent': log['user_agent'] or '',
                'retries': str(log['retries'] or 0),
                'upstream_addr': log['upstream_addr'] or '',
                'is_stream': str(log['is_stream'] or 0)
            }
            
            pipe.hset(key, mapping=value)
            
            # 创建时间索引（按日期分组）
            if log['timestamp']:
                try:
                    from datetime import datetime
                    # 解析时间戳并转换为Unix时间戳（数字）
                    dt = datetime.fromisoformat(log['timestamp'].replace('Z', '+00:00'))
                    timestamp_score = dt.timestamp()
                    date_key = log['timestamp'][:10]  # YYYY-MM-DD
                    pipe.zadd(f"gpt-load:request_logs:date:{date_key}", {log['id']: timestamp_score})
                except (ValueError, AttributeError):
                    # 如果时间戳格式有问题，跳过时间索引
                    pass
            
            # 创建组索引
            pipe.sadd(f"gpt-load:request_logs:group:{log['group_id']}", log['id'])
            
            migrated_count += 1
        
        pipe.execute()
        conn.close()
        print(f"✓ 请求日志迁移完成: {migrated_count} 条记录")

    def migrate_group_hourly_stats(self):
        """迁移小时统计数据"""
        print("\n=== 迁移小时统计数据 ===")
        
        conn = sqlite3.connect(self.sqlite_path)
        conn.row_factory = sqlite3.Row
        cursor = conn.cursor()
        
        cursor.execute("SELECT * FROM group_hourly_stats")
        stats = cursor.fetchall()
        
        migrated_count = 0
        pipe = self.redis_client.pipeline()
        
        for stat in stats:
            key = f"gpt-load:group_hourly_stats:{stat['id']}"
            value = {
                'id': str(stat['id']),
                'time': stat['time'] or '',
                'group_id': str(stat['group_id'] or 0),
                'success_count': str(stat['success_count'] or 0),
                'failure_count': str(stat['failure_count'] or 0),
                'created_at': stat['created_at'] or '',
                'updated_at': stat['updated_at'] or ''
            }
            
            pipe.hset(key, mapping=value)
            
            # 创建时间序列索引
            if stat['time']:
                try:
                    from datetime import datetime
                    # 解析时间戳并转换为Unix时间戳（数字）
                    dt = datetime.fromisoformat(stat['time'].replace('Z', '+00:00'))
                    timestamp_score = dt.timestamp()
                    pipe.zadd(f"gpt-load:group_hourly_stats:group:{stat['group_id']}",
                             {stat['id']: timestamp_score})
                except (ValueError, AttributeError):
                    # 如果时间戳格式有问题，跳过时间索引
                    pass
            
            migrated_count += 1
        
        pipe.execute()
        conn.close()
        print(f"✓ 小时统计数据迁移完成: {migrated_count} 条记录")

    def create_migration_metadata(self):
        """创建迁移元数据"""
        print("\n=== 创建迁移元数据 ===")
        
        metadata = {
            'migration_time': datetime.now().isoformat(),
            'source': 'sqlite',
            'target': 'redis',
            'version': '1.0',
            'tables_migrated': ','.join([
                'system_settings',
                'groups',
                'api_keys',
                'request_logs',
                'group_hourly_stats'
            ])
        }
        
        self.redis_client.hset("gpt-load:migration:metadata", mapping=metadata)
        print("✓ 迁移元数据已创建")

    def run_migration(self, auto_clean=False):
        """执行完整迁移"""
        print("开始SQLite到Redis数据迁移...")
        print(f"源数据库: {self.sqlite_path}")
        print(f"目标Redis: {self.redis_client.connection_pool.connection_kwargs}")

        # 清理现有数据（可选）
        if auto_clean:
            response = 'y'
        else:
            try:
                response = input("\n是否清理Redis中现有的gpt-load数据? (y/N): ")
            except EOFError:
                # 在非交互环境中默认清理
                response = 'y'
                print("\n非交互模式，自动清理现有数据...")

        if response.lower() == 'y':
            keys = self.redis_client.keys("gpt-load:*")
            if keys:
                self.redis_client.delete(*keys)
                print(f"✓ 已清理 {len(keys)} 个现有键")
            else:
                print("✓ 没有现有数据需要清理")

        # 执行迁移
        self.migrate_system_settings()
        self.migrate_groups()
        self.migrate_api_keys()
        self.migrate_request_logs()
        self.migrate_group_hourly_stats()
        self.create_migration_metadata()

        print("\n🎉 数据迁移完成!")
        print("\n请更新.env文件中的REDIS_DSN配置，然后重启服务。")

if __name__ == "__main__":
    # 配置参数
    sqlite_path = "./data/gpt-load.db"

    # 检测运行环境，如果在Docker容器中则使用容器名
    import socket
    try:
        # 尝试解析redis主机名，如果成功说明在Docker网络中
        socket.gethostbyname('redis')
        redis_host = "redis"
        print("检测到Docker环境，使用Redis容器名连接")
    except socket.gaierror:
        # 如果失败，使用localhost
        redis_host = "localhost"
        print("使用localhost连接Redis")

    redis_port = 6379
    redis_db = 0

    # 检查是否为非交互模式
    import sys
    auto_clean = '--auto-clean' in sys.argv or not sys.stdin.isatty()

    # 执行迁移
    migrator = SQLiteToRedisMigrator(sqlite_path, redis_host, redis_port, redis_db)
    migrator.run_migration(auto_clean=auto_clean)
