# PostgreSQL 迁移实施计划

## 迁移触发条件检查清单

### ✅ 量化指标
- [ ] Redis 内存使用 > 1GB (当前: 9.03MB)
- [ ] 数据键数量 > 500,000 (当前: 27,039)
- [ ] API 密钥数量 > 100,000 (当前: 13,435)
- [ ] 并发请求 > 1,000 QPS
- [ ] 用户数量 > 100 (当前: 单用户)

### ✅ 功能需求
- [ ] 需要复杂的 SQL 查询和报表
- [ ] 需要多租户和权限管理
- [ ] 需要数据分析和商业智能
- [ ] 需要 ACID 事务保证
- [ ] 需要数据归档和长期存储

### ✅ 运维需求
- [ ] 需要数据备份和恢复策略
- [ ] 需要高可用和灾难恢复
- [ ] 需要数据合规和审计
- [ ] 团队具备 PostgreSQL 运维能力

## 实施步骤

### 第一步: 启用 PostgreSQL 服务
```yaml
# docker-compose.yml 修改
postgres:
  image: "postgres:16"
  container_name: gpt-load-postgres
  environment:
    POSTGRES_USER: gptload
    POSTGRES_PASSWORD: ${POSTGRES_PASSWORD:-secure_password_123}
    POSTGRES_DB: gpt_load
  ports:
    - "5432:5432"
  volumes:
    - ./data/postgres:/var/lib/postgresql/data
    - ./init-scripts:/docker-entrypoint-initdb.d
  healthcheck:
    test: ["CMD-SHELL", "pg_isready -U gptload -d gpt_load"]
    interval: 5s
    timeout: 5s
    retries: 10
```

### 第二步: 数据库设计
```sql
-- 用户和权限管理
CREATE TABLE users (
    id SERIAL PRIMARY KEY,
    username VARCHAR(255) UNIQUE NOT NULL,
    email VARCHAR(255) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    role VARCHAR(50) DEFAULT 'user',
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);

CREATE TABLE organizations (
    id SERIAL PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    plan VARCHAR(50) DEFAULT 'basic',
    quota_limit INTEGER DEFAULT 10000,
    created_at TIMESTAMP DEFAULT NOW()
);

-- API 密钥管理 (增强版)
CREATE TABLE api_keys (
    id SERIAL PRIMARY KEY,
    key_value VARCHAR(700) NOT NULL UNIQUE,
    user_id INTEGER REFERENCES users(id),
    organization_id INTEGER REFERENCES organizations(id),
    group_id INTEGER NOT NULL,
    name VARCHAR(255),
    status VARCHAR(50) DEFAULT 'active',
    quota_limit INTEGER,
    quota_used INTEGER DEFAULT 0,
    rate_limit INTEGER DEFAULT 100,
    expires_at TIMESTAMP,
    last_used_at TIMESTAMP,
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);

-- 请求日志 (分区表)
CREATE TABLE request_logs (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    api_key_id INTEGER REFERENCES api_keys(id),
    timestamp TIMESTAMP NOT NULL,
    method VARCHAR(10),
    endpoint VARCHAR(500),
    status_code INTEGER,
    response_time INTEGER,
    request_size INTEGER,
    response_size INTEGER,
    error_message TEXT,
    user_agent VARCHAR(512),
    source_ip INET
) PARTITION BY RANGE (timestamp);

-- 创建月度分区
CREATE TABLE request_logs_2025_01 PARTITION OF request_logs
    FOR VALUES FROM ('2025-01-01') TO ('2025-02-01');

-- 统计数据
CREATE TABLE usage_statistics (
    id SERIAL PRIMARY KEY,
    organization_id INTEGER REFERENCES organizations(id),
    date DATE NOT NULL,
    total_requests INTEGER DEFAULT 0,
    successful_requests INTEGER DEFAULT 0,
    failed_requests INTEGER DEFAULT 0,
    total_cost DECIMAL(10,4) DEFAULT 0,
    created_at TIMESTAMP DEFAULT NOW()
);

-- 索引优化
CREATE INDEX idx_api_keys_user_id ON api_keys(user_id);
CREATE INDEX idx_api_keys_status ON api_keys(status);
CREATE INDEX idx_request_logs_timestamp ON request_logs(timestamp);
CREATE INDEX idx_request_logs_api_key_id ON request_logs(api_key_id);
```

### 第三步: 数据迁移脚本
```python
#!/usr/bin/env python3
"""
Redis to PostgreSQL + Redis 双数据库迁移
"""

import asyncio
import asyncpg
import redis
import json
from datetime import datetime

class DualDatabaseMigrator:
    def __init__(self):
        self.redis_client = redis.Redis(host='redis', port=6379, db=0, decode_responses=True)
        self.pg_pool = None
    
    async def init_postgres(self):
        self.pg_pool = await asyncpg.create_pool(
            "******************************************************/gpt_load"
        )
    
    async def migrate_to_dual_storage(self):
        """迁移到双数据库架构"""
        
        # 1. 迁移用户数据
        await self.migrate_users()
        
        # 2. 迁移 API 密钥到 PostgreSQL
        await self.migrate_api_keys()
        
        # 3. 迁移历史日志到 PostgreSQL
        await self.migrate_request_logs()
        
        # 4. 保留热数据在 Redis
        await self.setup_redis_cache()
        
        print("✅ 双数据库迁移完成")
    
    async def migrate_api_keys(self):
        """迁移 API 密钥"""
        redis_keys = self.redis_client.keys("gpt-load:api_keys:*")
        
        async with self.pg_pool.acquire() as conn:
            for redis_key in redis_keys:
                key_data = self.redis_client.hgetall(redis_key)
                
                await conn.execute("""
                    INSERT INTO api_keys 
                    (key_value, group_id, status, request_count, failure_count, created_at)
                    VALUES ($1, $2, $3, $4, $5, $6)
                    ON CONFLICT (key_value) DO NOTHING
                """, 
                key_data['key_value'],
                int(key_data['group_id']),
                key_data['status'],
                int(key_data['request_count']),
                int(key_data['failure_count']),
                datetime.fromisoformat(key_data['created_at']) if key_data['created_at'] else datetime.now()
                )
        
        print(f"✅ 迁移了 {len(redis_keys)} 个 API 密钥到 PostgreSQL")
    
    async def setup_redis_cache(self):
        """设置 Redis 缓存策略"""
        
        # 缓存活跃的 API 密钥
        async with self.pg_pool.acquire() as conn:
            active_keys = await conn.fetch("""
                SELECT key_value, group_id, status, quota_limit, quota_used
                FROM api_keys 
                WHERE status = 'active' AND last_used_at > NOW() - INTERVAL '7 days'
            """)
            
            for key_record in active_keys:
                cache_data = {
                    'group_id': key_record['group_id'],
                    'status': key_record['status'],
                    'quota_remaining': (key_record['quota_limit'] or 0) - (key_record['quota_used'] or 0)
                }
                
                self.redis_client.setex(
                    f"api_key_cache:{key_record['key_value']}", 
                    3600,  # 1小时过期
                    json.dumps(cache_data)
                )
        
        print("✅ Redis 缓存设置完成")

# 使用示例
async def main():
    migrator = DualDatabaseMigrator()
    await migrator.init_postgres()
    await migrator.migrate_to_dual_storage()

if __name__ == "__main__":
    asyncio.run(main())
```

### 第四步: 应用层改造
```python
# 双数据库访问层
class DataAccessLayer:
    def __init__(self):
        self.redis = redis.Redis(host='redis', decode_responses=True)
        self.pg_pool = asyncpg.create_pool("postgresql://...")
    
    async def get_api_key_info(self, key_value):
        """获取 API 密钥信息 - 缓存优先"""
        
        # 1. 先查 Redis 缓存
        cached = self.redis.get(f"api_key_cache:{key_value}")
        if cached:
            return json.loads(cached)
        
        # 2. 查 PostgreSQL
        async with self.pg_pool.acquire() as conn:
            key_data = await conn.fetchrow(
                "SELECT * FROM api_keys WHERE key_value = $1", key_value
            )
            
            if key_data:
                # 3. 写入缓存
                cache_data = dict(key_data)
                self.redis.setex(f"api_key_cache:{key_value}", 3600, json.dumps(cache_data))
                return cache_data
        
        return None
    
    async def log_request(self, request_data):
        """记录请求 - 异步写入"""
        
        # 1. 立即更新 Redis 计数
        self.redis.incr(f"requests:daily:{request_data['api_key']}:{datetime.now().date()}")
        
        # 2. 异步写入 PostgreSQL
        asyncio.create_task(self._save_to_postgres(request_data))
    
    async def _save_to_postgres(self, request_data):
        """异步保存到 PostgreSQL"""
        async with self.pg_pool.acquire() as conn:
            await conn.execute("""
                INSERT INTO request_logs 
                (api_key_id, timestamp, method, endpoint, status_code, response_time)
                VALUES ($1, $2, $3, $4, $5, $6)
            """, *request_data.values())
```

## 监控和维护

### 性能监控
```bash
# PostgreSQL 监控
docker compose exec postgres psql -U gptload -d gpt_load -c "
SELECT 
    schemaname,
    tablename,
    n_tup_ins as inserts,
    n_tup_upd as updates,
    n_tup_del as deletes
FROM pg_stat_user_tables;
"

# Redis 监控
docker compose exec redis redis-cli info stats
```

### 数据一致性检查
```python
async def check_consistency():
    # 检查 Redis 和 PostgreSQL 的数据一致性
    pg_count = await conn.fetchval("SELECT COUNT(*) FROM api_keys WHERE status = 'active'")
    redis_keys = len(redis.keys("api_key_cache:*"))
    
    if abs(pg_count - redis_keys) > 100:  # 允许一定差异
        send_alert("数据一致性告警")
```
