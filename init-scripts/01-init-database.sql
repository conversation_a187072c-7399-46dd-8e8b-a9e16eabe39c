-- GPT-Load PostgreSQL 数据库初始化脚本
-- 创建增强的数据库结构，支持多用户和企业级功能

-- 启用必要的扩展
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pg_stat_statements";

-- 用户和组织管理
CREATE TABLE IF NOT EXISTS organizations (
    id SERIAL PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    display_name VARCHAR(255),
    plan VARCHAR(50) DEFAULT 'basic',
    quota_limit BIGINT DEFAULT 100000,
    quota_used BIGINT DEFAULT 0,
    status VARCHAR(50) DEFAULT 'active',
    settings JSONB DEFAULT '{}',
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);

CREATE TABLE IF NOT EXISTS users (
    id SERIAL PRIMARY KEY,
    username VA<PERSON><PERSON><PERSON>(255) UNIQUE NOT NULL,
    email VARCHAR(255) UNIQUE NOT NULL,
    password_hash VARCHAR(255),
    role VARCHAR(50) DEFAULT 'user',
    organization_id INTEGER REFERENCES organizations(id),
    status VARCHAR(50) DEFAULT 'active',
    last_login_at TIMESTAMP,
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);

-- API 组管理 (增强版)
CREATE TABLE IF NOT EXISTS api_groups (
    id SERIAL PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    display_name VARCHAR(255),
    organization_id INTEGER REFERENCES organizations(id),
    proxy_keys TEXT,
    description TEXT,
    upstreams JSONB NOT NULL DEFAULT '[]',
    validation_endpoint VARCHAR(255),
    channel_type VARCHAR(50) NOT NULL DEFAULT 'openai',
    sort_order INTEGER DEFAULT 0,
    test_model VARCHAR(255) NOT NULL DEFAULT 'gpt-3.5-turbo',
    param_overrides JSONB DEFAULT '{}',
    config JSONB DEFAULT '{}',
    rate_limit INTEGER DEFAULT 1000,
    quota_limit BIGINT,
    status VARCHAR(50) DEFAULT 'active',
    last_validated_at TIMESTAMP,
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW(),
    CONSTRAINT uni_api_groups_name_org UNIQUE (name, organization_id)
);

-- API 密钥管理 (增强版)
CREATE TABLE IF NOT EXISTS api_keys (
    id SERIAL PRIMARY KEY,
    key_value VARCHAR(700) NOT NULL UNIQUE,
    name VARCHAR(255),
    user_id INTEGER REFERENCES users(id),
    organization_id INTEGER REFERENCES organizations(id),
    group_id INTEGER REFERENCES api_groups(id) NOT NULL,
    status VARCHAR(50) NOT NULL DEFAULT 'active',
    request_count BIGINT NOT NULL DEFAULT 0,
    failure_count BIGINT NOT NULL DEFAULT 0,
    success_count BIGINT NOT NULL DEFAULT 0,
    quota_limit BIGINT,
    quota_used BIGINT DEFAULT 0,
    rate_limit INTEGER DEFAULT 100,
    cost_total DECIMAL(12,4) DEFAULT 0,
    expires_at TIMESTAMP,
    last_used_at TIMESTAMP,
    last_error TEXT,
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);

-- 请求日志 (分区表，按月分区)
CREATE TABLE IF NOT EXISTS request_logs (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    timestamp TIMESTAMP NOT NULL DEFAULT NOW(),
    api_key_id INTEGER REFERENCES api_keys(id),
    group_id INTEGER REFERENCES api_groups(id),
    organization_id INTEGER REFERENCES organizations(id),
    group_name VARCHAR(255),
    key_value VARCHAR(700),
    is_success BOOLEAN NOT NULL DEFAULT false,
    source_ip INET,
    status_code INTEGER NOT NULL DEFAULT 0,
    request_path VARCHAR(500),
    method VARCHAR(10) DEFAULT 'POST',
    duration INTEGER NOT NULL DEFAULT 0,
    request_size INTEGER DEFAULT 0,
    response_size INTEGER DEFAULT 0,
    cost DECIMAL(10,6) DEFAULT 0,
    error_message TEXT,
    user_agent VARCHAR(512),
    retries INTEGER NOT NULL DEFAULT 0,
    upstream_addr VARCHAR(500),
    is_stream BOOLEAN NOT NULL DEFAULT false,
    model VARCHAR(255),
    tokens_input INTEGER DEFAULT 0,
    tokens_output INTEGER DEFAULT 0,
    metadata JSONB DEFAULT '{}'
) PARTITION BY RANGE (timestamp);

-- 创建当前月份的分区
DO $$
DECLARE
    current_month TEXT;
    next_month TEXT;
    partition_name TEXT;
BEGIN
    current_month := to_char(NOW(), 'YYYY_MM');
    next_month := to_char(NOW() + INTERVAL '1 month', 'YYYY_MM');
    partition_name := 'request_logs_' || current_month;
    
    EXECUTE format('CREATE TABLE IF NOT EXISTS %I PARTITION OF request_logs 
                    FOR VALUES FROM (%L) TO (%L)',
                   partition_name,
                   date_trunc('month', NOW()),
                   date_trunc('month', NOW() + INTERVAL '1 month'));
END $$;

-- 小时统计数据
CREATE TABLE IF NOT EXISTS hourly_statistics (
    id SERIAL PRIMARY KEY,
    time_bucket TIMESTAMP NOT NULL,
    organization_id INTEGER REFERENCES organizations(id),
    group_id INTEGER REFERENCES api_groups(id),
    success_count BIGINT NOT NULL DEFAULT 0,
    failure_count BIGINT NOT NULL DEFAULT 0,
    total_requests BIGINT NOT NULL DEFAULT 0,
    total_cost DECIMAL(12,4) DEFAULT 0,
    avg_response_time INTEGER DEFAULT 0,
    total_tokens BIGINT DEFAULT 0,
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW(),
    UNIQUE(time_bucket, organization_id, group_id)
);

-- 系统设置 (保持兼容)
CREATE TABLE IF NOT EXISTS system_settings (
    id SERIAL PRIMARY KEY,
    setting_key VARCHAR(255) NOT NULL UNIQUE,
    setting_value TEXT NOT NULL,
    description VARCHAR(512),
    category VARCHAR(100) DEFAULT 'general',
    is_public BOOLEAN DEFAULT false,
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);

-- 审计日志
CREATE TABLE IF NOT EXISTS audit_logs (
    id SERIAL PRIMARY KEY,
    user_id INTEGER REFERENCES users(id),
    organization_id INTEGER REFERENCES organizations(id),
    action VARCHAR(100) NOT NULL,
    resource_type VARCHAR(100),
    resource_id VARCHAR(100),
    details JSONB DEFAULT '{}',
    ip_address INET,
    user_agent VARCHAR(512),
    timestamp TIMESTAMP DEFAULT NOW()
);

-- 创建索引
CREATE INDEX IF NOT EXISTS idx_api_keys_user_id ON api_keys(user_id);
CREATE INDEX IF NOT EXISTS idx_api_keys_group_id ON api_keys(group_id);
CREATE INDEX IF NOT EXISTS idx_api_keys_organization_id ON api_keys(organization_id);
CREATE INDEX IF NOT EXISTS idx_api_keys_status ON api_keys(status);
CREATE INDEX IF NOT EXISTS idx_api_keys_key_value ON api_keys(key_value);

CREATE INDEX IF NOT EXISTS idx_request_logs_timestamp ON request_logs(timestamp);
CREATE INDEX IF NOT EXISTS idx_request_logs_api_key_id ON request_logs(api_key_id);
CREATE INDEX IF NOT EXISTS idx_request_logs_group_id ON request_logs(group_id);
CREATE INDEX IF NOT EXISTS idx_request_logs_organization_id ON request_logs(organization_id);
CREATE INDEX IF NOT EXISTS idx_request_logs_is_success ON request_logs(is_success);

CREATE INDEX IF NOT EXISTS idx_hourly_statistics_time_bucket ON hourly_statistics(time_bucket);
CREATE INDEX IF NOT EXISTS idx_hourly_statistics_organization_id ON hourly_statistics(organization_id);
CREATE INDEX IF NOT EXISTS idx_hourly_statistics_group_id ON hourly_statistics(group_id);

CREATE INDEX IF NOT EXISTS idx_audit_logs_user_id ON audit_logs(user_id);
CREATE INDEX IF NOT EXISTS idx_audit_logs_timestamp ON audit_logs(timestamp);
CREATE INDEX IF NOT EXISTS idx_audit_logs_action ON audit_logs(action);

-- 创建视图
CREATE OR REPLACE VIEW api_key_stats AS
SELECT 
    ak.id,
    ak.key_value,
    ak.name,
    ag.name as group_name,
    o.name as organization_name,
    ak.status,
    ak.request_count,
    ak.success_count,
    ak.failure_count,
    CASE 
        WHEN ak.request_count > 0 
        THEN ROUND((ak.success_count::DECIMAL / ak.request_count) * 100, 2)
        ELSE 0 
    END as success_rate,
    ak.quota_limit,
    ak.quota_used,
    CASE 
        WHEN ak.quota_limit IS NOT NULL 
        THEN ROUND((ak.quota_used::DECIMAL / ak.quota_limit) * 100, 2)
        ELSE NULL 
    END as quota_usage_percent,
    ak.cost_total,
    ak.last_used_at,
    ak.created_at
FROM api_keys ak
LEFT JOIN api_groups ag ON ak.group_id = ag.id
LEFT JOIN organizations o ON ak.organization_id = o.id;

-- 插入默认数据
INSERT INTO organizations (id, name, display_name, plan) 
VALUES (1, 'default', 'Default Organization', 'enterprise')
ON CONFLICT (id) DO NOTHING;

INSERT INTO users (id, username, email, role, organization_id)
VALUES (1, 'admin', '<EMAIL>', 'admin', 1)
ON CONFLICT (username) DO NOTHING;

-- 重置序列
SELECT setval('organizations_id_seq', (SELECT MAX(id) FROM organizations));
SELECT setval('users_id_seq', (SELECT MAX(id) FROM users));

-- 创建函数：自动更新 updated_at
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- 创建触发器
CREATE TRIGGER update_organizations_updated_at BEFORE UPDATE ON organizations FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_users_updated_at BEFORE UPDATE ON users FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_api_groups_updated_at BEFORE UPDATE ON api_groups FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_api_keys_updated_at BEFORE UPDATE ON api_keys FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_system_settings_updated_at BEFORE UPDATE ON system_settings FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

COMMENT ON DATABASE gpt_load IS 'GPT-Load Proxy Database with PostgreSQL + Redis Architecture';
