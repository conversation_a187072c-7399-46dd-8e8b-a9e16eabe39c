-- GPT-Load 原始表结构 (基于 SQLite 结构)
-- 创建与原始应用兼容的表结构

-- 删除之前创建的不兼容表
DROP TABLE IF EXISTS api_groups CASCADE;
DROP TABLE IF EXISTS api_keys CASCADE;
DROP TABLE IF EXISTS organizations CASCADE;
DROP TABLE IF EXISTS users CASCADE;

-- 系统设置表
CREATE TABLE IF NOT EXISTS system_settings (
    id SERIAL PRIMARY KEY,
    setting_key VARCHAR(255) NOT NULL UNIQUE,
    setting_value TEXT NOT NULL,
    description VARCHAR(512),
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);

-- 组表 (保持原始结构)
CREATE TABLE IF NOT EXISTS groups (
    id SERIAL PRIMARY KEY,
    name VARCHAR(255) NOT NULL UNIQUE,
    display_name VARCHAR(255),
    proxy_keys TEXT,
    description TEXT,
    upstreams TEXT NOT NULL DEFAULT '[]',
    validation_endpoint VARCHAR(255),
    channel_type VARCHAR(50) NOT NULL DEFAULT 'openai',
    sort INTEGER DEFAULT 0,
    test_model VARCHAR(255) NOT NULL DEFAULT 'gpt-3.5-turbo',
    param_overrides TEXT DEFAULT '{}',
    config TEXT DEFAULT '{}',
    last_validated_at TIMESTAMP,
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);

-- API 密钥表 (保持原始结构)
CREATE TABLE IF NOT EXISTS api_keys (
    id SERIAL PRIMARY KEY,
    key_value VARCHAR(700) NOT NULL UNIQUE,
    group_id INTEGER NOT NULL REFERENCES groups(id),
    status VARCHAR(50) NOT NULL DEFAULT 'active',
    request_count BIGINT NOT NULL DEFAULT 0,
    failure_count BIGINT NOT NULL DEFAULT 0,
    last_used_at TIMESTAMP,
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);

-- 请求日志表
CREATE TABLE IF NOT EXISTS request_logs (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    timestamp TIMESTAMP NOT NULL DEFAULT NOW(),
    group_id INTEGER REFERENCES groups(id),
    group_name VARCHAR(255),
    key_value VARCHAR(700),
    is_success BOOLEAN NOT NULL DEFAULT false,
    source_ip INET,
    status_code INTEGER NOT NULL DEFAULT 0,
    request_path VARCHAR(500),
    duration INTEGER NOT NULL DEFAULT 0,
    error_message TEXT,
    user_agent VARCHAR(512),
    retries INTEGER NOT NULL DEFAULT 0,
    upstream_addr VARCHAR(500),
    is_stream BOOLEAN NOT NULL DEFAULT false
);

-- 小时统计表
CREATE TABLE IF NOT EXISTS group_hourly_stats (
    id SERIAL PRIMARY KEY,
    time TIMESTAMP NOT NULL,
    group_id INTEGER NOT NULL REFERENCES groups(id),
    success_count BIGINT NOT NULL DEFAULT 0,
    failure_count BIGINT NOT NULL DEFAULT 0,
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW(),
    UNIQUE(time, group_id)
);

-- 创建索引
CREATE INDEX IF NOT EXISTS idx_api_keys_group_id ON api_keys(group_id);
CREATE INDEX IF NOT EXISTS idx_api_keys_status ON api_keys(status);
CREATE INDEX IF NOT EXISTS idx_api_keys_key_value ON api_keys(key_value);
CREATE INDEX IF NOT EXISTS idx_request_logs_timestamp ON request_logs(timestamp);
CREATE INDEX IF NOT EXISTS idx_request_logs_group_id ON request_logs(group_id);
CREATE INDEX IF NOT EXISTS idx_group_hourly_stats_time ON group_hourly_stats(time);
CREATE INDEX IF NOT EXISTS idx_group_hourly_stats_group_id ON group_hourly_stats(group_id);

-- 创建更新时间触发器函数
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- 创建触发器
DROP TRIGGER IF EXISTS update_system_settings_updated_at ON system_settings;
CREATE TRIGGER update_system_settings_updated_at 
    BEFORE UPDATE ON system_settings 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS update_groups_updated_at ON groups;
CREATE TRIGGER update_groups_updated_at 
    BEFORE UPDATE ON groups 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS update_api_keys_updated_at ON api_keys;
CREATE TRIGGER update_api_keys_updated_at 
    BEFORE UPDATE ON api_keys 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS update_group_hourly_stats_updated_at ON group_hourly_stats;
CREATE TRIGGER update_group_hourly_stats_updated_at 
    BEFORE UPDATE ON group_hourly_stats 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- 插入默认数据 (如果不存在)
INSERT INTO system_settings (setting_key, setting_value, description) 
VALUES 
    ('app_version', '1.0.0', 'Application version'),
    ('database_version', '1.0.0', 'Database schema version')
ON CONFLICT (setting_key) DO NOTHING;

COMMENT ON DATABASE "gpt-load" IS 'GPT-Load Proxy Database - Compatible with original SQLite structure';
