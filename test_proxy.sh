#!/bin/bash

# GPT-Load 代理测试脚本
# 测试 hajimi-warp SOCKS5 代理是否正常工作

set -e

echo "=== GPT-Load 代理配置测试 ==="
echo ""

# 检查 hajimi-warp 容器状态
echo "🔍 检查 hajimi-warp 容器状态..."
if docker ps | grep -q "hajimi-warp"; then
    echo "  ✓ hajimi-warp 容器正在运行"
    docker ps --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}" | grep hajimi-warp
else
    echo "  ❌ hajimi-warp 容器未运行"
    echo "  请先启动 hajimi-warp 容器"
    exit 1
fi

echo ""

# 检查代理端口
echo "🔍 检查代理端口连通性..."
if timeout 5 curl -s --socks5 127.0.0.1:1080 https://api.ipify.org >/dev/null 2>&1; then
    echo "  ✓ SOCKS5 代理 127.0.0.1:1080 可用"
else
    echo "  ❌ SOCKS5 代理不可用"
    echo "  请检查 hajimi-warp 容器状态"
    exit 1
fi

echo ""

# 测试 SOCKS5 代理
echo "🔍 测试 SOCKS5 代理功能..."
if command -v curl >/dev/null 2>&1; then
    echo "  测试通过代理访问外部服务..."
    
    # 测试不使用代理的 IP
    echo "  当前 IP (不使用代理):"
    timeout 10 curl -s https://api.ipify.org || echo "    无法获取当前 IP"
    
    echo ""
    
    # 测试使用代理的 IP
    echo "  代理 IP (使用 SOCKS5 代理):"
    timeout 10 curl -s --socks5 127.0.0.1:1080 https://api.ipify.org || echo "    无法通过代理获取 IP"
    
    echo ""
    
    # 测试访问 OpenAI API (通过代理)
    echo "  测试通过代理访问 OpenAI API..."
    response=$(timeout 10 curl -s --socks5 127.0.0.1:1080 -w "%{http_code}" -o /dev/null https://api.openai.com/v1/models || echo "000")
    if [ "$response" = "401" ] || [ "$response" = "200" ]; then
        echo "    ✓ 可以通过代理访问 OpenAI API (HTTP $response)"
    else
        echo "    ⚠️  代理访问 OpenAI API 异常 (HTTP $response)"
    fi
    
else
    echo "  ⚠️  curl 命令不可用，跳过代理功能测试"
fi

echo ""

# 检查 Docker 网络配置
echo "🔍 检查 Docker 网络配置..."
echo "  gpt-load 容器网络配置:"
if docker compose ps | grep -q "gpt-load"; then
    docker compose exec gpt-load cat /etc/hosts | grep "host.docker.internal" || echo "    ⚠️  host.docker.internal 未配置"
else
    echo "    ⚠️  gpt-load 容器未运行"
fi

echo ""

# 显示当前代理配置
echo "📋 当前代理配置:"
echo "  HTTP_PROXY: $(grep HTTP_PROXY .env | cut -d'=' -f2)"
echo "  HTTPS_PROXY: $(grep HTTPS_PROXY .env | cut -d'=' -f2)"
echo "  NO_PROXY: $(grep NO_PROXY .env | cut -d'=' -f2 || echo '未设置')"

echo ""

# 测试容器内代理连接
echo "🔍 测试容器内代理连接..."
if docker compose ps | grep -q "gpt-load.*Up"; then
    echo "  测试从 gpt-load 容器内访问代理..."
    
    # 测试容器内是否能连接到代理
    if docker compose exec gpt-load timeout 5 curl -s --socks5 host.docker.internal:1080 https://api.ipify.org >/dev/null 2>&1; then
        echo "    ✓ gpt-load 容器可以通过代理访问外网"
    else
        echo "    ❌ gpt-load 容器无法通过代理访问外网"
        echo "    请检查网络配置和防火墙设置"
    fi
    
    # 测试环境变量
    echo "  容器内代理环境变量:"
    docker compose exec gpt-load printenv | grep -E "(HTTP_PROXY|HTTPS_PROXY|NO_PROXY)" || echo "    ⚠️  代理环境变量未设置"
    
else
    echo "  ⚠️  gpt-load 容器未运行，无法测试容器内连接"
fi

echo ""

# 提供建议
echo "💡 配置建议:"
echo "  1. 确保 hajimi-warp 容器正常运行并监听 1080 端口"
echo "  2. 如果代理测试失败，请检查防火墙设置"
echo "  3. 可以在 .env 文件中调整 NO_PROXY 设置，排除内部服务"
echo "  4. 重启 gpt-load 服务以应用代理配置: docker compose restart gpt-load"

echo ""
echo "✅ 代理配置测试完成"
