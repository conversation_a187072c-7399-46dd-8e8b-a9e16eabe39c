#!/bin/bash

# GPT-Load 服务测试脚本
# 测试 GPT-Load 服务和数据库连接状态

set -e

echo "=== GPT-Load 服务状态测试 ==="
echo ""

# 检查 GPT-Load 服务状态
echo "🔍 检查 GPT-Load 服务状态..."
if docker compose ps | grep -q "gpt-load.*Up"; then
    echo "  ✓ GPT-Load 服务正在运行"
    docker compose ps --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}"
else
    echo "  ❌ GPT-Load 服务未运行"
    echo "  请先启动服务: docker compose up -d"
    exit 1
fi

echo ""

# 测试服务健康状态
echo "🔍 测试服务健康状态..."
if command -v curl >/dev/null 2>&1; then
    echo "  测试 GPT-Load 健康端点..."

    # 测试健康检查端点
    response=$(timeout 10 curl -s -w "%{http_code}" -o /dev/null http://localhost:3001/health || echo "000")
    if [ "$response" = "200" ]; then
        echo "    ✓ GPT-Load 健康检查通过 (HTTP $response)"

        # 获取健康状态详情
        health_info=$(timeout 5 curl -s http://localhost:3001/health 2>/dev/null || echo "无法获取详情")
        echo "    健康状态: $health_info"
    else
        echo "    ❌ GPT-Load 健康检查失败 (HTTP $response)"
    fi

    echo ""

    # 测试管理界面
    echo "  测试管理界面访问..."
    response=$(timeout 10 curl -s -w "%{http_code}" -o /dev/null http://localhost:3001/ || echo "000")
    if [ "$response" = "200" ]; then
        echo "    ✓ 管理界面可访问 (HTTP $response)"
    else
        echo "    ❌ 管理界面访问失败 (HTTP $response)"
    fi

else
    echo "  ⚠️  curl 命令不可用，跳过服务测试"
fi

echo ""

# 检查数据库连接
echo "🔍 检查数据库连接..."

# 检查 PostgreSQL
echo "  PostgreSQL 连接测试:"
if docker compose ps | grep -q "gpt-load-postgres.*Up"; then
    if docker compose exec postgres pg_isready -U postgres -d gpt-load >/dev/null 2>&1; then
        echo "    ✓ PostgreSQL 连接正常"

        # 获取数据库信息
        db_info=$(docker compose exec postgres psql -U postgres -d gpt-load -t -c "SELECT version();" 2>/dev/null | head -1 | xargs || echo "无法获取版本信息")
        echo "    数据库版本: $db_info"
    else
        echo "    ❌ PostgreSQL 连接失败"
    fi
else
    echo "    ❌ PostgreSQL 服务未运行"
fi

echo ""

# 检查 Redis
echo "  Redis 连接测试:"
if docker compose ps | grep -q "gpt-load-redis.*Up"; then
    if docker compose exec redis redis-cli ping >/dev/null 2>&1; then
        echo "    ✓ Redis 连接正常"

        # 获取 Redis 信息
        redis_info=$(docker compose exec redis redis-cli info server | grep "redis_version" | cut -d':' -f2 | tr -d '\r' || echo "无法获取版本信息")
        echo "    Redis 版本: $redis_info"

        # 获取键数量
        key_count=$(docker compose exec redis redis-cli dbsize 2>/dev/null || echo "0")
        echo "    数据键数量: $key_count"
    else
        echo "    ❌ Redis 连接失败"
    fi
else
    echo "    ❌ Redis 服务未运行"
fi

echo ""

# 显示当前配置
echo "📋 当前配置信息:"
echo "  数据库: PostgreSQL + Redis 双数据库架构"
echo "  代理: 已禁用 (如需使用请在 .env 中配置)"
echo "  管理界面: http://localhost:3001"
echo "  认证密钥: $(grep AUTH_KEY .env | cut -d'=' -f2 || echo '未设置')"

echo ""

# 提供建议
echo "💡 使用建议:"
echo "  1. 访问管理界面: http://localhost:3001"
echo "  2. 配置 API 分组和密钥"
echo "  3. 如需使用代理，请在 .env 文件中配置 HTTP_PROXY 和 HTTPS_PROXY"
echo "  4. 查看服务日志: docker compose logs -f gpt-load"
echo "  5. 重启服务: docker compose restart"

echo ""
echo "✅ GPT-Load 服务测试完成"
